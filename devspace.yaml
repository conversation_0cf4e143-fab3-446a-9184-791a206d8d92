version: v2beta1
name: fulfilment-core-service
localRegistry:
  enabled: true

# This is a list of `pipelines` that DevSpace can execute (you can define your own)
pipelines:
  # This is the pipeline for the main command: `devspace dev` (or `devspace run-pipeline dev`)
  dev:
    run: |-
      run_dependencies --all       # 1. Deploy any projects this project needs (see "dependencies")
      ensure_pull_secrets --all    # 2. Ensure pull secrets
      create_deployments --all     # 3. Deploy He<PERSON> charts and manifests specfied as "deployments"
      start_dev fulfilment-core-service                # 4. Start dev mode "fulfilment-core-service" (see "dev" section)
  # You can run this pipeline via `devspace deploy` (or `devspace run-pipeline deploy`)
  deploy:
    run: |-
      mvn clean package -Dmaven.test.skip=true          # 0. Build the project
      run_dependencies --all                            # 1. Deploy any projects this project needs (see "dependencies")
      ensure_pull_secrets --all                         # 2. Ensure pull secrets
      build_images --all -t $(uuidgen)                  # 3. Build, tag image with random uuid and push all images (see "images")
      create_deployments --all                          # 4. Deploy Helm charts and manifests specfied as "deployments"

# This is a list of `images` that DevSpace can build for this project
# We recommend to skip image building during development (devspace dev) as much as possible
images:
  fulfilment-core-service:
    image: localregistry/${DEVSPACE_NAME}
    dockerfile: ./infra/k8s/Dockerfile
    context: .

# This is a list of `deployments` that DevSpace can create for this project
deployments:
  fulfilment-core-service:
    # This deployment uses `helm` but you can also define `kubectl` deployments or kustomizations
    helm:
      displayOutput: true
      # We are deploying this project with the Helm chart you provided
      chart:
        name: component-chart
        repo: https://charts.devspace.sh
      # Under `values` we can define the values for this Helm chart used during `helm install/upgrade`
      # You may also use `valuesFiles` to load values from files, e.g. valuesFiles: ["values.yaml"]
      values:
        containers:
          - image: localregistry/${DEVSPACE_NAME}
            env:
              - name: FS_DB_URL
                value: "****************************"
              - name: FS_DB_USERNAME
                value: "fs"
              - name: FS_DB_PASSWORD
                value: "fs"
              - name: FS_DB_MIGRATIONS_PASSWORD
                value: "fs"
              - name: FS_DB_MIGRATIONS_USERNAME
                value: "fs"
              - name: KAFKA_BROKER_LIST
                value: "bse-kafka-brokers.kafka"
              - name: SPRING_PROFILES_ACTIVE
                value: "local"
        service:
          ports:
            - port: 8080


# This is a list of `dev` containers that are based on the containers created by your deployments
dev:
  fulfilment-core-service:
    # Search for the container that runs this image
    imageSelector: localregistry/${DEVSPACE_NAME}
    # Replace the container image with this dev-optimized image (allows to skip image building during development)
    devImage: ghcr.io/loft-sh/devspace-containers/java-maven:3-openjdk-17-slim
    # Sync files between the local filesystem and the development container
    sync:
      - path: ./
      - path: "~/.m2/settings.xml:/root/.m2/settings.xml"
        file: true
    # Open a terminal and use the following command to start it
    terminal:
      command: ./devspace_start.sh
    # Inject a lightweight SSH server into the container (so your IDE can connect to the remote dev env)
    ssh:
      enabled: true
    # Make the following commands from my local machine available inside the dev container
    proxyCommands:
      - command: devspace
      - command: kubectl
      - command: helm
      - gitCredentials: true
    # Forward the following ports to be able to access your fulfilment-core-service location via localhost
    ports:
      - port: "8080"
      - port: "5005"
    # Open the following URLs once they return an HTTP status code other than 502 or 503
    open:
      - url: http://localhost:8080
    env:
      - name: DEVSPACE_NAME
        value: ${DEVSPACE_NAME}


# Use the `commands` section to define repeatable dev workflows for this project 
commands:
  start:
    command: |
      devspace enter -- bash -c '
        mvn spring-boot:run \
          -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
      '

vars:
  DEVSPACE_NAME:
    source: env
    default: DEVSPACE_NAME


# Define dependencies to other projects with a devspace.yaml
# dependencies:
#   api:
#     git: https://...  # Git-based dependencies
#     tag: v1.0.0
#   ui:
#     path: ./ui        # Path-based dependencies (for monorepos)
