package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
@Slf4j
@Getter
public class OrderStatusUpdatedTestConsumer implements Consumer<@Valid OrderStatusUpdated> {

    public static final CopyOnWriteArrayList<OrderStatusUpdated> ORDER_STATUS_UPDATED_LIST =
        new CopyOnWriteArrayList<>();

    @Override
    public void accept(@Valid OrderStatusUpdated orderStatusUpdated) {
        log.info("Consuming OrderStatusUpdated: payload={}", orderStatusUpdated);
        ORDER_STATUS_UPDATED_LIST.add(orderStatusUpdated);
        log.info("OrderStatusUpdated consumed successfully: payload={}", orderStatusUpdated);
    }

}
