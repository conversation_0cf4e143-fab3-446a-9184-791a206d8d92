package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
@Slf4j
@Getter
public class OrderCancelledTestConsumer implements Consumer<OrderCancelled> {

    public static final CopyOnWriteArrayList<OrderCancelled> MESSAGES = new CopyOnWriteArrayList<>();

    @Override
    public void accept(OrderCancelled orderCancelled) {
        log.info("Consuming OrderCancelled: payload={}", orderCancelled);
        MESSAGES.add(orderCancelled);
        log.info("OrderCancelled consumed successfully: payload={}", orderCancelled);
    }

}
