package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderReturnedInStoreQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.ReturnItem;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.UpsellItem;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderReturnedInStoreConsumerIntegrationTest extends AbstractIntegrationTest {

    public static final long RETURN_ID = 147273L;
    public static final double GROSS_PRICE = 249.95;

    @Value("${spring.cloud.stream.bindings.orderReturnedInStoreConsumer-in-0.destination}")
    private String orderReturnedInStoreTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderReturnedInStoreQueueConsumer orderReturnedInStoreQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Test
    void shouldConsumerOrderReturnedInStore() {
        // arrange
        String warehouse = "INGRAM_MICRO";
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();

        var orderLine = OrderLine.builder()
            .ean("1234567890123")
            .ecomId(UUID.randomUUID())
            .originalQty(1)
            .orderLineQtyStatus(List.of(OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                .orderFulfillmentPart(OrderFulfillmentPart.builder()
                    .fulfillmentNode(warehouse)
                    .build())
                .build()))
            .build();

        order.setOrderLines(List.of(orderLine));
        orderService.save(order);

        var orderReturnedInStore = new OrderReturnedInStore()
            .withOrderId(order.getOrderId())
            .withReturnId(RETURN_ID)
            .withTotalUpsellDiscountAmount(BigDecimal.valueOf(0L))
            .withReturnItems(List.of(
                new ReturnItem()
                    .withGrossPrice(BigDecimal.valueOf(GROSS_PRICE))
                    .withDiscountAmount(BigDecimal.valueOf(0L))
                    .withOrderLine(
                        new com.bestseller.generated.interfacecontracts.kafkamessages.pojos
                            .orderReturnedInStore.OrderLine()
                            .withEan(orderLine.getEan())
                            .withQuantity(1))
            ));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderReturnedInStoreTopic, orderReturnedInStore));

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.POS_RETURNED_IN_STORE);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(orderLine.getEcomId())
                        .states(List.of("POS_RETURNED_IN_STORE"))
                        .build()
                )
            )
            .build());

        // assert
        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus()
                .equals(OrderState.POS_RETURNED_IN_STORE)
                && orderStatusUpdateInfoRepository.findOrderStatusUpdateInfoByOrder(order) != null);

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @Test
    void consumingOrderReturnedInStore_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var ean = "5715423786800";
        var orderReturnedInStore = new OrderReturnedInStore()
            .withOrderId(orderId)
            .withReturnId(1L)
            .withUpsellItems(List.of(
                new UpsellItem()
                    .withEan(ean)
                    .withProductName("ProductName")
                    .withGrossPrice(BigDecimal.valueOf(GROSS_PRICE))
                    .withDiscountAmount(BigDecimal.valueOf(GROSS_PRICE))
            ))
            .withReturnItems(List.of(
                new ReturnItem()
                    .withGrossPrice(BigDecimal.valueOf(GROSS_PRICE))
                    .withDiscountAmount(BigDecimal.valueOf(GROSS_PRICE))
                    .withOrderLine(
                        new com.bestseller.generated.interfacecontracts.kafkamessages.pojos
                            .orderReturnedInStore.OrderLine()
                            .withEan("ean1")
                            .withQuantity(1))
            ))
            .withTotalUpsellDiscountAmount(BigDecimal.valueOf(GROSS_PRICE));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderReturnedInStoreTopic, orderReturnedInStore));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderReturnedInStore>> tasks = databaseQueueTaskDao.getTasks(
                orderReturnedInStoreQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderReturnedInStore.class,
                orderReturnedInStore.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderReturnedInStore> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderReturnedInStore);
        });
    }
}
