package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.messaging.consumer.OrderStatusUpdatedTestConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

public class OrderStatusUpdatedKafkaMessageProducerTest extends AbstractIntegrationTest {

    @Autowired
    private OrderStatusUpdatedKafkaMessageProducer orderStatusUpdatedKafkaMessageProducer;

    @Autowired
    private OrderStatusUpdatedTestConsumer orderStatusUpdatedTestConsumer;

    @Test
    void shouldProduceMessage() {
        // arrange
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(UUID.randomUUID().toString());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.EXPORTED);
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload
                .builder()
                .orderLines(Collections.emptyList())
                .build());

        // act
        orderStatusUpdatedKafkaMessageProducer.produce(orderStatusUpdated);

        // assert
        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator()
                .contains(orderStatusUpdated);
        });
    }
}
