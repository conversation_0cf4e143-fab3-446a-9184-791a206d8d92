package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineAcknowledgedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderLineAcknowledgedConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderLineAcknowledgedConsumer-in-0.destination}")
    private String orderLineAcknowledgedTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderLineAcknowledgedQueueConsumer orderLineAcknowledgedQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Test
    void shouldConsumeOrderLineAcknowledged() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean("1707921066")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .orderLines(List.of(orderLine))
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.ON)
            .build();
        orderService.save(order);

        var orderLineAcknowledged = new OrderLineAcknowledged();
        orderLineAcknowledged.setOrderId(order.getOrderId());
        orderLineAcknowledged.setEan(orderLine.getEan());
        orderLineAcknowledged.setQuantity(orderLine.getOriginalQty());
        orderLineAcknowledged.setAcknowledgementDate(ZonedDateTime.now());

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledged));

        // assert
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.ACKNOWLEDGED);
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(List.of("PROCESSING", "PROCESSING"))
                            .build()
                    )
                )
                .build()
        );

        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus().equals(OrderState.PROCESSING));

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @Test
    void consumingOrderLineAcknowledged_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var orderLineAcknowledged = new OrderLineAcknowledged();
        orderLineAcknowledged.setOrderId(orderId);
        orderLineAcknowledged.setEan("5715509089740");
        orderLineAcknowledged.setQuantity(2);
        orderLineAcknowledged.setAcknowledgementDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledged));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderLineAcknowledged>> tasks = databaseQueueTaskDao.getTasks(
                orderLineAcknowledgedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderLineAcknowledged.class,
                orderLineAcknowledged.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderLineAcknowledged> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderLineAcknowledged);
        });
    }
}
