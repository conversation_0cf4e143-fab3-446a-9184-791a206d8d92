package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.messaging.consumer.OrderStatusUpdateTradeByteTestConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderStatusUpdateTradebyteKafkaMessageProducerTest extends AbstractIntegrationTest {

    @Autowired
    private OrderStatusUpdateTradebyteKafkaMessageProducer orderStatusUpdateTradeByteProducer;

    @Autowired
    private OrderStatusUpdateTradeByteTestConsumer orderStatusUpdateTradeByteTestConsumer;

    @Test
    void shouldProduceMessage() {
        // arrange
        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte();
        statusUpdateTradeByte.setOrderId(UUID.randomUUID().toString());
        statusUpdateTradeByte.setSku("1234567890123");
        statusUpdateTradeByte.setStatus(OrderState.RETURNED.getIdentifier());

        // act
        orderStatusUpdateTradeByteProducer.produce(statusUpdateTradeByte);

        // assert
        await().untilAsserted(() -> {
            var orderStatusUpdateTradebytes = OrderStatusUpdateTradeByteTestConsumer.ORDER_STATUS_UPDATE_TRADEBYTES;
            assertThat(orderStatusUpdateTradebytes)
                .usingRecursiveFieldByFieldElementComparator()
                .contains(statusUpdateTradeByte);
        });
    }
}
