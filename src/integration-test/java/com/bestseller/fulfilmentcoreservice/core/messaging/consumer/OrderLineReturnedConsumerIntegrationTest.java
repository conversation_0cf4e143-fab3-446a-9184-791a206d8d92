package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.TradebyteOrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineReturnedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderLineReturnedConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderLineReturnedConsumer-in-0.destination}")
    private String orderLineReturnedTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Autowired
    private OrderLineReturnedQueueConsumer orderLineReturnedQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Test
    void shouldConsumerOrderLineReturned() {
        // arrange
        String warehouse = "INGRAM_MICRO";
        ZonedDateTime now = ZonedDateTime.now();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.TRADEBYTE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();
        var orderLine = OrderLine.builder()
            .ean("1234567890123")
            .ecomId(UUID.randomUUID())
            .originalQty(1)
            .orderLineQtyStatus(List.of(OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                .orderFulfillmentPart(OrderFulfillmentPart.builder()
                    .fulfillmentNode(warehouse)
                    .build())
                .build()))
            .build();
        order.setOrderLines(List.of(orderLine));
        orderService.save(order);

        var orderLineReturned = new OrderLineReturned()
            .withOrderId(order.getOrderId())
            .withEan(orderLine.getEan())
            .withQuantity(orderLine.getOriginalQty())
            .withLineNumber(orderLine.getLineNumber())
            .withReturnReason(CustomerReturnReason.LOST_ITEM_21.name())
            .withReturnType(ReturnType.CUSTOMER_RETURN.name())
            .withEffectiveDate(now)
            .withWarehouse(warehouse);

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineReturnedTopic, orderLineReturned));

        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte();
        statusUpdateTradeByte.setOrderId(order.getOrderId());
        statusUpdateTradeByte.setSku("1234567890123");
        statusUpdateTradeByte.setStatus(OrderState.RETURNED.getIdentifier());
        statusUpdateTradeByte.setTbOrderItemId(0);

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.RETURNED);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(orderLine.getEcomId())
                        .states(List.of("RETURNED"))
                        .build()
                )
            )
            .build());

        // assert
        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus().equals(OrderState.RETURNED)
                && orderStatusUpdateInfoRepository.findOrderStatusUpdateInfoByOrder(order) != null);

        assertOrderStatusUpdatedKafkaMessage(orderStatusUpdated);
        assertOrderStatusUpdateTradebyteKafkaMessages(statusUpdateTradeByte);
    }

    @Test
    void consumingOrderLineReturned_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var orderLineReturned = new OrderLineReturned();
        orderLineReturned.setOrderId(orderId);
        orderLineReturned.setEan("5715509089740");
        orderLineReturned.setQuantity(2);
        orderLineReturned.setLineNumber(1);
        orderLineReturned.setReturnReason(CustomerReturnReason.LOST_ITEM_21.name());
        orderLineReturned.setReturnType(ReturnType.CUSTOMER_RETURN.name());
        orderLineReturned.setEffectiveDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS));
        orderLineReturned.setWarehouse("INGRAM_MICRO");

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineReturnedTopic, orderLineReturned));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderLineReturned>> tasks = databaseQueueTaskDao.getTasks(
                orderLineReturnedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderLineReturned.class,
                orderLineReturned.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderLineReturned> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderLineReturned);
        });
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void consumeOrderLineReturned_givenOrderStatusUpdatedTradeByte_updateMultipleQuantitiesReturnedSeparately() {
        // arrange
        var warehouse = "INGRAM_MICRO";
        var now = ZonedDateTime.now();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(now)
            .platform(Platform.TRADEBYTE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();

        var tbOrderItemId = 143183166;
        var tbOrderItemId2 = 143183164;
        var tbOrderItemId3 = 143183162;
        var orderLine = OrderLine.builder()
            .ean("5715509089740")
            .ecomId(UUID.randomUUID())
            .originalQty(3)
            .orderLineQtyStatus(
                List.of(
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.DISPATCHED)
                        .tradebyteOrderLineQtyStatus(
                            TradebyteOrderLineQtyStatus.builder()
                                .originalLineNumber(tbOrderItemId)
                                .build()
                        )
                        .build(),
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.DISPATCHED)
                        .tradebyteOrderLineQtyStatus(
                            TradebyteOrderLineQtyStatus.builder()
                                .originalLineNumber(tbOrderItemId2)
                                .build()
                        )
                        .build(),
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.DISPATCHED)
                        .tradebyteOrderLineQtyStatus(
                            TradebyteOrderLineQtyStatus.builder()
                                .originalLineNumber(tbOrderItemId3)
                                .build()
                        )
                        .build()
                )
            )
            .build();
        order.setOrderLines(List.of(orderLine));
        orderService.save(order);

        // return 1 quantity
        var orderLineReturned = new OrderLineReturned()
            .withOrderId(order.getOrderId())
            .withEan(orderLine.getEan())
            .withQuantity(1)
            .withLineNumber(orderLine.getLineNumber())
            .withReturnReason(CustomerReturnReason.LOST_ITEM_21.name())
            .withReturnType(ReturnType.CUSTOMER_RETURN.name())
            .withEffectiveDate(now)
            .withWarehouse(warehouse);

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineReturnedTopic, orderLineReturned));

        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte()
            .withOrderId(order.getOrderId())
            .withSku(orderLine.getEan())
            .withStatus(OrderState.RETURNED.getIdentifier())
            .withTbOrderItemId(tbOrderItemId);

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.RETURNED);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(orderLine.getEcomId())
                        .states(List.of("RETURNED", "DISPATCHED", "DISPATCHED"))
                        .build()
                )
            )
            .build());

        // assert
        assertOrderStatusUpdatedKafkaMessage(orderStatusUpdated);
        assertOrderStatusUpdateTradebyteKafkaMessages(statusUpdateTradeByte);

        // return 2 remaining quantities
        var orderLineReturned2 = new OrderLineReturned()
            .withOrderId(order.getOrderId())
            .withEan(orderLine.getEan())
            .withQuantity(2)
            .withLineNumber(orderLine.getLineNumber())
            .withReturnReason(CustomerReturnReason.LOST_ITEM_21.name())
            .withReturnType(ReturnType.CUSTOMER_RETURN.name())
            .withEffectiveDate(now)
            .withWarehouse(warehouse);

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineReturnedTopic, orderLineReturned2));

        var statusUpdateTradeByte2 = new OrderStatusUpdateTradebyte()
            .withOrderId(order.getOrderId())
            .withSku(orderLine.getEan())
            .withStatus(OrderState.RETURNED.getIdentifier())
            .withTbOrderItemId(tbOrderItemId2);
        var statusUpdateTradeByte3 = new OrderStatusUpdateTradebyte()
            .withOrderId(order.getOrderId())
            .withSku(orderLine.getEan())
            .withStatus(OrderState.RETURNED.getIdentifier())
            .withTbOrderItemId(tbOrderItemId3);

        var orderStatusUpdated2 = new OrderStatusUpdated();
        orderStatusUpdated2.setOrderId(order.getOrderId());
        orderStatusUpdated2.setType(OrderStatusUpdated.Type.RETURNED);
        orderStatusUpdated2.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(orderLine.getEcomId())
                        .states(List.of("RETURNED", "RETURNED", "RETURNED"))
                        .build()
                )
            )
            .build());

        // assert
        assertOrderStatusUpdatedKafkaMessage(orderStatusUpdated2);
        assertOrderStatusUpdateTradebyteKafkaMessages(statusUpdateTradeByte2, statusUpdateTradeByte3);
    }

    private static void assertOrderStatusUpdateTradebyteKafkaMessages(
        OrderStatusUpdateTradebyte... statusUpdateTradeByteMessages
    ) {
        await().untilAsserted(() -> {
            var orderStatusUpdateTradeBytes = OrderStatusUpdateTradeByteTestConsumer.ORDER_STATUS_UPDATE_TRADEBYTES;
            assertThat(orderStatusUpdateTradeBytes)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("dateCreated")
                        .build()
                )
                .contains(statusUpdateTradeByteMessages);
        });
    }

    private static void assertOrderStatusUpdatedKafkaMessage(OrderStatusUpdated orderStatusUpdated) {
        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }
}
