package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderPartsCancelledQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertNull;

class OrderPartsCancelledConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderPartsCancelledConsumer-in-0.destination}")
    private String orderPartsCancelledTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderPartsCancelledQueueConsumer orderPartsCancelledQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Test
    void shouldConsumeOrderPartsCancelled() {
        // arrange
        String orderId = UUID.randomUUID().toString();
        var order = Order.builder()
            .orderId(orderId)
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.TRADEBYTE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();

        var orderLine = OrderLine.builder()
            .ean("*************")
            .ecomId(UUID.randomUUID())
            .lineNumber(1)
            .orderLineQtyStatus(List.of(
                OrderLineQtyStatus.builder()
                    .orderFulfillmentPart(OrderFulfillmentPart
                        .builder()
                        .fulfillmentNode("fulfillmentNode")
                        .holdFromRouting(true)
                        .build())
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                    .orderState(OrderState.PROCESSING)
                    .build()))
            .originalQty(1)
            .openQty(1)
            .build();

        order.setOrderLines(List.of(orderLine));
        orderService.save(order);

        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse("warehouse")
            .withCancellationDate(ZonedDateTime.now())
            .withOrderLines(List.of(
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                    .withEan("*************")
                    .withLineNumber(1)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(1)));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderPartsCancelledTopic, orderPartsCancelled));

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.CANCELLED);
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(List.of("CANCELLED"))
                            .build()
                    )
                )
                .build()
        );

        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte();
        statusUpdateTradeByte.setOrderId(order.getOrderId());
        statusUpdateTradeByte.setSku("*************");
        statusUpdateTradeByte.setStatus(OrderState.CANCELLED.getIdentifier());
        statusUpdateTradeByte.setTbOrderItemId(1);

        // assert
        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus().equals(OrderState.CANCELLED));

        await().untilAsserted(() -> {
            List<OrderStatusUpdateInfo> statusUpdateInfos = orderStatusUpdateInfoRepository.findAllByOrder(order);
            assertThat(statusUpdateInfos.size()).isEqualTo(1);
            assertThat(statusUpdateInfos.getFirst().getCancelReason()).isEqualTo(CancelReason.STORE_REJECTION);
            assertThat(statusUpdateInfos.getFirst().getCancellationDate()).isEqualTo(
                orderPartsCancelled.getCancellationDate().toLocalDate());
        });

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;

            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
        await().untilAsserted(() -> {
            var orderStatusUpdateTradeBytes = OrderStatusUpdateTradeByteTestConsumer.ORDER_STATUS_UPDATE_TRADEBYTES;
            assertThat(orderStatusUpdateTradeBytes)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("dateCreated")
                        .build()
                )
                .contains(statusUpdateTradeByte);
        });
    }

    @Test
    void consumingOrderPartsCancelled_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var orderLine =
            new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                .withEan("*************")
                .withLineNumber(1)
                .withCancelReason(CancelReason.MANUAL_CANCELLATION.getReason())
                .withQuantity(1);

        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse("warehouse")
            .withCancellationDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .withOrderLines(List.of(orderLine));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderPartsCancelledTopic, orderPartsCancelled));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderPartsCancelled>> tasks = databaseQueueTaskDao.getTasks(
                orderPartsCancelledQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderPartsCancelled.class,
                orderPartsCancelled.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderPartsCancelled> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderPartsCancelled);
        });
    }

    @Test
    @SuppressWarnings("LineLength")
    void givenOrderPartsCancelledWithOneQuantity_whenEanHasMultipleOriginalQuantity_updateJustOneOrderLineQuantityStatus() {
        // arrange
        String orderId = UUID.randomUUID().toString();
        var order = Order.builder()
            .orderId(orderId)
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.TRADEBYTE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();

        var orderLine1 = OrderLine.builder()
            .ean("*************")
            .ecomId(UUID.randomUUID())
            .lineNumber(1)
            .orderLineQtyStatus(List.of(
                OrderLineQtyStatus.builder()
                    .orderFulfillmentPart(OrderFulfillmentPart
                        .builder()
                        .fulfillmentNode("fulfillmentNode")
                        .holdFromRouting(true)
                        .build())
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                    .orderState(OrderState.PROCESSING)
                    .build()))
            .originalQty(2)
            .openQty(2)
            .build();

        var orderLine2 = OrderLine.builder()
            .ean("*************")
            .ecomId(UUID.randomUUID())
            .lineNumber(2)
            .orderLineQtyStatus(List.of(
                OrderLineQtyStatus.builder()
                    .orderFulfillmentPart(OrderFulfillmentPart
                        .builder()
                        .fulfillmentNode("fulfillmentNode")
                        .holdFromRouting(true)
                        .build())
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                    .orderState(OrderState.PROCESSING)
                    .build()))
            .originalQty(2)
            .openQty(2)
            .build();

        order.setOrderLines(List.of(orderLine1, orderLine2));
        orderService.save(order);

        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse("warehouse")
            .withCancellationDate(ZonedDateTime.now())
            .withOrderLines(List.of(
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                    .withEan("*************")
                    .withLineNumber(1)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(1)));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderPartsCancelledTopic, orderPartsCancelled));

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.CANCELLED);
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine
                            .builder()
                            .id(orderLine1.getEcomId())
                            .states(List.of("CANCELLED"))
                            .build()
                    )
                )
                .build()
        );

        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte();
        statusUpdateTradeByte.setOrderId(order.getOrderId());
        statusUpdateTradeByte.setSku("*************");
        statusUpdateTradeByte.setStatus(OrderState.CANCELLED.getIdentifier());
        statusUpdateTradeByte.setTbOrderItemId(1);

        // assert
        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus().equals(OrderState.CANCELLED));

        await().untilAsserted(() -> {
            List<OrderStatusUpdateInfo> statusUpdateInfos = orderStatusUpdateInfoRepository.findAllByOrder(order);
            assertThat(statusUpdateInfos.size()).isEqualTo(2);
            assertThat(statusUpdateInfos.getFirst().getCancelReason()).isEqualTo(
                CancelReason.STORE_REJECTION);
            assertThat(statusUpdateInfos.getFirst().getCancellationDate()).isEqualTo(
                orderPartsCancelled.getCancellationDate().toLocalDate());
            assertNull(statusUpdateInfos.get(1).getCancelReason());
            assertNull(statusUpdateInfos.get(1).getCancellationDate());
        });

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;

            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
        await().untilAsserted(() -> {
            var orderStatusUpdateTradeBytes = OrderStatusUpdateTradeByteTestConsumer.ORDER_STATUS_UPDATE_TRADEBYTES;
            assertThat(orderStatusUpdateTradeBytes)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("dateCreated")
                        .build()
                )
                .contains(statusUpdateTradeByte);
        });
    }
}
