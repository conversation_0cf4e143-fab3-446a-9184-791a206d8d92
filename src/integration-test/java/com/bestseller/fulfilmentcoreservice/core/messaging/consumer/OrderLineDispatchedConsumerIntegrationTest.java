package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineDispatchedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderLineDispatchedConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderLineDispatchedConsumer-in-0.destination}")
    private String orderLineDispatchedTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderLineDispatchedQueueConsumer orderLineDispatchedQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Test
    void shouldConsumeOrderLineDispatched() {
        // arrange
        String warehouse = "INGRAM_MICRO_NL";

        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.TRADEBYTE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();

        var orderLine = OrderLine.builder()
            .ean("*************")
            .ecomId(UUID.randomUUID())
            .orderLineQtyStatus(List.of(
                OrderLineQtyStatus.builder()
                    .orderFulfillmentPart(OrderFulfillmentPart
                        .builder()
                        .fulfillmentNode(warehouse)
                        .holdFromRouting(true)
                        .build())
                    .orderState(OrderState.PROCESSING)
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder().order(order).build())
                    .build()))
            .originalQty(1)
            .openQty(1)
            .build();
        order.setOrderLines(List.of(orderLine));
        orderService.save(order);

        var orderLineDispatched = new OrderLineDispatched()
            .withOrderId(order.getOrderId())
            .withEan(orderLine.getEan())
            .withQuantity(orderLine.getOriginalQty())
            .withCarrierName("carrier")
            .withDispatchDate(ZonedDateTime.now())
            .withTrackingNumber("trackingNumber")
            .withReturnShipmentId("returnShipmentId")
            .withWarehouse(warehouse)
            .withOrangePrinted(ZonedDateTime.now());

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineDispatchedTopic, orderLineDispatched));

        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.DISPATCHED);
        orderStatusUpdated.setTimestamp(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS));
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(List.of("DISPATCHED"))
                            .returnShipmentId(orderLineDispatched.getReturnShipmentId())
                            .trackingNumber(orderLineDispatched.getTrackingNumber())
                            .build()
                    )
                )
                .build()
        );

        var statusUpdateTradeByte = new OrderStatusUpdateTradebyte();
        statusUpdateTradeByte.setOrderId(order.getOrderId());
        statusUpdateTradeByte.setSku("*************");
        statusUpdateTradeByte.setIdCode("trackingNumber");
        statusUpdateTradeByte.setIdCodeReturnProposal("returnShipmentId");
        statusUpdateTradeByte.setStatus(OrderState.DISPATCHED.getIdentifier());
        statusUpdateTradeByte.setTbOrderItemId(0);

        // assert
        await().until(() ->
            orderService.findById(order.getOrderId()).getMaxStatus().equals(OrderState.DISPATCHED)
                && orderStatusUpdateInfoRepository.findOrderStatusUpdateInfoByOrder(order) != null);

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;

            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });

        await().untilAsserted(() -> {
            var orderStatusUpdateTradeBytes = OrderStatusUpdateTradeByteTestConsumer.ORDER_STATUS_UPDATE_TRADEBYTES;
            assertThat(orderStatusUpdateTradeBytes)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("dateCreated")
                        .build()
                )
                .contains(statusUpdateTradeByte);
        });
    }

    @Test
    void consumingOrderLineDispatched_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var orderLineDispatched = new OrderLineDispatched()
            .withOrderId(orderId)
            .withEan("5715509089740")
            .withDispatchDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .withOrangePrinted(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .withQuantity(1)
            .withTrackingNumber(UUID.randomUUID().toString())
            .withCarrierName(UUID.randomUUID().toString());

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineDispatchedTopic, orderLineDispatched));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderLineDispatched>> tasks = databaseQueueTaskDao.getTasks(
                orderLineDispatchedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderLineDispatched.class,
                orderLineDispatched.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderLineDispatched> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderLineDispatched);
        });
    }
}
