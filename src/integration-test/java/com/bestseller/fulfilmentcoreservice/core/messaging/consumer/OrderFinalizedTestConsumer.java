package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
@Getter
@Slf4j
public class OrderFinalizedTestConsumer implements Consumer<@Valid OrderFinalized> {
    public static final CopyOnWriteArrayList<OrderFinalized> MESSAGES = new CopyOnWriteArrayList<>();

    @Override
    public void accept(@Valid OrderFinalized orderFinalized) {
        log.info("Consuming OrderFinalized: payload={}", orderFinalized);
        MESSAGES.add(orderFinalized);
        log.info("OrderFinalized consumed successfully: payload={}", orderFinalized);
    }
}
