package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.PartnerChannelGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.PartnerChannelRepository;
import com.bestseller.fulfilmentcoreservice.queue.UpdatePaymentStatusQueueConsumer;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

class PaymentStatusUpdatedConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("classpath:paymentStatusUpdated/demandware_payment_status_updated.json")
    private Resource paymentStatusUpdatedJson;

    @Value("${spring.cloud.stream.bindings.paymentStatusUpdatedConsumer-in-0.destination}")
    private String paymentStatusUpdatedTopic;

    @Autowired
    private PartnerChannelRepository partnerChannelRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Autowired
    private UpdatePaymentStatusQueueConsumer paymentStatusUpdatedQueueConsumer;

    @Test
    void shouldConsumerPaymentStatusUpdatedKafkaMessage() {
        // arrange
        var orderId = "TO0469733";
        var order = Order.builder()
            .orderId(orderId)
            .currency("EUR")
            .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("1707921066")
                    .ecomId(UUID.randomUUID())
                    .originalQty(1)
                    .build()
            ))
            .customer(
                Customer.builder()
                    .customerId(UUID.randomUUID().toString())
                    .email("<EMAIL>")
                    .build()
            )
            .build();
        var partnerChannel = PartnerChannelGenerator.createPartnerChannelGenerator();
        var orderPartnerChannel = partnerChannelRepository.save(partnerChannel);
        order.setPartnerChannel(orderPartnerChannel);
        orderService.save(order);

        var paymentStatusUpdated = getObjectFromResources(paymentStatusUpdatedJson, PaymentStatusUpdated.class);
        paymentStatusUpdated.setOrderId(orderId);

        // act
        getKafkaProducer().send(new ProducerRecord<>(paymentStatusUpdatedTopic, paymentStatusUpdated));

        await().until(() ->
            orderService.findById(paymentStatusUpdated.getOrderId()).isOrderPaymentAuthorised());

        // assert
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(orderId);
        orderStatusUpdated.setType(OrderStatusUpdated.Type.ROUTING);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(order.getOrderLines().get(0).getEcomId())
                        .states(List.of("ROUTING"))
                        .build()
                )
            )
            .build());

        await().until(() ->
            orderService.findById(paymentStatusUpdated.getOrderId())
                .getMaxStatus().equals(OrderState.ROUTING));

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @Test
    void given_cancelPaymentStatusUpdated() {
        // arrange
        var orderId = orderIdGenerator.generate();
        var order = Order.builder()
            .orderId(orderId)
            .currency("EUR")
            .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("1707921066")
                    .ecomId(UUID.randomUUID())
                    .originalQty(1)
                    .build()
            ))
            .customer(
                Customer.builder()
                    .customerId(UUID.randomUUID().toString())
                    .email("<EMAIL>")
                    .build()
            )
            .build();
        var partnerChannel = PartnerChannelGenerator.createPartnerChannelGenerator();
        var orderPartnerChannel = partnerChannelRepository.save(partnerChannel);
        order.setPartnerChannel(orderPartnerChannel);
        orderService.save(order);

        // act
        getKafkaProducer().send(new ProducerRecord<>(paymentStatusUpdatedTopic,
            new PaymentStatusUpdated().withOrderId(orderId)
                .withTimestamp(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
                .withPaymentState(PaymentStatusUpdated.PaymentState.CANCELLED)));

        // assert
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(orderId);
        orderStatusUpdated.setType(OrderStatusUpdated.Type.CANCELLED);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(order.getOrderLines().get(0).getEcomId())
                        .states(List.of("CANCELLED"))
                        .build()
                )
            )
            .build());

        await().until(() ->
            orderService.findById(orderId).getMaxStatus().equals(OrderState.CANCELLED));

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @Test
    void cancelPaymentStatusUpdated_givenOrderWithVirtualOrderLine_AllLinesShouldBeCancelled() {
        // arrange
        var orderId = orderIdGenerator.generate();
        var order = Order.builder()
            .orderId(orderId)
            .currency("EUR")
            .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("1707921066")
                    .ecomId(UUID.randomUUID())
                    .originalQty(1)
                    .build(),
                OrderLine.builder()
                    .ean("1707921067")
                    .ecomId(UUID.randomUUID())
                    .originalQty(1)
                    .virtualProduct(true)
                    .build()
            ))
            .customer(
                Customer.builder()
                    .customerId(UUID.randomUUID().toString())
                    .email("<EMAIL>")
                    .build()
            )
            .build();
        var partnerChannel = PartnerChannelGenerator.createPartnerChannelGenerator();
        var orderPartnerChannel = partnerChannelRepository.save(partnerChannel);
        order.setPartnerChannel(orderPartnerChannel);
        orderService.save(order);


        // act
        getKafkaProducer().send(new ProducerRecord<>(paymentStatusUpdatedTopic,
            new PaymentStatusUpdated().withOrderId(orderId)
                .withTimestamp(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
                .withPaymentState(PaymentStatusUpdated.PaymentState.CANCELLED)));

        // assert
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(orderId);
        orderStatusUpdated.setType(OrderStatusUpdated.Type.CANCELLED);
        orderStatusUpdated.setPayload(DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(order.getOrderLines().get(0).getEcomId())
                        .states(List.of("CANCELLED"))
                        .build(),
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(order.getOrderLines().get(1).getEcomId())
                        .states(List.of("CANCELLED"))
                        .build()
                )
            )
            .build());

        await().until(() ->
            orderService.findById(orderId).getMaxStatus().equals(OrderState.CANCELLED));

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @ParameterizedTest
    @EnumSource(value = PaymentStatusUpdated.PaymentState.class, names = {"AUTHORISED", "CANCELLED"})
    void consumePaymentStatusUpdated_givenUnknownOrderId_enqueueDatabaseQueueTask(
        PaymentStatusUpdated.PaymentState paymentState) {
        // arrange
        var paymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(UUID.randomUUID().toString())
            .withTimestamp(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .withPaymentState(paymentState);

        // act
        getKafkaProducer().send(new ProducerRecord<>(paymentStatusUpdatedTopic, paymentStatusUpdated));

        // assert
        await().untilAsserted(() -> {
            List<Task<PaymentStatusUpdated>> tasks = databaseQueueTaskDao.getTasks(
                paymentStatusUpdatedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                PaymentStatusUpdated.class,
                paymentStatusUpdated.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<PaymentStatusUpdated> paymentStatusUpdatedList = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(paymentStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(paymentStatusUpdated);
        });
    }

}
