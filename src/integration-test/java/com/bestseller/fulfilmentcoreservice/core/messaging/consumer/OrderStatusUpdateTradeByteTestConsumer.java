package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
@Slf4j
@Getter
public class OrderStatusUpdateTradeByteTestConsumer implements Consumer<@Valid OrderStatusUpdateTradebyte> {
    public static final CopyOnWriteArrayList<OrderStatusUpdateTradebyte> ORDER_STATUS_UPDATE_TRADEBYTES =
        new CopyOnWriteArrayList<>();

    @Override
    public void accept(@Valid OrderStatusUpdateTradebyte orderStatusUpdateTradebyte) {
        log.info("Consuming OrderStatusUpdateTradebyte: payload={}", orderStatusUpdateTradebyte);
        ORDER_STATUS_UPDATE_TRADEBYTES.add(orderStatusUpdateTradebyte);
        log.info("OrderStatusUpdateTradebyte consumed successfully: payload={}", orderStatusUpdateTradebyte);
    }
}
