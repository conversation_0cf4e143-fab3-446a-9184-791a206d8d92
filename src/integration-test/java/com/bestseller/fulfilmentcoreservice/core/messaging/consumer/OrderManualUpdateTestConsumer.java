package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@Component
@RequiredArgsConstructor
@Slf4j
@Getter
public class OrderManualUpdateTestConsumer implements Consumer<OrderManualUpdate> {

    public static final CopyOnWriteArrayList<OrderManualUpdate> MESSAGES = new CopyOnWriteArrayList<>();

    @Override
    public void accept(OrderManualUpdate orderManualUpdate) {
        log.info("Consuming OrderManualUpdate: payload={}", orderManualUpdate);
        MESSAGES.add(orderManualUpdate);
        log.info("OrderManualUpdate consumed successfully: payload={}", orderManualUpdate);
    }

}
