package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineExportedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderLineExportedConsumerIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderLineExportedConsumer-in-0.destination}")
    private String orderLineExportedTopic;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderLineExportedQueueConsumer orderLineExportedQueueConsumer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Test
    void shouldConsumerOrderLineExported() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean("1234567890123")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .orderLines(List.of(orderLine))
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .brand(Brand.JJ)
            .build();
        orderService.save(order);

        var orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(order.getOrderId());
        orderLineExported.setEan(orderLine.getEan());
        orderLineExported.setQuantity(orderLine.getOriginalQty());
        orderLineExported.setExportDate(ZonedDateTime.now());

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineExportedTopic, orderLineExported));

        // assert
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setType(OrderStatusUpdated.Type.EXPORTED);
        orderStatusUpdated.setPayload(
            DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(List.of("EXPORTED", "EXPORTED"))
                            .build()
                    )
                )
                .build()
        );

        await().until(() ->
            orderRepository.findById(order.getOrderId()).orElseThrow().getMaxStatus().equals(OrderState.EXPORTED));

        await().untilAsserted(() -> {
            var orderStatusUpdatedList = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            assertThat(orderStatusUpdatedList)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(orderStatusUpdated);
        });
    }

    @Test
    void consumingOrderLineExported_givenUnknownOrderId_databaseQueueTaskEnqueued() {
        // arrange
        var orderId = "OL1224564666";
        var orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(orderId);
        orderLineExported.setEan("5715509089740");
        orderLineExported.setQuantity(2);
        orderLineExported.setExportDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS));

        // act
        getKafkaProducer().send(new ProducerRecord<>(orderLineExportedTopic, orderLineExported));

        // assert
        await().untilAsserted(() -> {
            List<Task<OrderLineExported>> tasks = databaseQueueTaskDao.getTasks(
                orderLineExportedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                OrderLineExported.class,
                orderLineExported.getOrderId(),
                OrderNotFoundException.class.getSimpleName()
            );
            List<OrderLineExported> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .containsExactly(orderLineExported);
        });
    }
}
