package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class OrderReturnInfoServiceIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    private OrderReturnInfoService orderReturnInfoService;

    @Test
    @SuppressWarnings({"MagicNumber", "IllegalCatch"})
    void updateLatestReturnDate_givenRaceConditionCase_noExceptionsThrown() throws InterruptedException {
        // arrange
        var orderId = UUID.randomUUID().toString();
        var executorService = Executors.newFixedThreadPool(5);
        var exceptions = new ConcurrentLinkedQueue<Exception>();

        // act
        for (int i = 0; i < 5; i++) {
            executorService.submit(() -> {
                try {
                    orderReturnInfoService.updateLatestReturnDate(orderId, LocalDate.now());
                } catch (Exception e) {
                    exceptions.add(e);
                }
            });
        }

        executorService.shutdown();
        var finished = executorService.awaitTermination(10, TimeUnit.SECONDS);

        // assert
        assertThat(finished).isTrue();
        assertThat(exceptions).isEmpty();
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId)).isPresent();
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void updateLatestReturnDate_givenRegistryFoundInDb_nothingIsUpdated() {
        // arrange
        var returnDate = LocalDate.now();
        var orderId = UUID.randomUUID().toString();
        var daysToAdd = 10;
        orderReturnInfoService.updateLatestReturnDate(orderId, returnDate.plusDays(daysToAdd));

        // act
        orderReturnInfoService.updateLatestReturnDate(orderId, returnDate);

        // assert
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId).orElseThrow().getLatestReturnDate())
            .isEqualTo(returnDate.plusDays(daysToAdd));
    }

    @Test
    void updateLatestReturnDate_givenNoReturnInfoInTheDb_newReturnInfoIsCreated() {
        // arrange
        var returnDate = LocalDate.now();
        var orderId = UUID.randomUUID().toString();

        // act
        orderReturnInfoService.updateLatestReturnDate(orderId, returnDate);

        // assert
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId)).isPresent();
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId).orElseThrow().getLatestReturnDate())
            .isEqualTo(returnDate);
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void updateLatestReturnDate_givenReturnInfoInDb_returnInfoIsUpdated() {
        // arrange
        var returnDate = LocalDate.now();
        var orderId = UUID.randomUUID().toString();
        var daysToSubtract = 10;
        orderReturnInfoService.updateLatestReturnDate(orderId, returnDate.minusDays(daysToSubtract));

        // act
        orderReturnInfoService.updateLatestReturnDate(orderId, returnDate);

        // assert
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId).orElseThrow().getLatestReturnDate())
            .isEqualTo(returnDate);
    }

}
