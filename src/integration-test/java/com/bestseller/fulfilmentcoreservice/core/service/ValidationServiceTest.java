package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ValidationServiceTest extends AbstractIntegrationTest {

    @Autowired
    private ValidationService validationService;

    @Test
    void checkValidationService_when_catchConstraintViolationException() {
        // arrange & act
        Set<ConstraintViolation<OrderPlaced>> validationErrors =
            validationService.getValidationErrors(new OrderPlaced());

        // assert
        assertThat(validationErrors).isNotEmpty();
        assertThatThrownBy(() -> validationService.validate(new OrderPlaced()))
            .isInstanceOf(ConstraintViolationException.class);
    }

}
