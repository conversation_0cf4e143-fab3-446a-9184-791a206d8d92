package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.core.exception.ValidationException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

class OrderPlacedValidationServiceTest extends AbstractIntegrationTest {

    @Value("classpath:orders/iso_order_without_address_line_3.json")
    private Resource isoOrderWithoutAddressLine3OrderPlaced;

    @Value("classpath:orders/parcel_shop_order_without_address_line_3.json")
    private Resource parcelShopOrderWithoutAddressLine3OrderPlaced;

    @Autowired
    private OrderPlacedValidationService orderPlacedValidationService;

    @Test
    void validate_givenIsoOrderWithoutAddressLine3_noExceptionThrown() {
        // arrange
        var orderPlaced = getObjectFromResources(isoOrderWithoutAddressLine3OrderPlaced, OrderPlaced.class);

        // act & assert
        assertDoesNotThrow(() -> orderPlacedValidationService.validate(orderPlaced));
    }

    @Test
    void validate_givenParcelShopOrderWithoutAddressLine3_exceptionThrown() {
        // arrange
        var orderPlaced = getObjectFromResources(parcelShopOrderWithoutAddressLine3OrderPlaced, OrderPlaced.class);

        // act & assert
        var validationException =
            assertThrows(ValidationException.class, () -> orderPlacedValidationService.validate(orderPlaced));
        assertThat(validationException)
            .hasMessageContaining("For parcel shop shipping: "
                + "\"OrderPlaced.shippingInformation.shippingAddress.addressLine3\" must not be empty or null!"
            );
    }

}
