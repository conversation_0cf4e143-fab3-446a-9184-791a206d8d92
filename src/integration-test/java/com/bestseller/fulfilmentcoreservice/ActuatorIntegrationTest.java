package com.bestseller.fulfilmentcoreservice;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;

@AutoConfigureWebTestClient(timeout = "10000")
class ActuatorIntegrationTest extends AbstractIntegrationTest {
    @Test
    void healthCheck_anonymousUser_okResponse() {
        // arrange - nothing

        // act
        getClient().get().uri("/actuator/health").exchange()
            // assert
            .expectStatus().is2xxSuccessful();
    }

}
