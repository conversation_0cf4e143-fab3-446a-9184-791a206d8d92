package com.bestseller.fulfilmentcoreservice.database;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class BrandViewIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Check that the vw_brand view covers all the values of the Brand enum.
     */
    @Test
    void test_brandView_coversAll() {
        // arrange
        // Get all enum values from the Brand enum
        Set<String> enumBrands = Set.of(Brand.values()).stream()
            .map(Brand::name)
            .collect(Collectors.toSet());

        // act
        // Query the view to get all the brand codes
        List<String> viewBrands = jdbcTemplate.queryForList("SELECT code FROM vw_brand", String.class);

        // assert
        // Assert that the sets are equal (the view should contain all enum values)
        assertEquals(enumBrands, Set.copyOf(viewBrands),
            "The brand_view does not cover all Brand enum values");
    }

    @Test
    void test_brandView_fails() {
        // arrange
        Set<String> enumBrands = Set.of(Brand.values()).stream()
            .map(Brand::name)
            .collect(Collectors.toSet());
        enumBrands.add("XX");

        // act
        List<String> viewBrands = jdbcTemplate.queryForList("SELECT code FROM vw_brand", String.class);

        // assert
        assertNotEquals(enumBrands, Set.copyOf(viewBrands),
            "The brand_view does not cover all Brand enum values");
    }
}
