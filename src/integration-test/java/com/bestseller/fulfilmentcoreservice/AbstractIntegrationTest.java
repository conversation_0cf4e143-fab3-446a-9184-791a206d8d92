package com.bestseller.fulfilmentcoreservice;

import com.bestseller.fulfilmentcoreservice.configuration.KafkaTestConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.kafka.KafkaContainer;
import org.wiremock.integrations.testcontainers.WireMockContainer;

import java.io.InputStreamReader;
import java.util.stream.Stream;

import static org.testcontainers.utility.DockerImageName.parse;
import static org.testcontainers.utility.MountableFile.forClasspathResource;

@ExtendWith(SpringExtension.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = KafkaTestConfiguration.class
)
@Getter
@ActiveProfiles("local")
public abstract class AbstractIntegrationTest {

    @SuppressWarnings("resource")
    private static final MySQLContainer<?> MYSQL_CONTAINER = new MySQLContainer<>(parse("mysql:8-debian"))
        .withReuse(true);

    private static final KafkaContainer KAFKA_CONTAINER = new KafkaContainer(parse("apache/kafka:latest"))
        .withReuse(true);

    private static final WireMockContainer WIREMOCK_SERVER = new WireMockContainer(WireMockContainer.WIREMOCK_2_LATEST)
        .withCopyFileToContainer(forClasspathResource("wiremock"), "/home/<USER>/")
        .withReuse(true);

    @Autowired
    private WebTestClient client;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeAll
    public static void startContainers() {
        Stream.of(MYSQL_CONTAINER, KAFKA_CONTAINER, WIREMOCK_SERVER)
            .parallel()
            .forEach(GenericContainer::start);
    }

    @DynamicPropertySource
    static void setApplicationProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", MYSQL_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", MYSQL_CONTAINER::getUsername);
        registry.add("spring.datasource.password", MYSQL_CONTAINER::getPassword);

        registry.add("spring.flyway.url", MYSQL_CONTAINER::getJdbcUrl);
        registry.add("spring.flyway.user", MYSQL_CONTAINER::getUsername);
        registry.add("spring.flyway.password", MYSQL_CONTAINER::getPassword);

        registry.add("spring.kafka.bootstrap-servers", KAFKA_CONTAINER::getBootstrapServers);
        registry.add("spring.cloud.stream.kafka.binder.brokers", KAFKA_CONTAINER::getBootstrapServers);

        registry.add("gateway.services.payment-service.url", () -> WIREMOCK_SERVER.getBaseUrl() + "/pms");
    }

    @SneakyThrows
    protected <T> T getObjectFromResources(Resource resource, Class<T> clazz) {
        return objectMapper.readValue(new InputStreamReader(resource.getInputStream()), clazz);
    }
}
