package com.bestseller.fulfilmentcoreservice.controller;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderPlacedValidationView;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPromotion;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;

class OrderPlacedValidationIntegrationTest extends AbstractIntegrationTest {
    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> httpHeaders.setBasicAuth("admin", "admin");

    @Test
    void validateOrderPlaced_givenValidOrderPlaced_isSuccess() {
        // arrange
        // act
        OrderPlacedValidationView response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(OrderPlacedGenerator.createValidOrderPlaced())
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(true)
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenNullStore_isFailed() {
        // arrange
        OrderPlaced orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.setStore(null);

        // act
        OrderPlacedValidationView response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(false)
                    .errorMessages(List.of("\"OrderPlaced.store\" must not be empty or null!"))
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenNullOrderDetails_isFailed() {
        // arrange
        OrderPlaced orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.setOrderDetails(null);

        // act
        OrderPlacedValidationView response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(false)
                    .errorMessages(List.of("orderDetails must not be null"))
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenEmptyShippingHouseNumber_isSuccess() {
        // arrange
        var orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.getShippingInformation().getShippingAddress().setHouseNumber(StringUtils.EMPTY);

        // act
        var response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(true)
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenEmptyCarrierVariant_isSuccess() {
        // arrange
        var orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.getOrderDetails().setCarrierVariant(StringUtils.EMPTY);

        // act
        var response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(true)
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenPositiveOrderPromotionNetPrice_isSuccess() {
        // arrange
        var orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.setOrderPromotions(List.of(
            new OrderPromotion()
                .withNetPrice(BigDecimal.ONE)
        ));

        // act
        var response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(true)
                    .build()
            );
    }

    @Test
    void validateOrderPlaced_givenPositiveOrderPromotionGrossPrice_isSuccess() {
        // arrange
        var orderPlaced = OrderPlacedGenerator.createValidOrderPlaced();
        orderPlaced.setOrderPromotions(List.of(
            new OrderPromotion()
                .withGrossPrice(BigDecimal.ONE)
        ));

        // act
        var response = getClient()
            .post()
            .uri(uriBuilder -> uriBuilder.path("order-placed/validate").build())
            .bodyValue(orderPlaced)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .returnResult(OrderPlacedValidationView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(response)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderPlacedValidationView.builder()
                    .isSuccess(true)
                    .build()
            );
    }
}

