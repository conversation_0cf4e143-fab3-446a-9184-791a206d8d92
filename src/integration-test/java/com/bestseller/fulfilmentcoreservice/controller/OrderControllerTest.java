package com.bestseller.fulfilmentcoreservice.controller;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.api.dto.RefundOptionsResponse;
import com.bestseller.fulfilmentcoreservice.core.messaging.consumer.OrderManualUpdateTestConsumer;
import com.bestseller.fulfilmentcoreservice.core.messaging.consumer.OrderStatusUpdatedTestConsumer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.service.PaymentService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderBlock;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFilter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFilterRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

class OrderControllerTest extends AbstractIntegrationTest {

    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> httpHeaders.setBasicAuth("admin", "admin");

    @Value("classpath:paymentStatusUpdated/demandware_payment_status_updated.json")
    private Resource paymentStatusUpdatedJson;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderFilterRepository orderFilterRepository;

    @MockitoSpyBean
    private PaymentService paymentService;

    @Test
    void getOrderSuccess() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean("**********")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .orderLines(List.of(orderLine))
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .build();
        orderService.save(order);

        // act
        OrderView orderView = getClient().get()
            .uri("/orders/{id}", order.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(OrderView.class)
            .getResponseBody()
            .blockFirst();

        // assert
        assertThat(orderView.id())
            .isEqualTo(order.getOrderId());

    }

    @Test
    void getOrderNotFound() {
        // arrange
        String testOrderId = "OL1200297343";

        // act & assert
        getClient().get()
            .uri("/orders/{id}", testOrderId)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isNotFound();
    }

    @Test
    void getOrders_givenRequestWithNoAuth_unauthorized() {
        // arrange
        String testOrderId = "OL1200297343";

        // act & assert
        getClient().get()
            .uri("/orders/{id}", testOrderId)
            .exchange()
            .expectStatus()
            .isEqualTo(HttpStatus.UNAUTHORIZED.value());
    }

    @Test
    void blockOrder_orderShouldBeBlocked() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean("**********")
            .ecomId(UUID.randomUUID())
            .originalQty(1)
            .build();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .orderLines(List.of(orderLine))
            .build();
        orderService.save(order);

        // act
        getClient()
            .put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/block").build(order.getOrderId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        // assert
        var actualOrder = orderService.findById(order.getOrderId());
        assertThat(actualOrder.getMinStatus()).isEqualByComparingTo(OrderState.BLOCKED);
        assertThat(actualOrder.getMaxStatus()).isEqualByComparingTo(OrderState.BLOCKED);
        assertThat(actualOrder.getOrderBlock().getResumeState()).isEqualByComparingTo(OrderState.PLACED);

        var orderFilter = orderFilterRepository.findByOrderId(order.getOrderId());
        assertThat(orderFilter).isNotPresent();

        await().untilAsserted(() -> {
            var messages = OrderStatusUpdatedTestConsumer.ORDER_STATUS_UPDATED_LIST;
            var orderStatusUpdatedPayload = DefaultOrderStatusUpdatedPayload.builder()
                .orderLines(
                    List.of(
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(List.of("BLOCKED"))
                            .build()
                    )
                )
                .build();
            assertThat(messages)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("updatedAt", "timestamp")
                        .build()
                )
                .contains(
                    new OrderStatusUpdated()
                        .withOrderId(order.getOrderId())
                        .withType(OrderStatusUpdated.Type.BLOCKED)
                        .withPayload(orderStatusUpdatedPayload)
                );
        });
    }

    @Test
    void blockOrder_orderNotFound_orderFilterIsCreated() {
        // arrange
        var orderId = UUID.randomUUID().toString();

        // act
        getClient()
            .put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/block").build(orderId))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        // assert
        var orderFilter = orderFilterRepository.findByOrderId(orderId);
        assertThat(orderFilter).isPresent();
        assertThat(orderFilter.get().getOrderId()).isEqualTo(orderId);
    }

    @Test
    void unblockOrder_paymentAuthorized_orderUnblockedAndFulfilled() {
        // arrange
        var order = orderService.save(
            Order.builder()
                .orderId(UUID.randomUUID().toString())
                .currency("EUR")
                .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
                .platform(Platform.DEMANDWARE)
                .shippingMethod(ShippingMethod.STANDARD)
                .orderLines(
                    List.of(
                        OrderLine.builder()
                            .ean("**********")
                            .ecomId(UUID.randomUUID())
                            .originalQty(2)
                            .build()
                    )
                )
                .minStatus(OrderState.BLOCKED)
                .maxStatus(OrderState.BLOCKED)
                .build()
        );
        order.setOrderBlock(
            OrderBlock.builder()
                .resumeState(OrderState.PLACED)
                .build()
        );
        orderService.save(order);
        orderFilterRepository.save(
            OrderFilter.builder()
                .orderId(order.getOrderId())
                .build()
        );

        // Mock behavior for paymentService
        var mockPaymentStatusUpdated = getObjectFromResources(paymentStatusUpdatedJson, PaymentStatusUpdated.class);
        mockPaymentStatusUpdated.setOrderId(order.getOrderId());
        doReturn(mockPaymentStatusUpdated)
            .when(paymentService)
            .getPaymentStatusUpdatedMessage(order.getOrderId());

        // act
        getClient()
            .put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/unblock").build(order.getOrderId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        // assert
        var actualOrder = orderService.findById(order.getOrderId());
        // Because the order has been authorized, unblocking triggers the fulfillment
        assertThat(actualOrder.getMinStatus()).isEqualByComparingTo(OrderState.ROUTING);
        assertThat(actualOrder.getMaxStatus()).isEqualByComparingTo(OrderState.ROUTING);
        assertThat(actualOrder.isOrderPaymentAuthorised()).isTrue();
        assertThat(actualOrder.getOrderBlock()).isNull();

        var orderFilterOptional = orderFilterRepository.findByOrderId(order.getOrderId());
        assertThat(orderFilterOptional).isEmpty();
    }

    @Test
    void unblockOrder_paymentNotAuthorized_orderUnblockedAndNotFulfilled() {
        // arrange
        var order = orderService.save(
            Order.builder()
                .orderId(UUID.randomUUID().toString())
                .currency("EUR")
                .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
                .platform(Platform.DEMANDWARE)
                .shippingMethod(ShippingMethod.STANDARD)
                .orderLines(
                    List.of(
                        OrderLine.builder()
                            .ean("**********")
                            .ecomId(UUID.randomUUID())
                            .originalQty(2)
                            .build()
                    )
                )
                .minStatus(OrderState.BLOCKED)
                .maxStatus(OrderState.BLOCKED)
                .build()
        );
        order.setOrderBlock(
            OrderBlock.builder()
                .resumeState(OrderState.PLACED)
                .build()
        );
        orderService.save(order);
        orderFilterRepository.save(
            OrderFilter.builder()
                .orderId(order.getOrderId())
                .build()
        );

        // Mock behavior for paymentService
        var mockPaymentStatusUpdated = getObjectFromResources(paymentStatusUpdatedJson, PaymentStatusUpdated.class);
        mockPaymentStatusUpdated.setOrderId(order.getOrderId());
        mockPaymentStatusUpdated.setPaymentState(PaymentStatusUpdated.PaymentState.SETTLEMENT_REQUESTED);
        doReturn(mockPaymentStatusUpdated)
            .when(paymentService)
            .getPaymentStatusUpdatedMessage(order.getOrderId());

        // act
        getClient()
            .put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/unblock").build(order.getOrderId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        // assert
        var actualOrder = orderService.findById(order.getOrderId());
        assertThat(actualOrder.getMinStatus()).isEqualByComparingTo(OrderState.PLACED);
        assertThat(actualOrder.getMaxStatus()).isEqualByComparingTo(OrderState.PLACED);
        assertThat(actualOrder.isOrderPaymentAuthorised()).isFalse();
        assertThat(actualOrder.getOrderBlock()).isNull();

        var orderFilterOptional = orderFilterRepository.findByOrderId(order.getOrderId());
        assertThat(orderFilterOptional).isEmpty();
    }

    @Test
    void updateEmailAddress_orderNotFound_notFoundErrorReceived() {
        // arrange
        var orderId = UUID.randomUUID().toString();

        // act & assert
        getClient().put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/update-email")
                .queryParam("emailAddress", "<EMAIL>")
                .build(orderId))
            .headers(HTTP_HEADERS_CONSUMER)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus()
            .isNotFound();
    }

    @Test
    void updateEmailAddress_customerEmailIsUpdatedAndOrderManualUpdateMessageIsProduced() {
        // arrange
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MILLIS))
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("**********")
                    .ecomId(UUID.randomUUID())
                    .originalQty(1)
                    .build()
            ))
            .customer(
                Customer.builder()
                    .customerId(UUID.randomUUID().toString())
                    .email("<EMAIL>")
                    .build()
            )
            .build();
        orderService.save(order);

        // act
        getClient().put()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/update-email")
                .queryParam("emailAddress", "<EMAIL>")
                .build(order.getOrderId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED)
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        // assert
        var actualOrder = orderService.findById(order.getOrderId());
        assertThat(actualOrder.getCustomer().getEmail()).isEqualTo("<EMAIL>");

        await().untilAsserted(() -> {
            var messages = OrderManualUpdateTestConsumer.MESSAGES;
            assertThat(messages)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withIgnoredFields("timestamp")
                        .build()
                )
                .contains(
                    new OrderManualUpdate()
                        .withOrderId(order.getOrderId())
                        .withKey("email")
                        .withValue("<EMAIL>")
                );
        });
    }

    @Test
    void refundOptions_givenValidOrderId_itReturnsRefundOptions() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean("**********")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();
        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .orderLines(List.of(orderLine))
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .shippingAddress(Address.builder()
                .city("Berlin")
                .countryCode("DE")
                .build())
            .market(Market.DE)
            .build();
        orderService.save(order);

        //act
        RefundOptionsResponse response = getClient()
            .get()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/refund-options").build(order.getOrderId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(RefundOptionsResponse.class)
            .getResponseBody()
            .blockFirst();

        //assert
        assertThat(response).isNotNull();
        Assertions.assertTrue(response.refundShippingFee());
        Assertions.assertFalse(response.chargeReturnFee());
    }

    @Test
    void refundOptions_givenInvalidOrderId_itReturnsNotFound() {
        // arrange
        var orderId = UUID.randomUUID().toString();

        // act & assert
        getClient().get()
            .uri(uriBuilder -> uriBuilder.path("orders/{id}/refund-options").build(orderId))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isNotFound();
    }
}
