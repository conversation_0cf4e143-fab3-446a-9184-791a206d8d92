package com.bestseller.fulfilmentcoreservice.controller;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.api.dto.ReturnReasonCodesResponse;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;

public class RefundControllerTest extends AbstractIntegrationTest {
    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> httpHeaders.setBasicAuth("admin", "admin");
    @Autowired
    private OrderService orderService;

    @SuppressWarnings("MagicNumber")
    @Test
    void getReturnReasonCodes_givenValidRefundRequestIds_itReturnsReturnReasonCodes() {
        // arrange
        var refundRequestIdFirst = UUID.randomUUID();
        var refundRequestIdSecond = UUID.randomUUID();

        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .shippingAddress(Address.builder()
                .city("Berlin")
                .countryCode("DE")
                .build())
            .market(Market.DE)
            .build();

        var expectedResponse = ReturnReasonCodesResponse.builder()
            .returnReasonCodes(List.of(
                CustomerReturnReason.MANUAL_REFUND_NO_RETURN_FEE_23.getValue()
            ))
            .build();

        var orderLineQuantityStatusFirst =
            OrderLineQtyStatus.builder()
                .orderStatusUpdateInfo(OrderStatusUpdateInfo
                    .builder()
                    .order(order)
                    .refundRequestId(refundRequestIdFirst)
                    .customerReturnReason(CustomerReturnReason.MANUAL_REFUND_NO_RETURN_FEE_23)
                    .build())
                .build();

        var orderLineQuanityStatusSecond =
            OrderLineQtyStatus.builder()
                .orderStatusUpdateInfo(OrderStatusUpdateInfo
                    .builder()
                    .order(order)
                    .refundRequestId(refundRequestIdSecond)
                    .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                    .build())
                .build();

        var orderLine = OrderLine.builder()
            .ean("1707921066")
            .ecomId(UUID.randomUUID())
            .orderLineQtyStatus(List.of(orderLineQuantityStatusFirst, orderLineQuanityStatusSecond))
            .originalQty(2)
            .build();
        order.setOrderLines(List.of(orderLine));

        orderService.save(order);

        var refundRequestId = refundRequestIdFirst.toString();


        // act & assert
        var response = getClient().get()
            .uri(uriBuilder -> uriBuilder.path("/refunds/{id}/reason-codes")
                .build(refundRequestId))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(ReturnReasonCodesResponse.class)
            .getResponseBody()
            .blockFirst();

        assertThat(response).isEqualTo(expectedResponse);
    }

    @Test
    void getReturnReasonCodes_givenValidRefundRequestIds_itReturnsEmptyListReturnReasonCodes() {
        // arrange
        var refundRequestIdFirst = UUID.randomUUID().toString();
        var expectedResponse = ReturnReasonCodesResponse.builder()
            .returnReasonCodes(List.of())
            .build();

        var order = Order.builder()
            .orderId(UUID.randomUUID().toString())
            .currency("EUR")
            .orderDate(ZonedDateTime.now())
            .platform(Platform.DEMANDWARE)
            .shippingMethod(ShippingMethod.STANDARD)
            .shippingAddress(Address.builder()
                .city("Berlin")
                .countryCode("DE")
                .build())
            .market(Market.DE)
            .build();

        var orderLineQuantityStatusFirst =
            OrderLineQtyStatus.builder()
                .orderStatusUpdateInfo(OrderStatusUpdateInfo
                    .builder()
                    .order(order)
                    .refundRequestId(UUID.randomUUID())
                    .customerReturnReason(CustomerReturnReason.MANUAL_REFUND_NO_RETURN_FEE_23)
                    .build())
                .build();

        var orderLineQuanityStatusSecond =
            OrderLineQtyStatus.builder()
                .orderStatusUpdateInfo(OrderStatusUpdateInfo
                    .builder()
                    .order(order)
                    .refundRequestId(UUID.randomUUID())
                    .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                    .build())
                .build();

        var orderLine = OrderLine.builder()
            .ean("1707921066")
            .ecomId(UUID.randomUUID())
            .orderLineQtyStatus(List.of(orderLineQuantityStatusFirst, orderLineQuanityStatusSecond))
            .originalQty(2)
            .build();
        order.setOrderLines(List.of(orderLine));

        orderService.save(order);


        // act & assert
        var response = getClient().get()
            .uri(uriBuilder -> uriBuilder.path("/refunds/{id}/reason-codes")
                .build(refundRequestIdFirst))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(ReturnReasonCodesResponse.class)
            .getResponseBody()
            .blockFirst();

        assertThat(response).isEqualTo(expectedResponse);
    }

}
