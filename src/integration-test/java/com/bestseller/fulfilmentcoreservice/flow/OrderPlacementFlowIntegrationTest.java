package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import jakarta.transaction.Transactional;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.ZonedDateTime;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.Brand.JJ;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.Brand.ON;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType.STOREFRONT;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.Market.BSE_DK;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.Market.BSE_NL;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.Market.DE;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.STANDARD;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.VIRTUAL;
import static com.logistics.statetransition.OrderState.PLACED;
import static com.logistics.statetransition.Platform.DEMANDWARE;
import static com.logistics.statetransition.Platform.TRADEBYTE;
import static org.assertj.core.api.Assertions.assertThat;

@Transactional
class OrderPlacementFlowIntegrationTest extends FlowIntegrationTest {

    private static final String CACHE_KEY = "BRAND_BY_ORDER_ID_%s";

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Test
    void producingDemandwareValidOrderPlaced() {
        // arrange
        var orderId = orderIdGenerator.generate();

        // act
        var validOrderPlaced = produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);

        // assert
        Order orderFound = getOrderRepository().findById(validOrderPlaced.getOrderId()).orElseThrow();
        assertThat(orderFound)
            .extracting("orderId", "orderDate", "checkoutScopeId", "market", "platform",
                "channelType", "shippingMethod", "currency", "minStatus", "maxStatus",
                "externalOrderId", "test", "billToMatchesShipTo", "brand", "partnerChannel", "announcedDeliveryDate")
            .containsExactlyInAnyOrder(validOrderPlaced.getOrderId(), ZonedDateTime.parse("2023-10-23T21:05:37Z"),
                "jj", BSE_NL, DEMANDWARE, STOREFRONT, STANDARD, "EUR", PLACED, PLACED, null,
                false, false, JJ, null, LocalDate.parse("2023-10-28"));

        assertThat(orderFound.getOrderLines())
            .extracting("ean", "originalQty", "lineNumber", "virtualProduct")
            .containsExactlyInAnyOrder(Tuple.tuple("5715424250751", 2, 1, false),
                Tuple.tuple("5715426931214", 1, 2, false));
    }

    @Test
    void producingTradebyteValidOrderPlaced() {
        // arrange
        var orderId = orderIdGenerator.generate();

        // act
        var validOrderPlaced = produceValidOrderPlacedAndWaitForIt(getTradebyteValidOrderPlacedJson(), orderId);

        // assert
        Order orderFound = getOrderRepository().findById(validOrderPlaced.getOrderId()).orElseThrow();
        assertThat(orderFound)
            .extracting("orderId", "orderDate", "checkoutScopeId", "market", "platform",
                "channelType", "shippingMethod", "currency", "minStatus", "maxStatus",
                "externalOrderId", "test", "billToMatchesShipTo", "brand", "partnerChannel.channelId")
            .containsExactlyInAnyOrder(validOrderPlaced.getOrderId(),
                ZonedDateTime.parse("2023-10-16T00:00:00Z"), null, DE, TRADEBYTE, STOREFRONT,
                STANDARD, "EUR", PLACED, PLACED, "10103514098064", false, false, null, "zade");

        assertThat(orderFound.getOrderLines())
            .extracting("ean", "originalQty", "lineNumber", "virtualProduct")
            .containsExactlyInAnyOrder(Tuple.tuple("5715428033459", 1, 1, false));
    }

    @Test
    void producingVirtualValidOrderPlaced() {
        // arrange
        var orderId = orderIdGenerator.generate();
        var validOrderPlaced = produceValidOrderPlacedAndWaitForIt(getVirtualOrderPlacedJson(), orderId);

        Order orderFound = getOrderRepository().findById(validOrderPlaced.getOrderId()).orElseThrow();
        assertThat(orderFound)
            .extracting("orderId", "orderDate", "checkoutScopeId", "market", "platform",
                "channelType", "shippingMethod", "currency", "minStatus", "maxStatus",
                "externalOrderId", "test", "billToMatchesShipTo", "brand", "partnerChannel.channelId")
            .containsExactlyInAnyOrder(validOrderPlaced.getOrderId(), ZonedDateTime.parse("2023-11-01T07:58:52Z"),
                "on", BSE_DK, DEMANDWARE, STOREFRONT, VIRTUAL, "DKK", PLACED, PLACED, null,
                false, false, ON, null);

        final int originalQuantity = 7;
        assertThat(orderFound.getOrderLines())
            .extracting("ean", "originalQty", "lineNumber", "virtualProduct")
            .containsExactlyInAnyOrder(Tuple.tuple("5712062660904", originalQuantity, 1, true));
    }

}
