package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.logistics.statetransition.OrderState;
import jakarta.transaction.Transactional;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@Transactional
class OrderFulfillmentFlowIntegrationTest extends FlowIntegrationTest {

    private static final int ORDER_LINE_QUANTITY_MAX_SIZE = 3;

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Test
    void orderPartsRouted_flow() {
        // arrange
        var orderId = orderIdGenerator.generate();
        var validOrderPlaced = produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);

        // assert
        var orderFulfillmentPart =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);
        assertThat(orderFulfillmentPart.get(0))
            .extracting("partNumber", "fulfillmentNode")
            .containsExactlyInAnyOrder(1, "INGRAM_MICRO_NL");

        assertThat(orderFulfillmentPart.get(0).getOrder()).isNotNull();
        assertThat(orderFulfillmentPart.get(0).getOrderLineQtyStatus()).hasSize(ORDER_LINE_QUANTITY_MAX_SIZE);

        validOrderPlaced.getOrderLines().forEach(orderLineInMessage -> {
            assertThat(orderFulfillmentPart.get(0).getOrder()
                .getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getLineNumber() == orderLineInMessage.getLineNumber())
                .findFirst())
                .isPresent();

            assertThat(orderFulfillmentPart.get(0).getOrder()
                .getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getLineNumber() == orderLineInMessage.getLineNumber())
                .findFirst()
                .get()
                .getOrderLineQtyStatus())
                .hasSize(orderLineInMessage.getQuantity());
        });
    }

    @Test
    void orderPartsRouted_flow_createFulfillmentPart() {
        // arrange
        var orderId = orderIdGenerator.generate();
        var validOrderPlaced = produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);

        // assert
        var orderFulfillmentPart =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);
        assertThat(orderFulfillmentPart.get(0).isActive()).isTrue();
        assertThat(orderFulfillmentPart.get(0).getOrderLineQtyStatus().size())
            .isEqualTo(ORDER_LINE_QUANTITY_MAX_SIZE);

        validOrderPlaced.getOrderLines().forEach(orderLineInMessage -> {
            assertThat(orderFulfillmentPart.get(0).getOrder()
                .getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getLineNumber() == orderLineInMessage.getLineNumber())
                .findFirst())
                .isPresent();

            assertThat(orderFulfillmentPart.get(0).getOrder()
                .getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getLineNumber() == orderLineInMessage.getLineNumber())
                .findFirst()
                .get()
                .getOrderLineQtyStatus())
                .hasSize(orderLineInMessage.getQuantity());
        });
    }

    @Test
    void orderPartsRouted_flow_createFulfillmentPartWithDifferentNode() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderPartsRoutedPart1AndWaitForIt(orderId);
        produceOrderPartsRoutedPart2AndWaitForIt(orderId);

        // assert
        var orderFulfillmentParts =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);
        assertThat(orderFulfillmentParts).hasSize(2);
        assertThat(orderFulfillmentParts.get(0).isActive()).isTrue();
        assertThat(orderFulfillmentParts.get(0).getOrderLineQtyStatus()).isNotEmpty();
        assertThat(orderFulfillmentParts.get(1).getOrderLineQtyStatus()).isNotEmpty();
    }

    @Test
    void orderPartsRouted_flow_multipleFulfilmentNode() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        produceOrderPartsRoutedPart1AndWaitForIt(orderId);
        produceOrderLineExportedAndWaitForIt(orderId, 1);

        // act
        produceOrderPartsRoutedPart2AndWaitForIt(orderId);

        // assert
        var orderFulfillmentParts =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);
        assertThat(orderFulfillmentParts).hasSize(2);
    }

    @SuppressWarnings("magicnumber")
    @Test
    void orderFulfilment_flow_multipleFulfilmentNodeWithReroute() {
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderPartsRoutedPart1AndWaitForIt(orderId);
        produceOrderPartsRoutedPart2AndWaitForIt(orderId);

        produceOrderLineExportedAndWaitForIt(orderId, 1);
        produceOrderLineExportedAndWaitForIt(orderId, 2);

        produceOrderLineAcknowledgedAndWaitForIt(orderId, 1);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 2);

        produceOrderPartsRoutedPart1AndWaitForIt(orderId, "SHIP_FROM_STORE_NL"); // reroute
        produceOrderLineExported(orderId, 1, "SHIP_FROM_STORE_NL",
            orderLineExported ->
                transactionTemplate.execute(status -> {
                    var order = getOrderRepository().findById(orderId).orElseThrow();

                    var orderLineStatus = order.getOrderLines().stream()
                        .filter(orderLine -> orderLine.getEan().equals(orderLineExported.getEan()))
                        .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                        .map(OrderLineQtyStatus::getOrderState)
                        .findFirst()
                        .orElseThrow();

                    return orderLineStatus.equals(OrderState.EXPORTED);
                }));

        produceOrderLineAcknowledgedAndWaitForIt(orderId, 1, "SHIP_FROM_STORE_NL", null);

        produceOrderLineDispatchedAndWaitForIt(orderId, 1, "SHIP_FROM_STORE_NL");
        produceOrderLineDispatchedAndWaitForIt(orderId, 2, "INGRAM_MICRO");

        // assert
        assertOrderFulfilmentParts(orderId);
        var orderFulfillmentParts =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);

        orderFulfillmentParts.stream().filter(OrderFulfillmentPart::isActive)
            .forEach(orderFulfillmentPart -> assertThat(orderFulfillmentPart.getTrackingNumber()).isNotNull());

        orderFulfillmentParts.stream().filter(orderFulfillmentPart -> !orderFulfillmentPart.isActive())
            .forEach(orderFulfillmentPart -> assertThat(orderFulfillmentPart.getTrackingNumber()).isNullOrEmpty());

        assertThat(orderFulfillmentParts).hasSize(3);
        assertThat(orderFulfillmentParts.get(0).isActive()).isFalse();
        assertThat(orderFulfillmentParts.get(0).getOrderLineQtyStatus()).isEmpty();
        assertThat(orderFulfillmentParts.get(1).getOrderLineQtyStatus()).isNotEmpty();
        assertThat(orderFulfillmentParts.get(2).getOrderLineQtyStatus()).isNotEmpty();
    }

    @SuppressWarnings("magicnumber")
    @Test
    void orderFulfilment_flow_eanWithMultipleQuantityANdDifferentState() {
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);

        produceOrderLineExportedAndWaitForIt(orderId, 1);
        produceOrderLineExportedAndWaitForIt(orderId, 2);

        produceOrderLineAcknowledgedAndWaitForIt(orderId, 1, null, 1);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 2);

        produceOrderPartsCancelledAndWaitForIt(orderId);

        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            order.getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getLineNumber() == 1)
                .findFirst()
                .ifPresentOrElse(orderLine -> {
                    var cancelledOrderLineQuantity = orderLine.getOrderLineQtyStatus().stream()
                        .anyMatch(orderLineQtyStatus ->
                            orderLineQtyStatus.getOrderState().equals(OrderState.CANCELLED));

                    var acknowledgedOrderLineQuantity = orderLine.getOrderLineQtyStatus().stream()
                        .anyMatch(orderLineQtyStatus ->
                            orderLineQtyStatus.getOrderState().equals(OrderState.PROCESSING));

                    assertTrue(cancelledOrderLineQuantity, "Order line not cancelled");
                    assertTrue(acknowledgedOrderLineQuantity, "Order line not acknowledged");
                }, org.junit.jupiter.api.Assertions::fail);
            assertEquals(OrderState.PROCESSING, order.getMinStatus());
            assertEquals(OrderState.CANCELLED, order.getMaxStatus());
        }));
    }

    @Test
    void orderFulfilment_flow_slow_consumer() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // act
        produceOrderLineExported(orderId, 1, null, assertMessagedSavedInDbQueue());
        produceOrderLineExported(orderId, 2, null, assertMessagedSavedInDbQueue());

        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);

        // assert
        await().untilAsserted(() -> getOrderRepository().findById(orderId)
            .ifPresent(order -> assertEquals(OrderState.ROUTED, order.getMinStatus())));
    }

    @Test
    void virtualOrder_fulfilmentFlow() {
        String orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getVirtualOrderPlacedJson(), orderId);

        produceGiftCardPurchasedAndWaitForIt(orderId);
    }

    private @NotNull Function<OrderLineExported, Boolean> assertMessagedSavedInDbQueue() {
        return orderLineExported -> {
            var tasks = getDatabaseQueueTaskDao().getTasks(
                getOrderLineExportedQueueConsumer().getQueueConfig().getLocation().getQueueId().asString(),
                OrderLineExported.class,
                orderLineExported.getOrderId(),
                InvalidStateTransitionException.class.getSimpleName()
            );
            List<OrderLineExported> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
            assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .contains(orderLineExported);
            return true;
        };
    }

    @SuppressWarnings("magicnumber")
    private void assertOrderFulfilmentParts(String orderId) {
        var numberOfFulfilmentParts = 3;
        var orderFulfillmentParts =
            getOrderFulfillmentPartRepository().findOrderFulfillmentPartByOrderOrderId(orderId);
        assertThat(orderFulfillmentParts).hasSize(numberOfFulfilmentParts);
        assertThat(orderFulfillmentParts.get(0).isActive()).isFalse();
        assertThat(orderFulfillmentParts.get(1).getOrderLineQtyStatus()).isNotEmpty();
        assertThat(orderFulfillmentParts.get(2).getOrderLineQtyStatus()).isNotEmpty();

        var activeOrderFulfilmentParts = orderFulfillmentParts.stream()
            .filter(OrderFulfillmentPart::isActive)
            .toList();

        assertThat(activeOrderFulfilmentParts).hasSize(2);
        assertTrue(activeOrderFulfilmentParts.stream()
            .anyMatch(orderFulfillmentPart -> orderFulfillmentPart.getFulfillmentNode().equals("SHIP_FROM_STORE_NL")));

        assertTrue(activeOrderFulfilmentParts.stream()
            .anyMatch(orderFulfillmentPart -> orderFulfillmentPart.getFulfillmentNode().equals("INGRAM_MICRO")));
    }
}
