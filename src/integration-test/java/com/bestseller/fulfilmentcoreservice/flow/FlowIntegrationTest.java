package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.fulfilmentcoreservice.AbstractIntegrationTest;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFulfillmentPartRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderReturnInfoRepository;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineExportedQueueConsumer;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.OrderState;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.Getter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.ZonedDateTime;
import java.util.function.Consumer;
import java.util.function.Function;

import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@Getter
abstract class FlowIntegrationTest extends AbstractIntegrationTest {

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.orderPartsRoutedConsumer-in-0.destination}")
    private String orderPartsRoutedTopic;

    @Value("${spring.cloud.stream.bindings.orderPartsCancelledConsumer-in-0.destination}")
    private String orderPartsCancelledTopic;

    @Value("${spring.cloud.stream.bindings.giftCardPurchasedConsumer-in-0.destination}")
    private String giftCardPurchasedTopic;

    @Value("classpath:orders/demandware_order.json")
    private Resource demandwareValidOrderPlacedJson;

    @Value("classpath:orders/tradebyte_order.json")
    private Resource tradebyteValidOrderPlacedJson;

    @Value("classpath:orders/demandware_virtual_order.json")
    private Resource virtualOrderPlacedJson;

    @Value("classpath:orderpartsrouted/demandware_order_parts_routed.json")
    private Resource orderPartsRoutedJson;

    @Value("classpath:orderpartsrouted/demandware_order_parts_routed_2_eans.json")
    private Resource orderPartsRoutedJsonWith2Eans;

    @Value("classpath:orderpartsrouted/demandware_order_parts_routed_part1.json")
    private Resource orderPartsRoutedPart1Json;

    @Value("classpath:orderpartsrouted/demandware_order_parts_routed_part2.json")
    private Resource orderPartsRoutedPart2Json;

    @Value("classpath:paymentStatusUpdated/demandware_payment_status_updated.json")
    private Resource paymentStatusUpdatedJson;

    @Value("classpath:orderlinereturned/order_line_returned.json")
    private Resource orderLineReturnedJson;

    @Value("classpath:orderpartscancelled/order_parts_cancelled.json")
    private Resource orderPartsCancelledJson;

    @Value("classpath:giftcardpurchased/gift_card_purchased.json")
    private Resource giftCardPurchasedJson;

    @Value("${spring.cloud.stream.bindings.paymentStatusUpdatedConsumer-in-0.destination}")
    private String paymentStatusUpdatedTopic;

    @Value("${spring.cloud.stream.bindings.orderLineReturnedConsumer-in-0.destination}")
    private String orderLineReturnedTopic;

    @Value("${spring.cloud.stream.bindings.orderLineDispatchedConsumer-in-0.destination}")
    private String orderLineDispatchedTopic;

    @Value("${spring.cloud.stream.bindings.orderLineAcknowledgedConsumer-in-0.destination}")
    private String orderLineAcknowledgedTopic;

    @Value("${spring.cloud.stream.bindings.orderLineExportedConsumer-in-0.destination}")
    private String orderLineExportedTopic;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderFulfillmentPartRepository orderFulfillmentPartRepository;

    @Autowired
    private OrderReturnInfoRepository orderReturnInfoRepository;

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private OrderLineExportedQueueConsumer orderLineExportedQueueConsumer;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @PersistenceContext
    private EntityManager entityManager;

    protected void produceOrderLineReturnedAndWaitForIt(String orderId) {
        var orderLineReturned = getObjectFromResources(orderLineReturnedJson, OrderLineReturned.class);
        orderLineReturned.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(orderLineReturnedTopic, orderLineReturned));

        Awaitility.await().until(
            () -> orderReturnInfoRepository.existsById(orderId));
    }

    protected void produceOrderPartsRoutedAndWaitForIt(String orderId) {
        var orderPartsRouted = getObjectFromResources(orderPartsRoutedJson, OrderPartsRouted.class);
        orderPartsRouted.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(orderPartsRoutedTopic, orderPartsRouted));

        // assert
        await().until(() ->
            !orderFulfillmentPartRepository
                .findOrderFulfillmentPartByOrderOrderId(orderPartsRouted.getOrderId())
                .isEmpty()
        );
    }

    private void produceOrderPartsRoutedAndWaitForIt(Resource orderPartsRoutedRawJson,
                                                     String orderId,
                                                     String fulfilmentNode) {
        var orderPartsRouted = getObjectFromResources(orderPartsRoutedRawJson, OrderPartsRouted.class);
        orderPartsRouted.setOrderId(orderId);
        if (fulfilmentNode != null) {
            orderPartsRouted.setFulfillmentNode(fulfilmentNode);
        }
        getKafkaProducer().send(new ProducerRecord<>(orderPartsRoutedTopic, orderPartsRouted));

        // assert
        await().until(() ->
            !orderFulfillmentPartRepository
                .findOrderFulfillmentPartByOrderOrderIdAndFulfillmentNode(orderPartsRouted.getOrderId(),
                    orderPartsRouted.getFulfillmentNode())
                .isEmpty()
        );
    }

    protected void produceOrderPartsRoutedWith2EansAndWaitForIt(String orderId) {
        var orderPartsRouted = getObjectFromResources(orderPartsRoutedJsonWith2Eans, OrderPartsRouted.class);
        orderPartsRouted.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(orderPartsRoutedTopic, orderPartsRouted));

        // assert
        await().until(() ->
            !orderFulfillmentPartRepository
                .findOrderFulfillmentPartByOrderOrderId(orderPartsRouted.getOrderId())
                .isEmpty()
        );
    }

    protected void produceOrderPartsRoutedPart1AndWaitForIt(String orderId) {
        produceOrderPartsRoutedAndWaitForIt(orderPartsRoutedPart1Json, orderId, null);
    }

    protected void produceOrderPartsRoutedPart1AndWaitForIt(String orderId, String fulfilmentNode) {
        produceOrderPartsRoutedAndWaitForIt(orderPartsRoutedPart1Json, orderId, fulfilmentNode);
    }

    protected void produceOrderPartsRoutedPart2AndWaitForIt(String orderId) {
        produceOrderPartsRoutedAndWaitForIt(orderPartsRoutedPart2Json, orderId, null);
    }

    protected void producePaymentStatusUpdatedAndWaitForIt(String orderId) {
        var paymentStatusUpdated = getObjectFromResources(paymentStatusUpdatedJson, PaymentStatusUpdated.class);
        paymentStatusUpdated.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(paymentStatusUpdatedTopic, paymentStatusUpdated));
        await().until(() ->
            orderRepository.findById(paymentStatusUpdated.getOrderId())
                .orElseThrow().isOrderPaymentAuthorised());
        await().until(() ->
            orderRepository.findById(paymentStatusUpdated.getOrderId())
                .orElseThrow().getMaxStatus().equals(OrderState.ROUTING));
    }

    protected void produceOrderLineDispatchedAndWaitForIt(String orderId, int lineNumber) {
        produceOrderLineDispatchedAndWaitForIt(orderId, lineNumber, null);
    }

    protected void produceOrderLineDispatchedAndWaitForIt(String orderId, int lineNumber, String warehouse) {
        var resource = new ClassPathResource(
            "orderlinedispatched/order_line_dispatched_line_number_%s.json".formatted(lineNumber));
        var orderLineDispatched = getObjectFromResources(resource, OrderLineDispatched.class);
        orderLineDispatched.setOrderId(orderId);
        if (warehouse != null) {
            orderLineDispatched.setWarehouse(warehouse);
        }
        getKafkaProducer().send(new ProducerRecord<>(orderLineDispatchedTopic, orderLineDispatched));
        await().until(() ->
            orderRepository.findById(orderLineDispatched.getOrderId())
                .orElseThrow().getMaxStatus().equals(OrderState.DISPATCHED));
    }

    protected void produceOrderLineAcknowledgedAndWaitForIt(String orderId, int lineNumber) {
        produceOrderLineAcknowledgedAndWaitForIt(orderId, lineNumber, null, null);
    }

    protected void produceOrderLineAcknowledgedAndWaitForIt(String orderId,
                                                            int lineNumber,
                                                            String warehouse,
                                                            Integer quantity) {
        var resource = new ClassPathResource(
            "orderlineacknowledged/order_line_ack_line_number_%s.json".formatted(lineNumber));
        var orderLineAcknowledged = getObjectFromResources(resource, OrderLineAcknowledged.class);
        orderLineAcknowledged.setOrderId(orderId);
        if (warehouse != null) {
            orderLineAcknowledged.setWarehouse(warehouse);
        }
        if (quantity != null) {
            orderLineAcknowledged.setQuantity(quantity);
        }
        getKafkaProducer().send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledged));
        await().until(() ->
            orderRepository.findById(orderLineAcknowledged.getOrderId())
                .orElseThrow().getMaxStatus().equals(OrderState.PROCESSING));
    }

    protected void produceOrderLineExportedAndWaitForIt(String orderId, int lineNumber, String warehouse) {
        produceOrderLineExported(orderId, lineNumber, warehouse, message ->
            transactionTemplate.execute(status -> {
                Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalStateException("Order not found: " + orderId));

                entityManager.refresh(order);

                return OrderState.EXPORTED.equals(order.getMaxStatus());
            })
        );

    }

    protected void produceOrderLineExportedAndWaitForIt(String orderId, int lineNumber) {
        produceOrderLineExportedAndWaitForIt(orderId, lineNumber, null);
    }

    protected void produceOrderLineExported(String orderId,
                                            int lineNumber,
                                            String warehouse,
                                            Function<OrderLineExported, Boolean> condition) {
        var resource = new ClassPathResource(
            "orderlineexported/order_line_exported_line_number_%s.json".formatted(lineNumber));
        var orderLineExported = getObjectFromResources(resource, OrderLineExported.class);
        if (warehouse != null) {
            orderLineExported.setWarehouse(warehouse);
        }
        orderLineExported.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(orderLineExportedTopic, orderLineExported));
        await().until(() -> condition.apply(orderLineExported));
    }

    protected ValidOrderPlaced produceValidOrderPlacedAndWaitForIt(Resource jsonResource,
                                                                   String orderId,
                                                                   boolean resetOrderDate) {
        var validOrderPlaced = getObjectFromResources(jsonResource, ValidOrderPlaced.class);
        if (resetOrderDate) {
            validOrderPlaced.setPlacedDate(ZonedDateTime.now().minusDays(1));
        }
        validOrderPlaced.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        return validOrderPlaced;
    }

    protected ValidOrderPlaced produceValidOrderPlacedAndWaitForIt(Resource jsonResource, String orderId) {
        return produceValidOrderPlacedAndWaitForIt(jsonResource, orderId, false);
    }

    protected void produceOrderPartsCancelledAndWaitForIt(String orderId) {
        var orderPartsCancelled = getObjectFromResources(orderPartsCancelledJson, OrderPartsCancelled.class);
        orderPartsCancelled.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(orderPartsCancelledTopic, orderPartsCancelled));

        // assert
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
                orderRepository.findById(orderPartsCancelled.getOrderId())
                    .ifPresentOrElse(order -> {
                        orderPartsCancelled.getOrderLines()
                            .forEach(orderLineCancelled -> {
                                Assertions.assertTrue(
                                    order.getOrderLines()
                                        .stream()
                                        .anyMatch(orderLine -> orderLine.getEan().equals(orderLineCancelled.getEan()))
                                );
                            });
                    }, Assertions::fail);
            }
        ));

    }

    protected void produceGiftCardPurchasedAndWaitForIt(String orderId) {
        var giftCardPurchased = getObjectFromResources(giftCardPurchasedJson, GiftCardPurchased.class);
        giftCardPurchased.setOrderId(orderId);
        getKafkaProducer().send(new ProducerRecord<>(giftCardPurchasedTopic, giftCardPurchased));

        // assert
        await().untilAsserted(() ->
            transactionTemplate.executeWithoutResult(status -> {
                Consumer<OrderLineQtyStatus> validateOrderLineQtyStatus = orderLineQtyStatus ->
                    Assertions.assertEquals(
                        OrderState.DISPATCHED,
                        orderLineQtyStatus.getOrderState(),
                        "OrderLineQtyStatus state mismatch"
                    );

                Consumer<OrderLine> validateOrderLine = orderLine ->
                    orderLine.getOrderLineQtyStatus().stream()
                        .filter(orderLineQtyStatus ->
                            orderLineQtyStatus.getIndexCol() == giftCardPurchased.getIndex())
                        .findFirst()
                        .ifPresentOrElse(
                            validateOrderLineQtyStatus,
                            () -> Assertions.fail("OrderLineQtyStatus not found for index: %s"
                                .formatted(giftCardPurchased.getIndex()))
                        );

                orderRepository.findById(giftCardPurchased.getOrderId())
                    .ifPresentOrElse(
                        order -> order.getOrderLines().stream()
                            .filter(orderLine ->
                                orderLine.getLineNumber() == giftCardPurchased.getOrderLineNumber())
                            .findFirst()
                            .ifPresentOrElse(
                                validateOrderLine,
                                () -> Assertions.fail("OrderLine not found for line number: %s"
                                    .formatted(giftCardPurchased.getOrderLineNumber()))
                            ),
                        () -> Assertions.fail("Order not found: %s".formatted(giftCardPurchased.getOrderId()))
                    );
            })
        );
    }
}
