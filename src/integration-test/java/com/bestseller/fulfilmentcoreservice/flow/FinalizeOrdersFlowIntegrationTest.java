package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.fulfilmentcoreservice.core.messaging.consumer.OrderFinalizedTestConsumer;
import com.bestseller.fulfilmentcoreservice.core.task.OrderFinalizerTask;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

class FinalizeOrdersFlowIntegrationTest extends FlowIntegrationTest {

    @Autowired
    private OrderFinalizerTask orderFinalizerTask;

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Test
    void run_flow() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);
        produceOrderLineExportedAndWaitForIt(orderId, 1);
        produceOrderLineExportedAndWaitForIt(orderId, 2);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 1);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 2);
        produceOrderLineDispatchedAndWaitForIt(orderId, 1);
        produceOrderLineDispatchedAndWaitForIt(orderId, 2);

        // act
        orderFinalizerTask.run();

        // assert
        await().until(() ->
            getOrderRepository().findById(orderId)
                .orElseThrow().isFinalized());

        await().until(() ->
            OrderFinalizedTestConsumer.MESSAGES
                .stream()
                .anyMatch(m -> m.getOrderId().equals(orderId))
        );

    }

    @Test
    void run_flow_noOrdersToBeFinalized() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);
        producePaymentStatusUpdatedAndWaitForIt(orderId);
        produceOrderPartsRoutedAndWaitForIt(orderId);

        // act
        orderFinalizerTask.run();

        // assert
        await().until(() ->
            !getOrderRepository().findById(orderId)
                .orElseThrow().isFinalized());
    }
}
