package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.logistics.statetransition.OrderState;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

class OrderStateTransitionFlowIntegrationTest extends FlowIntegrationTest {

    @Value("${spring.cloud.stream.bindings.orderLineExportedConsumer-in-0.destination}")
    private String orderLineExportedTopic;

    @Value("${spring.cloud.stream.bindings.orderLineAcknowledgedConsumer-in-0.destination}")
    private String orderLineAcknowledgedTopic;

    @Value("${spring.cloud.stream.bindings.orderPartsCancelledConsumer-in-0.destination}")
    private String orderPartsCancelledTopic;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Test
    void testFromPlacedToReturnedOrderStateTransitions() {
        // generate order ID
        var orderId = getOrderIdGenerator().generate();

        // produce ValidOrderPlaced message
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);

        // check the order is in PLACED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.PLACED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.PLACED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.PLACED));
        }));

        // produce PaymentStatusUpdated message with AUTHORIZED status
        producePaymentStatusUpdatedAndWaitForIt(orderId);

        // check the order is in ROUTING state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.ROUTING);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.ROUTING);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.ROUTING));
        }));

        // produce OrderPartsRouted message
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);

        // check the order is in ROUTED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.ROUTED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.ROUTED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.ROUTED));
        }));

        // generate OrderLineExported messages
        List<OrderLineExported> orderLineExportedMessages = transactionTemplate.execute(status -> {
                var order = getOrderRepository().findById(orderId).orElseThrow();
                return order.getOrderLines().stream()
                    .map(orderLine -> new OrderLineExported()
                        .withOrderId(order.getOrderId())
                        .withWarehouse(order.getOrderFulfillmentParts().getFirst().getFulfillmentNode())
                        .withEan(orderLine.getEan())
                        .withQuantity(orderLine.getOriginalQty())
                        .withExportDate(ZonedDateTime.now())
                    )
                    .toList();
            }
        );
        assertThat(orderLineExportedMessages).hasSize(2);

        // produce the first OrderLineExported message
        getKafkaProducer().send(new ProducerRecord<>(orderLineExportedTopic, orderLineExportedMessages.getFirst()));

        // check the order has EXPORTED max status
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.ROUTED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.EXPORTED);
            order.getOrderLines().stream()
                .filter(orderLine -> orderLine.getEan().equals(orderLineExportedMessages.getFirst().getEan()))
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.EXPORTED));
        }));

        // produce the second OrderLineExported message
        getKafkaProducer().send(new ProducerRecord<>(orderLineExportedTopic, orderLineExportedMessages.getLast()));

        // check the order has EXPORTED min status
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.EXPORTED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.EXPORTED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.EXPORTED));
        }));

        // generate OrderLineAcknowledged messages
        List<OrderLineAcknowledged> orderLineAcknowledgedMessages = transactionTemplate.execute(status -> {
                var order = getOrderRepository().findById(orderId).orElseThrow();
                return order.getOrderLines().stream()
                    .map(orderLine -> new OrderLineAcknowledged()
                        .withOrderId(order.getOrderId())
                        .withWarehouse(order.getOrderFulfillmentParts().getFirst().getFulfillmentNode())
                        .withEan(orderLine.getEan())
                        .withQuantity(orderLine.getOriginalQty())
                        .withAcknowledgementDate(ZonedDateTime.now())
                    )
                    .toList();
            }
        );
        assertThat(orderLineAcknowledgedMessages).hasSize(2);

        // produce OrderLineAcknowledged messages
        getKafkaProducer()
            .send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledgedMessages.getFirst()));
        getKafkaProducer()
            .send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledgedMessages.getLast()));

        // check the order has PROCESSING state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.PROCESSING);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.PROCESSING);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.PROCESSING));
        }));

        // produce OrderLineDispatched messages
        produceOrderLineDispatchedAndWaitForIt(orderId, 1);
        produceOrderLineDispatchedAndWaitForIt(orderId, 2);

        // check the order has DISPATCHED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.DISPATCHED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.DISPATCHED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.DISPATCHED));
        }));

        // produce OrderLineReturned message
        produceOrderLineReturnedAndWaitForIt(orderId);

        // check the order has RETURNED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.DISPATCHED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.RETURNED);
        }));
    }

    @Test
    void testFromPlacedToCancelledOrderStateTransitions() {
        // generate order ID
        var orderId = getOrderIdGenerator().generate();

        // produce ValidOrderPlaced message
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);

        // check the order is in PLACED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.PLACED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.PLACED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.PLACED));
        }));

        // generate OrderPartsCancelled message
        var orderPartsCancelled = transactionTemplate.execute(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            return new OrderPartsCancelled()
                .withOrderId(order.getOrderId())
                .withWarehouse("INGRAM_MICRO_NL")
                .withOrderLines(order.getOrderLines().stream()
                    .map(orderLine -> new OrderLine()
                        .withEan(orderLine.getEan())
                        .withLineNumber(orderLine.getLineNumber())
                        .withQuantity(orderLine.getOriginalQty())
                        .withCancelReason("MANUAL_CANCELLATION")
                    )
                    .toList()
                )
                .withCancellationDate(ZonedDateTime.now());
        });

        // produce OrderPartsCancelled message
        getKafkaProducer().send(new ProducerRecord<>(orderPartsCancelledTopic, orderPartsCancelled));

        // check the order is in CANCELLED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.CANCELLED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.CANCELLED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.CANCELLED));
        }));
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void testInvalidStateTransitionFromPlacedToProcessing() {
        // generate order ID
        var orderId = getOrderIdGenerator().generate();

        // produce ValidOrderPlaced message
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId);

        // check the order is in PLACED state
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.PLACED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.PLACED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.PLACED));
        }));

        // generate OrderLineAcknowledged messages
        List<OrderLineAcknowledged> orderLineAcknowledgedMessages = transactionTemplate.execute(status -> {
                var order = getOrderRepository().findById(orderId).orElseThrow();
                return order.getOrderLines().stream()
                    .map(orderLine -> new OrderLineAcknowledged()
                        .withOrderId(order.getOrderId())
                        .withWarehouse("INGRAM_MICRO_NL")
                        .withEan(orderLine.getEan())
                        .withQuantity(orderLine.getOriginalQty())
                        .withAcknowledgementDate(ZonedDateTime.now())
                    )
                    .toList();
            }
        );
        assertThat(orderLineAcknowledgedMessages).hasSize(2);

        // produce OrderLineAcknowledged messages
        getKafkaProducer()
            .send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledgedMessages.getFirst()));
        getKafkaProducer()
            .send(new ProducerRecord<>(orderLineAcknowledgedTopic, orderLineAcknowledgedMessages.getLast()));

        // check the order is still in PLACED state
        await().atMost(Duration.ofSeconds(5)).until(() -> true); // wait for the messages to be processed
        await().untilAsserted(() -> transactionTemplate.executeWithoutResult(status -> {
            var order = getOrderRepository().findById(orderId).orElseThrow();
            assertThat(order.getMinStatus()).isEqualTo(OrderState.PLACED);
            assertThat(order.getMaxStatus()).isEqualTo(OrderState.PLACED);
            order.getOrderLines().stream()
                .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
                .forEach(orderLine -> assertThat(orderLine.getOrderState()).isEqualTo(OrderState.PLACED));
        }));
    }

}
