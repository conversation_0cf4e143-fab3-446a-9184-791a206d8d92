package com.bestseller.fulfilmentcoreservice.flow;

import com.bestseller.fulfilmentcoreservice.core.task.ReturnProcessorTask;
import com.bestseller.fulfilmentcoreservice.tools.OrderIdGenerator;
import com.logistics.statetransition.OrderState;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@Transactional
class ReturnProcessFlowIntegrationTest extends FlowIntegrationTest {

    @Autowired
    private ReturnProcessorTask returnProcessorTask;

    @Autowired
    private OrderIdGenerator orderIdGenerator;

    @Test
    void returnProcess_flow() {
        // arrange
        var orderId = orderIdGenerator.generate();
        produceValidOrderPlacedAndWaitForIt(getDemandwareValidOrderPlacedJson(), orderId, true);
        producePaymentStatusUpdatedAndWaitForIt(orderId);
        produceOrderPartsRoutedWith2EansAndWaitForIt(orderId);
        produceOrderLineExportedAndWaitForIt(orderId, 1);
        produceOrderLineExportedAndWaitForIt(orderId, 2);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 1);
        produceOrderLineAcknowledgedAndWaitForIt(orderId, 2);

        produceOrderLineDispatchedAndWaitForIt(orderId, 1);
        produceOrderLineDispatchedAndWaitForIt(orderId, 2);

        produceOrderLineReturnedAndWaitForIt(orderId);

        // act
        returnProcessorTask.processReturn();

        // assert
        await().until(() ->
            getOrderRepository().findById(orderId)
                .orElseThrow().getMaxStatus().equals(OrderState.RETURN_PROCESSED));
    }

}
