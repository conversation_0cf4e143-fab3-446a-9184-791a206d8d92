{"shippingInformation": {"shippingAddress": {"addressLine1": "STREET", "addressLine3": "270901", "city": "BRECHT", "country": "BE", "firstName": "<PERSON>", "houseNumber": "16", "lastName": "<PERSON>", "phoneNumber": "*********", "zipcode": "2980"}, "shippingCharges": [], "parcelLocker": "BRECHT", "additionalInformation": [{"key": "carrierCustomerNumber", "value": "1"}]}, "customerInformation": {"billingAddress": {"addressLine1": "STREET", "addressLine2": "1L", "city": "Brecht", "country": "BE", "firstName": "<PERSON>", "houseNumber": "16", "lastName": "<PERSON>", "phoneNumber": "*********", "zipcode": "2980"}, "email": "<EMAIL>", "customerId": "1", "isEmployee": false, "state": "MEMBER_OF_CUSTOMER_CLUB", "customerLocale": "en_NL"}, "orderLines": [{"id": "8b1d33a4-b292-4c1a-9541-88fd570153b5", "ean": "5715424250751", "lineNumber": 1, "productName": "Parka", "quantity": 2, "retailPrice": 119.99, "discountedUnitPrice": 107.99, "vat": 0.21, "taxUnitPrice": 18.74, "virtualProduct": false, "discountedTotalPrice": 107.99, "brand": "JACK&JONES ESSENTIALS"}, {"id": "8cf6276e-4ed5-4c98-9956-64fa1375eda2", "ean": "5715426931214", "lineNumber": 2, "productName": "Rolkraag Gebreide trui", "quantity": 1, "retailPrice": 44.99, "discountedUnitPrice": 40.49, "vat": 0.21, "taxUnitPrice": 7.03, "virtualProduct": false, "discountedTotalPrice": 40.49, "brand": "JACK&JONES"}], "orderDetails": {"shippingMethod": "STANDARD", "orderCreationDate": "2023-10-23T21:05:37.263Z", "carrier": "BPOSTAPI", "carrierVariant": "PICKUP", "parcelShopType": "BPOSTAPI", "orderValue": 148.48, "orderType": "STANDARD", "checkout": "jj", "brandedShipping": "JJ", "market": "BSE-NL", "channel": "storefront", "additionalInformation": [{"key": "isoStoreId", "value": "1"}]}, "payments": [{"method": "ADYEN_CREDIT_CARD", "subMethod": "maestro", "pspReference": "QLD9GTESTINGPK52", "provider": "ADYEN", "amount": 98.48, "currency": "EUR", "state": "REVIEW", "mainPayment": true}, {"method": "OC_GIFTCARD", "pspReference": "8299209854511262910", "additionalReference": "c660727e-54bc-41ba-b740-e525d7b8778a", "provider": "OPTICARD", "amount": 50.0, "currency": "EUR", "giftCardNumber": "8299209854511262910", "giftCardAuthCode": "c660727e-54bc-41ba-b740-e525d7b8778a", "mainPayment": false}], "orderId": "", "ipAddress": "127.0.0.1", "store": "DEMANDWARE", "isTest": false, "placedDate": "2023-10-23T21:05:37.263Z", "announcedDeliveryDate": "2023-10-28", "orderPromotions": []}