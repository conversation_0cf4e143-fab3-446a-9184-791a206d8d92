{"customerInformation": {"billingAddress": {"addressLine1": "Brombærvej", "city": "Ølgod", "country": "DK", "state": "", "firstName": "Marge", "houseNumber": "89", "lastName": "<PERSON>", "phoneNumber": "********", "zipcode": "6870"}, "email": "<EMAIL>", "customerId": "**********", "customerLocale": "nl-nl"}, "fulfillmentNode": "INGRAM_MICRO_NL", "isTest": false, "payments": [{"name": "ACCOUNT", "subMethod": "KLARNA_INVOICE"}], "orderDetails": {"carrier": "POSTNL", "carrierVariant": "PICKUP", "parcelShopType": "POST_NL", "parcelShop": true, "shippingMethod": "STANDARD", "orderCreationDate": "2023-11-27T16:15:08.530Z", "orderValue": 350.0, "orderType": "STANDARD", "shippingFees": 0.0, "shippingFeesTaxPercentage": 0.21, "shippingFeesCancelled": false, "currency": "EUR", "checkout": "jj"}, "orderId": "OL**********", "orderLines": [{"discountValue": 30.0, "ean": "*************", "isGiftItem": false, "lineNumber": 1, "productName": "Parka", "quantity": 1, "retailPrice": 149.99, "taxPercentage": 0.21, "brand": "JACK&JONES ESSENTIALS", "promotionId": "OL**********"}], "orderPartNumber": 1, "totalOrderParts": 2, "placedDate": "2023-11-26T19:43:56.935Z", "store": "DEMANDWARE", "channel": "storefront", "brand": "ONLY", "shippingInformation": {"shippingAddress": {"addressLine1": "Brombærvej", "city": "Ølgod", "country": "DK", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "houseNumber": "89", "lastName": "<PERSON>", "phoneNumber": "********", "zipcode": "6870"}, "parcelLocker": "<PERSON><PERSON><PERSON>"}}