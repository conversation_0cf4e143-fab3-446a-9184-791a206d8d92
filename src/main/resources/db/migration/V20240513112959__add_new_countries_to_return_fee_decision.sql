INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
    SELECT customerReturnReason, 'AE', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'BG', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'CY', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'HR', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'LV', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'RO', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'SI', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'SK', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'