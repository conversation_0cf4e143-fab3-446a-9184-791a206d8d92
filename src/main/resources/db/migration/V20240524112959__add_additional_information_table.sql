CREATE TABLE AdditionalOrderInformation (
        id              bigint not null auto_increment,
        createdTS       datetime(6),
        lastModifiedTS  datetime(6),
        version         int,
        orderId         varchar(64),
        entityType      varchar(64),
        `key`           varchar(256),
        `value`         varchar(512),
    CONSTRAINT PK_AdditionalInformation primary key (id),
    UNIQUE KEY `KEY_orderId_entityType_key` (`orderId`, `entityType`, `key`),
    INDEX `IX_orderId_index` (`orderId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;