CREATE TABLE ReturnFeeDecision
(
    customerReturnReason varchar(50) not null,
    ecomCountryCode      varchar(4)  not null,
    chargeReturnFee      bit(1)      not null,
    constraint PK_ReturnFeeDecision primary key (customerReturnReason, ecomCountryCode)
) engine = InnoDB;

INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_PURCHASE_1', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_OF_MIND_2', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('WRONG_OR_FAULTY_3', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DOESNT_FIT_4', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERENT_MATERIAL_5', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_BIG_6', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_LONG_7', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SMALL_8', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TOO_SHORT_9', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('DIFFERS_FROM_IMAGE_10', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_SMALL_11', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_TOO_BIG_12', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_FAULTY_13', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_IS_NOT_ON_ORDER_14', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_WAS_NOT_IN_THE_PARCEL_16', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('CHANGED_MIND_17', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('ITEM_DID_NOT_MEET_EXPECTATIONS_18', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('OTHER_REASONS_19', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REASON_NOT_STATED_20', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_ITEM_21', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'BE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'CA', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'CH', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'CZ', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'DE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'EE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'ES', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'FI', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'FR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'GB', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'GR', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'HU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'IE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'IT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'LT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'LU', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'NL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'NO', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'PL', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'PT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'ROTW', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_RETURN_FEE_22', 'US', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MANUAL_REFUND_NO_RETURN_FEE_23', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('MULTIPLE_ORDERS_IN_ONE_PARCEL_24', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('REFUSED_TO_ACCEPT_THE_PARCEL_31', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'AT', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'DK', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'SE', true);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('UNDELIVERABILITY_32', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('TRANSPORT_DAMAGE_33', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('LOST_OUTGOING_SHIPMENT_34', 'US', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'AT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'BE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'CA', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'CH', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'CZ', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'DE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'DK', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'EE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'ES', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'FI', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'FR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'GB', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'GR', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'HU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'IE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'IT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'LT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'LU', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'NL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'NO', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'PL', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'PT', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'ROTW', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'SE', false);
INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
VALUES ('RETURNED_IN_STORE_35', 'US', false);
