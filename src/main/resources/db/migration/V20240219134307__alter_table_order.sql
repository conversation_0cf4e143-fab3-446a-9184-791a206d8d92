create table UserDeviceInfo
(
    id       bigint    not null auto_increment,
    platform           varchar(255) not null,
    originalScopeId    varchar(15),
    appVersion         varchar(100),
    locale             varchar(10),
    orderId            varchar(32) null,
    createdTS          datetime(6),
    lastModifiedTS     datetime(6),
    version            integer,
    constraint PK_UserDeviceInfo primary key (id)
) engine = InnoDB;

alter table Orders
    add column orderPaymentAuthorised BOOLEAN;

alter table UserDeviceInfo
    add constraint FK_UserDeviceInfo_orderId_Orders foreign key (orderId) references Orders (orderId);

alter table PartnerChannel rename column id to channelId;
alter table OrderFulfillmentPart add column holdFromRouting BOOLEAN;
ALTER TABLE OrderLine alter column virtualProduct SET DEFAULT 0;
