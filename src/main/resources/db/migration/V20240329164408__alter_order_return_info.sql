alter table OrderStatusUpdateInfo
    add column customerReturnReason varchar(50),
    add column returnType           varchar(50),
    drop column returnShipmentId,
    add column returnShipmentId     varchar(50),
    add column returnDate           DATE,
    add column refundRequestId      char(36);

alter table OrderFulfillmentPart
    add column dispatchDate DATE;

alter table Orders
    add column returnFeeCharged bit(1) not null;

create table OrderReturnInfo
(
    orderId          varchar(50) not null,
    latestReturnDate date        not null,
    constraint PK_OrderReturnInfo primary key (orderId)
) engine = InnoDB;