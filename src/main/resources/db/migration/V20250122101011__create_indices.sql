CALL create_index_if_not_exists('Orders', 'idx_orders_orderDate', 'orderDate');

CALL create_index_if_not_exists('Orders', 'idx_orders_platform', 'platform');

CALL create_index_if_not_exists('Orders', 'idx_orders_minStatus', 'minStatus');

CALL create_index_if_not_exists('Orders', 'idx_orders_maxStatus', 'maxStatus');

CALL create_index_if_not_exists('OrderStatusUpdateInfo', 'idx_orderStatusUpdateInfo_refundRequestId', 'refundRequestId');

CALL create_index_if_not_exists('Orders', 'idx_orders_minStatus_isFinalized_orderDate', 'minStatus, isFinalized, orderDate');