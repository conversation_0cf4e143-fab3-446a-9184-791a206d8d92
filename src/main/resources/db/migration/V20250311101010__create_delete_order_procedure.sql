CREATE PROCEDURE delete_order_safely(IN p_orderIds TEXT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    DELETE oqs FROM OrderLineQtyStatus oqs
      JOIN OrderLine ol ON oqs.orderLineId = ol.id
     WHERE FIND_IN_SET(ol.orderId, p_orderIds);

    DELETE toqs FROM TradebyteOrderLineQtyStatus toqs
      JOIN OrderLineQtyStatus oqs ON oqs.tradebyteOrderLineQtyStatusId = toqs.id
      JOIN OrderLine ol ON oqs.orderLineId = ol.id
     WHERE FIND_IN_SET(ol.orderId, p_orderIds);

    DELETE FROM OrderLine WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderStatusUpdateInfo WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderFulfillmentPart WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderBlock WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderReturnInfo WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderFilter WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM AdditionalOrderInformation WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM Orders WHERE FIND_IN_SET(orderId, p_orderIds);

    COMMIT;
END;