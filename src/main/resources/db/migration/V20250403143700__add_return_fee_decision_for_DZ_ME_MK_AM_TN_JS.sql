INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
SELECT customerReturnReason, 'DZ', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'ME', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'MK', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'AM', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'TN', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'JO', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL';