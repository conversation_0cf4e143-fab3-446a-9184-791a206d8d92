create table OrderStatusUpdateInfo
(
    id      bigint   not null auto_increment,
    returnShipmentId   varchar(255) not null,
    createdTS                    datetime(6),
    lastModifiedTS               datetime(6),
    constraint PK_OrderStatusUpdateInfo primary key (id)
) engine = InnoDB;

alter table OrderLineQtyStatus
    add column orderStatusUpdateInfoId bigint null;

alter table OrderLineQtyStatus
    add constraint FK_OrderLineQtyStatus_orderStatusUpdateInfoId_OrderStatusUpdate foreign key (orderStatusUpdateInfoId) references OrderStatusUpdateInfo (id);