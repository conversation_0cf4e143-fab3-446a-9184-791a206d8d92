CREATE TABLE queue_tasks
(
    id                BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    queue_name        VARCHAR(127) NOT NULL,
    payload           TEXT,
    created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    next_process_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempt           INT       DEFAULT 0,
    reenqueue_attempt INT       DEFAULT 0,
    total_attempt     INT       DEFAULT 0
);

CREATE INDEX queue_tasks_name_time_desc_idx
    ON queue_tasks (queue_name, next_process_at, id DESC);
