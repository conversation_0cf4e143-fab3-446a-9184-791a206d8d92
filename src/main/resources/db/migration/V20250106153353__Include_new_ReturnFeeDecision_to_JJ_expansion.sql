INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
SELECT customerReturnReason, 'EG', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'MA', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'MC', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'SA', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL'
UNION ALL
SELECT customerReturnReason, 'KW', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL';
SELECT customerReturnReason, 'QA', chargeReturnFee
FROM ReturnFeeDecision
WHERE ecomCountryCode = 'NL';