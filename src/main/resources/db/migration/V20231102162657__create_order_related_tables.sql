create table Address
(
    id              bigint not null auto_increment,
    createdTS       datetime(6),
    lastModifiedTS  datetime(6),
    version         integer,
    address1        varchar(32),
    address2        varchar(32),
    address3        varchar(25),
    carrierVariant  varchar(20),
    city            varchar(24),
    countryCode     varchar(4),
    deliveryOption  varchar(100),
    firstName       varchar(32),
    houseNumber     varchar(32),
    houseNumberExt  varchar(10),
    lastName        varchar(26),
    parcelLocker    varchar(40),
    parcelShop      bit,
    parcelShopType  varchar(20),
    phone           varchar(255),
    physicalStoreId varchar(20),
    postCode        varchar(10),
    salutation      varchar(10),
    state           varchar(25),
    constraint PK_Address primary key (id)
) engine = InnoDB;

create table Customer
(
    id                 bigint       not null auto_increment,
    createdTS          datetime(6),
    lastModifiedTS     datetime(6),
    version            integer,
    customerId         varchar(255) not null,
    dateOfBirth        varchar(15),
    email              varchar(100),
    employeeId         varchar(10),
    externalCustomerId varchar(32),
    legalIdentifier    varchar(30),
    name               varchar(100),
    state              varchar(15),
    type               varchar(15),
    constraint PK_Customer primary key (id)
) engine = InnoDB;

create table OrderCharge
(
    id                  bigint      not null auto_increment,
    createdTS           datetime(6),
    lastModifiedTS      datetime(6),
    version             integer,
    campaignId          varchar(256),
    costPrice           decimal(10, 2),
    couponId            varchar(256),
    ean                 varchar(24),
    name                varchar(255),
    openQty             integer,
    originalQty         integer,
    partnerReference    varchar(256),
    promotionId         varchar(256),
    skuId               varchar(64),
    standardRetailPrice decimal(10, 2),
    taxRate             decimal(10, 4),
    type                varchar(20),
    vatClassId          varchar(100),
    cancelled           bit,
    chargeTotalId       bigint,
    orderId             varchar(50) null,
    indexCol            integer,
    constraint PK_OrderCharge primary key (id)
) engine = InnoDB;

create table OrderEntryAmount
(
    id                           bigint not null auto_increment,
    createdTS                    datetime(6),
    lastModifiedTS               datetime(6),
    version                      integer,
    grossDiscountedTotal         decimal(10, 2),
    grossDiscountedUnitPrice     decimal(10, 2),
    grossRetailUnitPrice         decimal(10, 2),
    lineVAT                      decimal(10, 2),
    originalGrossDiscountedTotal decimal(10, 2),
    unitDiscount                 decimal(10, 2),
    unitVAT                      decimal(10, 2),
    constraint PK_OrderEntryAmount primary key (id)
) engine = InnoDB;

create table OrderLine
(
    id                     bigint      not null auto_increment,
    createdTS              datetime(6),
    lastModifiedTS         datetime(6),
    version                integer,
    campaignId             varchar(256),
    costPrice              decimal(10, 2),
    couponId               varchar(256),
    ean                    varchar(24),
    name                   varchar(255),
    openQty                integer,
    originalQty            integer,
    partnerReference       varchar(256),
    promotionId            varchar(256),
    skuId                  varchar(64),
    standardRetailPrice    decimal(10, 2),
    taxRate                decimal(10, 4),
    type                   varchar(20),
    vatClassId             varchar(100),
    bonusProduct           bit         not null,
    brandDescription       varchar(63),
    channelScopeID         varchar(255),
    lineNumber             integer     not null,
    size                   varchar(255),
    cancelledLineTotalId   bigint,
    openLineTotalId        bigint,
    lineDiscountId         bigint,
    orderId                varchar(50) null,
    constraint PK_OrderLine primary key (id)
) engine = InnoDB;

create table OrderLineDiscount
(
    id             bigint not null auto_increment,
    createdTS      datetime(6),
    lastModifiedTS datetime(6),
    version        integer,
    discountAmount decimal(10, 2),
    discountRate   varchar(4),
    constraint PK_OrderLineDiscount primary key (id)
) engine = InnoDB;

create table OrderLineQtyStatus
(
    id                            bigint  not null auto_increment,
    createdTS                     datetime(6),
    lastModifiedTS                datetime(6),
    version                       integer,
    indexCol                      integer not null,
    orderState                    varchar(50),
    prevStatus                    varchar(50),
    returnPhysicalStoreId         varchar(255),
    orderLineId                   bigint,
    tradebyteOrderLineQtyStatusId bigint,
    constraint PK_OrderLineQtyStatus primary key (id)
) engine = InnoDB;

alter table Orders
    add column brand               varchar(2),
    add column channelType         varchar(15),
    add column checkoutScopeId     varchar(255),
    add column currency            varchar(3)  not null,
    add column externalOrderId     varchar(32),
    add column market              varchar(15),
    add column maxStatus           varchar(50) not null,
    add column minStatus           varchar(50) not null,
    add column platform            varchar(15) not null,
    add column shippingMethod      varchar(20) not null,
    add column test                bit         not null,
    add column billingAddressId    bigint,
    add column customerId          bigint,
    add column openTotalId         bigint,
    add column partnerChannelId    varchar(20),
    add column shippingAddressId   bigint,
    add column billToMatchesShipTo bit         not null;


create table OverallTotal
(
    id                           bigint not null auto_increment,
    createdTS                    datetime(6),
    lastModifiedTS               datetime(6),
    version                      integer,
    grossDiscountedTotal         decimal(10, 2),
    grossSubTotal                decimal(10, 2),
    originalGrossDiscountedTotal decimal(10, 2),
    vat                          decimal(10, 2),
    constraint PK_OverallTotal primary key (id)
) engine = InnoDB;

alter table PartnerChannel
    add column createdTS      datetime(6),
    add column lastModifiedTS datetime(6);


create table TradebyteOrderLineQtyStatus
(
    id                  bigint not null auto_increment,
    createdTS           datetime(6),
    lastModifiedTS      datetime(6),
    version             integer,
    lastTradebyteStatus varchar(50),
    originalLineNumber  integer,
    constraint PK_TradebyteOrderLineQtyStatus primary key (id)
) engine = InnoDB;


alter table OrderCharge
    add constraint FK_OrderCharge_chargeTotalId_OrderEntryAmount foreign key (chargeTotalId) references OrderEntryAmount (id),
    add constraint FK_OrderCharge_orderId_Orders foreign key (orderId) references Orders (orderId);


alter table OrderLine
    add constraint FK_OrderLine_cancelledLineTotalId_OrderEntryAmount foreign key (cancelledLineTotalId) references OrderEntryAmount (id),
    add constraint FK_OrderLine_openLineTotalId_OrderEntryAmount foreign key (openLineTotalId) references OrderEntryAmount (id),
    add constraint FK_OrderLine_lineDiscountId_OrderLineDiscount foreign key (lineDiscountId) references OrderLineDiscount (id),
    add constraint FK_OrderLine_orderId_Orders foreign key (orderId) references Orders (orderId);


alter table OrderLineQtyStatus
    add constraint FK_OrderLineQtyStatus_orderLineId_OrderLine foreign key (orderLineId) references OrderLine (id),
    add constraint FK_OrderLineQtyStatus_tbOrderLineStatusId_TBOrderLineStatus foreign key (tradebyteOrderLineQtyStatusId) references TradebyteOrderLineQtyStatus (id);

alter table Orders
    add constraint FK_Orders_billingAddressId_Address foreign key (billingAddressId) references Address (id),
    add constraint FK_Orders_customerId_Customer foreign key (customerId) references Customer (id),
    add constraint FK_Orders_openTotalId_OverallTotal foreign key (openTotalId) references OverallTotal (id),
    add constraint FK_Orders_partnerChannelId_PartnerChannel foreign key (partnerChannelId) references PartnerChannel (id),
    add constraint FK_Orders_shippingAddressId_Address foreign key (shippingAddressId) references Address (id);