CREATE OR REPLACE VIEW vw_brand AS
SELECT 'MB' AS code, 'general' AS demandwareName, NULL AS brandAbbreviation, 'Multibrand' AS description
UNION ALL
SELECT 'JJ', 'jack-jones', 'JJ', '<PERSON> & <PERSON>'
UNION ALL
SELECT 'MM', 'mama-licious', 'MM', 'Mamalicious'
UNION ALL
SELECT 'NI', 'name-it', 'NI', 'Name it'
UNION ALL
SELECT 'OC', 'object-collectors-item', 'OC', 'Object'
UNION ALL
SELECT 'OF', 'outfitters-nation', 'OF', 'Outfitters nation'
UNION ALL
SELECT 'ON', 'only', 'ON', 'Only'
UNION ALL
SELECT 'PC', 'pieces', 'PC', 'Pieces'
UNION ALL
SELECT 'SL', 'selected', 'SL', 'Selected'
UNION ALL
SELECT 'VL', 'vila', 'VL', 'Vila'
UNION ALL
SELECT 'VM', 'vero-moda', 'VM', 'Vero Moda'
UNION ALL
SELECT 'JL', 'jlindeberg', 'JL', '<PERSON><PERSON>'
UNION ALL
SELECT 'JR', 'junarose', '<PERSON>', '<PERSON>arose'
UNION ALL
SELECT 'GV', NULL, NULL, 'All gift-vouchers'
UNION ALL
SELECT 'BC', 'bestseller-com', NULL, 'Bestseller'
UNION ALL
SELECT 'LP', 'little-pieces', NULL, 'Little Pieces'
UNION ALL
SELECT 'YS', 'yas', 'YS', 'Yas'
UNION ALL
SELECT 'NM', 'noisy-may', 'NM', 'Noisy'
UNION ALL
SELECT 'OS', 'only-and-sons', 'OS', 'Only & sons'
UNION ALL
SELECT 'AD', 'adpt', NULL, 'Adapt'
UNION ALL
SELECT 'PT', 'produkt', NULL, 'Produkt'
UNION ALL
SELECT 'BI', 'bianco', NULL, 'Bianco'
UNION ALL
SELECT 'OH', 'outfit-hustlers', NULL, 'Outfit Hustlers'
UNION ALL
SELECT 'JX', 'jjxx', 'JX', 'JJXX'
UNION ALL
SELECT 'IQ', 'iiqual', 'IQ', 'IIQUAL'
UNION ALL
SELECT 'TF', 'thefounded', NULL, 'The Founded'
UNION ALL
SELECT 'LA', 'lilatelier', 'LA', 'Lil Atelier'
UNION ALL
SELECT 'RD', 'royal-denim-division', NULL, 'Royal Denim Division'
UNION ALL
SELECT 'AP', 'aprel', NULL, 'Aprel'
UNION ALL
SELECT 'AN', 'annarr', NULL, 'Annarr'
UNION ALL
SELECT 'TB', 'tradebyte', 'TB', 'TradeByte'
UNION ALL
SELECT 'TS', 'twosoon', 'TS', 'TwoSoon'
UNION ALL
SELECT 'RE', 'rougeedit', 'RE', 'RougeEdit';