CREATE OR REPLACE VIEW vw_orders AS
SELECT
    o.orderId,
    o.externalOrderId,
    o.orderDate,
    o.market,
    o.partnerChannelId,
    o.channelType,
    o.shippingMethod,
    o.platform AS store,
    case
        when platform = 'DEMANDWARE' then 'STANDARD'
        when platform = 'TRADEBYTE' then 'MARKETPLACE'
        when platform is null then 'UNKNOWN'
        end as orderType,
    o.checkOutScopeID,
    o.brand as brandCode,
    o.currency,
    o.createdTS,
    o.lastModifiedTS,
    o.customerId customerPk,
    o.userDeviceInfoId userDeviceInfoId,
    o.minStatus,
    o.maxStatus,
    o.isoStoreId,
    ba.salutation AS billingSalutation,
    ba.firstName AS billingFirstName,
    ba.lastName AS billingLastName,
    ba.address1 AS billingAddress1,
    ba.address2 AS billingAddress2,
    ba.address3 AS billingAddress3,
    ba.city billingCity,
    ba.postCode AS billingPostCode,
    ba.phone AS billingPhone,
    ba.countryCode AS billingCountryCode,
    ba.state AS billingState,
    ba.houseNumber AS billingHouseNumber,
    ba.houseNumberExt AS billingHouseNumberExt,
    sa.countryCode  AS shippingCountryCode,
    sa.deliveryOption shippingDeliveryOption,
    sa.carrierVariant shippingCarrierVariant,
    sa.state AS shippingState,
    br.description as brandDescription
 FROM Orders o
 JOIN Address ba ON ba.id = o.billingAddressId
 LEFT OUTER JOIN Address sa ON sa.id = o.shippingAddressId
 LEFT OUTER JOIN vw_brand br ON br.code = o.brand;