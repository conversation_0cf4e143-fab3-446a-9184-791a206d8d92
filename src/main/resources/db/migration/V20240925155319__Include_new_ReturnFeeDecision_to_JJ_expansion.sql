INSERT INTO ReturnFeeDecision(customerReturnReason, ecomCountryCode, chargeReturnFee)
    SELECT customerReturnReason, 'RS', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'BA', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'AD', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'MT', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL'
    UNION ALL
    SELECT customerReturnReason, 'AL', chargeReturnFee
    FROM ReturnFeeDecision
    WHERE ecomCountryCode = 'NL';