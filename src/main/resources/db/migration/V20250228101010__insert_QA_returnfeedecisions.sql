INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'CHANGED_MIND_17', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'CHANGED_OF_MIND_2', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'DIFFERENT_MATERIAL_5', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'DIFFERS_FROM_IMAGE_10', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'DOESNT_FIT_4', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_DID_NOT_MEET_EXPECTATIONS_18', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_IS_FAULTY_13', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_IS_NOT_ON_ORDER_14', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_IS_TOO_BIG_12', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_IS_TOO_SMALL_11', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'ITEM_WAS_NOT_IN_THE_PARCEL_16', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'LOST_ITEM_21', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'LOST_OUTGOING_SHIPMENT_34', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'MANUAL_REFUND_NO_RETURN_FEE_23', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'MANUAL_REFUND_RETURN_FEE_22', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'MULTIPLE_ORDERS_IN_ONE_PARCEL_24', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'MULTIPLE_PURCHASE_1', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'OTHER_REASONS_19', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'REASON_NOT_STATED_20', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'REFUSED_TO_ACCEPT_THE_PARCEL_31', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'RETURNED_IN_STORE_35', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'TOO_BIG_6', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'TOO_LONG_7', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'TOO_SHORT_9', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'TOO_SMALL_8', true);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'TRANSPORT_DAMAGE_33', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'UNDELIVERABILITY_32', false);
INSERT INTO ReturnFeeDecision(ecomCountryCode, customerReturnReason, chargeReturnFee) VALUES ('QA', 'WRONG_OR_FAULTY_3', false);