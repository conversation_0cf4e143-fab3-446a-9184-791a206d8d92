alter table Customer
    drop column legalIdentifier,
    drop column dateOfBirth;

update Customer set state = 'UNDETERMINED';

alter table Customer
    modify column state varchar(15) not null default 'UNDETERMINED';

alter table OrderLine
    drop foreign key FK_OrderLine_cancelledLineTotalId_OrderEntryAmount,
    drop column cancelledLineTotalId,
    drop column size,
    drop column costPrice,
    drop column channelScopeId;

alter table OrderLineQtyStatus
    drop column returnPhysicalStoreId;

alter table OrderCharge
    drop column partnerReference,
    drop column skuId;

alter table Orders
    drop foreign key FK_Orders_openTotalId_OverallTotal,
    drop column openTotalId;

alter table OrderLine
    drop foreign key FK_OrderLine_openLineTotalId_OrderEntryAmount,
    drop foreign key FK_OrderLine_lineDiscountId_OrderLineDiscount,
    drop column openLineTotalId,
    drop column lineDiscountId,
    drop column standardRetailPrice,
    drop column taxRate;

drop table OrderCharge;
drop table OrderLineDiscount;
drop table OrderEntryAmount;
drop table OverallTotal;