info:
  app:
    name: Fulfilment Core Service
    version: ${dd.version:local}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,bindings
  # Datadog
  datadog:
    metrics:
      export:
        api-key: ${DD_API_KEY}
        application-key: ${DD_APPLICATION_KEY}
        enabled: ${DD_ENABLED:false}
  metrics:
    tags:
      service: fcs
      env: ${spring.profiles.active}

spring:
  main:
    banner-mode: OFF
  jackson:
    deserialization:
      fail-on-missing-external-type-id-property: false # Allows to fix problem with nullable payload field in PaymentStatusUpdated event
  datasource:
    url: ${fs.db.url:*****************************************}
    driverClassName: com.mysql.cj.jdbc.Driver
    username: ${fs.db.username}
    password: ${fs.db.password}
  flyway:
    password: ${fs.db.migrations.password}
    user: ${fs.db.migrations.username}
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    open-in-view: true

  cloud:
    stream:
      default:
        group: FulfilmentCoreService
        producer:
          header-mode: none
      kafka:
        binder:
          brokers: ${kafka.broker.list}
          configuration:
            security:
              protocol: PLAINTEXT
        bindings:
          default:
            consumer:
              ack-mode: record
        default:
          content-type: application/json
          consumer:
            start-offset: latest
      bindings:
        default:
          content-type: application/json
        #Producer kafka topic
        orderStatusUpdated-out-0:
          destination: OrderStatusUpdated
          producer:
            partition-key-expression: payload.orderId
            partition-count: 2
        orderForFulfilment-out-0:
          destination: OrderForFulfillment
        orderStatusUpdateTradeByte-out-0:
          destination: OrderStatusUpdateTradebyte
        orderReturned-out-0:
          destination: OrderReturned
        refundCreationRequested-out-0:
          destination: RefundCreationRequested
        orderCancelled-out-0:
          destination: OrderCancelled
        refundRequested-out-0:
          destination: RefundRequested
        orderFinalized-out-0:
          destination: OrderFinalized
        orderManualUpdate-out-0:
          destination: OrderManualUpdate
        #Consumer kafka topic
        validOrderPlacedConsumer-in-0:
          destination: ValidOrderPlaced
        paymentStatusUpdatedConsumer-in-0:
          destination: PaymentStatusUpdated
        orderPartsRoutedConsumer-in-0:
          destination: OrderPartsRouted
        orderLineExportedConsumer-in-0:
          destination: OrderLineExported
        orderLineAcknowledgedConsumer-in-0:
          destination: OrderLineAcknowledged
        orderLineDispatchedConsumer-in-0:
          destination: OrderLineDispatched
        orderLineReturnedConsumer-in-0:
          destination: OrderLineReturned
        orderReturnedInStoreConsumer-in-0:
          destination: OrderReturnedInStore
        orderPartsCancelledConsumer-in-0:
          destination: OrderPartsCancelled
        giftCardPurchasedConsumer-in-0:
          destination: GiftCardPurchased
    function:
      definition: >
        validOrderPlacedConsumer;
        orderPartsRoutedConsumer;
        orderLineExportedConsumer;
        orderLineAcknowledgedConsumer;
        paymentStatusUpdatedConsumer;
        orderLineDispatchedConsumer;
        orderLineReturnedConsumer;
        orderReturnedInStoreConsumer;
        orderPartsCancelledConsumer;
        giftCardPurchasedConsumer

springdoc:
  swagger-ui:
    path: /

server:
  error:
    include-message: always
  tomcat:
    mbeanregistry:
      enabled: true

scheduled-tasks:
  return-processor:
    cron: 0 0 17,23 * * * # 5pm and 11pm
  order-finalizer:
    cron: 0 6/10 * * * * # Every 10 minutes, starting at 6 minutes past the hour
    query-page-size: 100


days-for-free-return-policy: 28

countries-excluded-from-refund-shipment-fee: US

database-queue:
  update-payment-status-queue-properties:
    queue-location:
      queue-id: update-payment-status-queue
  order-parts-cancelled-queue-properties:
    queue-location:
      queue-id: order-parts-cancelled-queue
  order-line-acknowledged-queue-properties:
    queue-location:
      queue-id: order-line-acknowledged-queue
  order-line-dispatched-queue-properties:
    queue-location:
      queue-id: order-line-dispatched-queue
  order-line-exported-queue-properties:
    queue-location:
      queue-id: order-line-exported-queue
  order-line-returned-queue-properties:
    queue-location:
      queue-id: order-line-returned-queue
  order-returned-in-store-queue-properties:
    queue-location:
      queue-id: order-returned-in-store-queue
  order-parts-routed-queue-properties:
    queue-location:
      queue-id: order-parts-routed-queue
  gift-card-purchased-queue-properties:
    queue-location:
      queue-id: gift-card-purchased-queue
  order-cancelled-producer-queue-properties:
    queue-location:
      queue-id: order-cancelled-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-finalized-producer-queue-properties:
    queue-location:
      queue-id: order-finalized-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-for-fulfilment-producer-queue-properties:
    queue-location:
      queue-id: order-for-fulfilment-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-manual-update-producer-queue-properties:
    queue-location:
      queue-id: order-manual-update-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-returned-producer-queue-properties:
    queue-location:
      queue-id: order-returned-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-status-updated-producer-queue-properties:
    queue-location:
      queue-id: order-status-updated-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  order-status-update-tradebyte-producer-queue-properties:
    queue-location:
      queue-id: order-status-update-tradebyte-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  refund-creation-requested-producer-queue-properties:
    queue-location:
      queue-id: refund-creation-requested-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  refund-requested-producer-queue-properties:
    queue-location:
      queue-id: refund-requested-producer-queue
    poll-settings:
      between-task-timeout: PT0.01S
  valid-order-placed-queue-properties:
    queue-location:
      queue-id: valid-order-placed-queue

gateway:
  services:
    payment-service:
      url: ${PMS_API_URL}
      auth: true
      username: ${PMS_API_USERNAME}
      password: ${PMS_API_PASSWORD}

pms:
  webclient:
    backoff:
      delay: PT5s