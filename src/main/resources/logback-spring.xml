<configuration>
    <springProfile name="acc,prod">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                    <maxDepthPerThrowable>2</maxDepthPerThrowable>
                </throwableConverter>
            </encoder>
        </appender>

        <logger name="org.springframework" level="WARN"/>
        <logger name="datadog.trace" level="WARN"/>
        <logger name="org.datadog" level="WARN"/>

        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
    <springProfile name="local,docker,dev,default">
        <!-- Define the log pattern property -->
        <property name="CONSOLE_LOG_PATTERN"
                  value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%highlight(%-5level)] [%thread] %cyan(%logger{36}) - %msg%n%rEx"/>

        <!-- Define the CONSOLE appender and use the pattern -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </encoder>
        </appender>

        <include resource="org/springframework/boot/logging/logback/base.xml"/>

        <logger name="org.springframework" level="WARN" />
        <logger name="org.apache.kafka" level="WARN" />
        <logger name="org.hibernate" level="WARN"/>

        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>