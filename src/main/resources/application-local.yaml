credentials:
  admin:
    username: admin
    password: "{noop}admin"

spring:
  datasource:
    url: *******************************
    username: fcs
    password: fcs
  flyway:
    password: fcs
    user: fcs
  cloud:
    stream:
      kafka:
        default:
          consumer:
            start-offset: earliest
      bindings:
        default:
          content-type: application/json
        #Consumer kafka topic
        orderStatusUpdatedTestConsumer-in-0:
          destination: OrderStatusUpdated
        orderStatusUpdateTradeByteTestConsumer-in-0:
          destination: OrderStatusUpdateTradebyte
        orderManualUpdateTestConsumer-in-0:
          destination: OrderManualUpdate
        orderCancelledTestConsumer-in-0:
          destination: OrderCancelled
        validOrderPlacedConsumer-in-0:
          destination: ValidOrderPlaced
        paymentStatusUpdatedConsumer-in-0:
          destination: PaymentStatusUpdated
        orderPartsRoutedConsumer-in-0:
          destination: OrderPartsRouted
        orderLineExportedConsumer-in-0:
          destination: OrderLineExported
        orderLineAcknowledgedConsumer-in-0:
          destination: OrderLineAcknowledged
        orderLineDispatchedConsumer-in-0:
          destination: OrderLineDispatched
        orderLineReturnedConsumer-in-0:
          destination: OrderLineReturned
        orderReturnedInStoreConsumer-in-0:
          destination: OrderReturnedInStore
        orderFinalizedTestConsumer-in-0:
          destination: OrderFinalized
        orderPartsCancelledConsumer-in-0:
          destination: OrderPartsCancelled
    function:
      definition: >
        orderStatusUpdatedTestConsumer;
        orderStatusUpdateTradeByteTestConsumer;
        orderManualUpdateTestConsumer;
        orderCancelledTestConsumer;
        validOrderPlacedConsumer;
        orderPartsRoutedConsumer;
        orderLineExportedConsumer;
        orderLineAcknowledgedConsumer;
        paymentStatusUpdatedConsumer;
        orderLineDispatchedConsumer;
        orderLineReturnedConsumer;
        orderReturnedInStoreConsumer;
        orderFinalizedTestConsumer;
        orderPartsCancelledConsumer;
        giftCardPurchasedConsumer

database-queue:
  update-payment-status-queue-properties:
    processing-settings:
      thread-count: 0
  order-parts-cancelled-queue-properties:
    processing-settings:
      thread-count: 0
  order-line-acknowledged-queue-properties:
    processing-settings:
      thread-count: 0
  order-line-dispatched-queue-properties:
    processing-settings:
      thread-count: 0
  order-line-exported-queue-properties:
    processing-settings:
      thread-count: 0
  order-line-returned-queue-properties:
    processing-settings:
      thread-count: 0
  order-returned-in-store-queue-properties:
    processing-settings:
      thread-count: 0
  order-parts-routed-queue-properties:
    processing-settings:
      thread-count: 0
  gift-card-purchased-queue-properties:
    processing-settings:
      thread-count: 0

gateway:
  services:
    payment-service:
      username: admin
      password: admin