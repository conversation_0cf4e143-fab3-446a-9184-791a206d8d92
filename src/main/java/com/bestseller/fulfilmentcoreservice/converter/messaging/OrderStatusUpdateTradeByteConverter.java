package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.time.ZonedDateTime;

@Mapper(imports = {ZonedDateTime.class, OrderState.class})
public interface OrderStatusUpdateTradeByteConverter {

    @Mapping(target = "status", expression = "java(OrderState.RETURNED.getIdentifier())")
    @Mapping(target = "channelSign", source = "order.partnerChannel.channelId")
    @Mapping(target = "dateCreated", expression = "java(ZonedDateTime.now())")
    @Mapping(target = "idCode", source = "orderLineQtyStatus.orderStatusUpdateInfo.returnShipmentId")
    @Mapping(target = "sku", source = "orderLine.ean")
    @Mapping(target = "orderId", source = "order.orderId")
    @Mapping(target = "tbOrderItemId", expression = "java(getTbOrderItemId(orderLine, orderLineQtyStatus))")
    OrderStatusUpdateTradebyte toReturned(Order order, OrderLine orderLine, OrderLineQtyStatus orderLineQtyStatus);

    @Mapping(target = "status", expression = "java(OrderState.DISPATCHED.getIdentifier())")
    @Mapping(target = "channelSign", source = "order.partnerChannel.channelId")
    @Mapping(target = "dateCreated", expression = "java(ZonedDateTime.now())")
    @Mapping(target = "idCode", source = "trackingNumber")
    @Mapping(target = "idCodeReturnProposal", source = "orderLineQtyStatus.orderStatusUpdateInfo.returnShipmentId")
    @Mapping(target = "sku", source = "orderLine.ean")
    @Mapping(target = "orderId", source = "order.orderId")
    @Mapping(target = "tbOrderItemId", expression = "java(getTbOrderItemId(orderLine, orderLineQtyStatus))")
    OrderStatusUpdateTradebyte toDispatched(Order order, OrderLine orderLine,
                                            OrderLineQtyStatus orderLineQtyStatus, String trackingNumber);

    @Mapping(target = "status", expression = "java(OrderState.CANCELLED.getIdentifier())")
    @Mapping(target = "channelSign", source = "order.partnerChannel.channelId")
    @Mapping(target = "dateCreated", expression = "java(ZonedDateTime.now())")
    @Mapping(target = "sku", source = "orderLine.ean")
    @Mapping(target = "orderId", source = "order.orderId")
    @Mapping(target = "tbOrderItemId", expression = "java(getTbOrderItemId(orderLine, orderLineQtyStatus))")
    OrderStatusUpdateTradebyte toCancelled(Order order, OrderLine orderLine, OrderLineQtyStatus orderLineQtyStatus);

    default Integer getTbOrderItemId(OrderLine orderLine, OrderLineQtyStatus orderLineQtyStatus) {
        return (orderLineQtyStatus != null && orderLineQtyStatus.getTradebyteOrderLineQtyStatus() != null
            && orderLineQtyStatus.getTradebyteOrderLineQtyStatus().getOriginalLineNumber() > 0)
            ? orderLineQtyStatus.getTradebyteOrderLineQtyStatus().getOriginalLineNumber()
            : orderLine.getLineNumber();
    }
}
