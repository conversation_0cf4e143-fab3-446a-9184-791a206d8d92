package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.OrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.model.TradebyteOrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.model.ValidOrderPlacedOrderLineModel;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLinePromotion;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.logistics.statetransition.Platform;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.Boolean.TRUE;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class OrderLineConverter {

    private static final String ORDER_LINE_TYPE = "LINE";

    /**
     * Converts the order lines from the message to the order lines that will be saved in the database.
     *
     * @param message Valid Order Placed
     * @return list of order lines
     */
    @Named("toOrderLines")
    public List<ValidOrderPlacedOrderLineModel> toOrderLines(ValidOrderPlaced message) {
        Map<String, List<OrderLineQtyStatusModel>> eanToOrderLineQtyStatuses = new HashMap<>();
        var sortedOrderLines = message.getOrderLines().stream()
            .sorted(Comparator.comparing(OrderLine::getId, Comparator.nullsLast(Comparator.naturalOrder())))
            .toList();
        for (OrderLine orderLine : sortedOrderLines) {
            List<OrderLineQtyStatusModel> orderLineQtyStatuses =
                createOrderLineQtyStatuses(orderLine, message.getStore());
            eanToOrderLineQtyStatuses.merge(
                orderLine.getEan(),
                orderLineQtyStatuses,
                (existingList, newList) -> {
                    existingList.addAll(newList);
                    return existingList;
                });
        }
        Map<String, ValidOrderPlacedOrderLineModel> eanToOrderLine = new HashMap<>();
        for (OrderLine orderLinePlaced : sortedOrderLines) {
            String ean = orderLinePlaced.getEan();

            if (!message.getOfflinePayment() && eanToOrderLine.containsKey(ean)) {
                throw new IllegalArgumentException(String.format("Order %s contains multiple lines with same "
                        + "ean %s but merging lines for online payment is not allowed. It will be rejected.",
                    message.getOrderId(), ean)
                );
            }

            ValidOrderPlacedOrderLineModel orderLine =
                getOrderLine(orderLinePlaced, eanToOrderLineQtyStatuses.get(ean), orderLinePlaced.getLineNumber());

            // Merge order lines by EAN when the payment method is offline
            eanToOrderLine.merge(
                ean,
                orderLine,
                (existingLine, newLine) -> {
                    int newQty = existingLine.getOriginalQty() + orderLinePlaced.getQuantity();
                    existingLine.setOriginalQty(newQty);
                    existingLine.setOpenQty(newQty);
                    return existingLine;
                });
        }
        return new ArrayList<>(eanToOrderLine.values());
    }

    private List<OrderLineQtyStatusModel> createOrderLineQtyStatuses(
        OrderLine placedOrderLine, String store) {
        List<OrderLineQtyStatusModel> orderLineQtyStatuses = new ArrayList<>();
        for (int i = 0; i < placedOrderLine.getQuantity(); i++) {
            OrderLineQtyStatusModel qtyStatus = OrderLineQtyStatusModel.builder().build();

            if (Platform.isTradebyte(store)) {
                qtyStatus.setTradebyteOrderLineQtyStatus(TradebyteOrderLineQtyStatusModel.builder()
                    .originalLineNumber(placedOrderLine.getExternalItemId())
                    .build());
            }
            orderLineQtyStatuses.add(qtyStatus);
        }
        return orderLineQtyStatuses;
    }

    private ValidOrderPlacedOrderLineModel getOrderLine(
        OrderLine placedOrderLine,
        List<OrderLineQtyStatusModel> orderLineQtyStatuses,
        int lineNumber) {

        ValidOrderPlacedOrderLineModel orderLine = convertToOrderLine(
            placedOrderLine, lineNumber, orderLineQtyStatuses);
        OrderLinePromotion promotion = placedOrderLine.getOrderLinePromotion();
        if (promotion != null) {
            orderLine.setPromotionId(promotion.getPromotionId());
            orderLine.setCampaignId(promotion.getCampaignId());
            orderLine.setCouponId(promotion.getCouponId());
        }
        return orderLine;
    }

    private ValidOrderPlacedOrderLineModel convertToOrderLine(
        OrderLine placedOrderLine,
        int lineNumber,
        List<OrderLineQtyStatusModel> orderLineQtyStatuses) {
        return ValidOrderPlacedOrderLineModel.builder()
            .ean(placedOrderLine.getEan())
            .ecomId(placedOrderLine.getId())
            .skuId(placedOrderLine.getEan())
            .lineNumber(lineNumber)
            .name(placedOrderLine.getProductName())
            .partnerReference(placedOrderLine.getPartnerReference())
            .standardRetailPrice(placedOrderLine.getListPrice() == null
                ? placedOrderLine.getRetailPrice()
                : placedOrderLine.getListPrice())
            .originalQty(placedOrderLine.getQuantity())
            .openQty(placedOrderLine.getQuantity())
            .brandDescription(placedOrderLine.getBrand())
            .type(ORDER_LINE_TYPE)
            .bonusProduct(TRUE.equals(placedOrderLine.getBonusProduct()))
            .taxRate(placedOrderLine.getVat())
            .vatClassId(placedOrderLine.getVatClass())
            .orderLineQtyStatus(orderLineQtyStatuses)
            .virtualProduct(Boolean.TRUE.equals(placedOrderLine.getVirtualProduct()))
            .build();
    }

}
