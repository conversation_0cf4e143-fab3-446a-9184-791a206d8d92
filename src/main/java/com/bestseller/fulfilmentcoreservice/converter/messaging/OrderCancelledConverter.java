package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.CompactOrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface OrderCancelledConverter {

    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "warehouse", constant = "HERMES")
    OrderCancelled toCancelled(Order order);

    @Mapping(target = "quantity", source = "originalQty")
    CompactOrderLine toCompactOrderLine(OrderLine orderLine);
}
