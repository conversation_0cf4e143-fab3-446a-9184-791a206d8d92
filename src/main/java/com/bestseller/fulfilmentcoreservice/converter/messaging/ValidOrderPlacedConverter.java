package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.AdditionalOrderInformationModel;
import com.bestseller.fulfilmentcoreservice.core.model.EntityType;
import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.logistics.statetransition.Platform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("all")
@Mapper(
    uses = {AddressConverter.class, CustomerConverter.class, OrderLineConverter.class},
    injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public abstract class ValidOrderPlacedConverter {
    private static final String DEFAULT_SHIPPING_METHOD = "STANDARD";

    private static final String VIRTUAL_SHIPPING_METHOD = "VIRTUAL";

    @Mapping(target = "orderDate", source = "placedDate")
    @Mapping(target = "shippingAddress", source = ".", qualifiedByName = "toShippingAddress")
    @Mapping(target = "checkoutScopeId", source = "orderDetails.checkout")
    @Mapping(target = "platform", source = "store")
    @Mapping(target = "channel", source = "orderDetails.channel")
    @Mapping(target = "shippingMethod", source = "orderDetails.shippingMethod", qualifiedByName = "getShippingMethodOrDefault")
    @Mapping(target = "externalOrderId", source = ".", qualifiedByName = "getExternalOrderId")
    @Mapping(target = "brandedShipping", source = "orderDetails.brandedShipping")
    @Mapping(target = "billToMatchesShipTo", constant = "false")
    @Mapping(target = "customer", source = ".", qualifiedByName = "toCustomerModel")
    @Mapping(target = "billingAddress", source = ".", qualifiedByName = "toBillingAddress")
    @Mapping(target = "orderLines", source = ".", qualifiedByName = "toOrderLines")
    @Mapping(target = "market", source = "orderDetails.market")
    @Mapping(target = "test", source = "isTest")
    @Mapping(target = "currency", source = ".", qualifiedByName = "getOrderCurrency")
    @Mapping(target = "orderValue", source = "orderDetails.orderValue")
    @Mapping(target = "additionalOrderInformation", source = ".",
        qualifiedByName = "toAdditionalOrderInformationModel")
    @Mapping(target = "isoStoreId", source = "orderDetails.isoStoreId")
    public abstract OrderModel toOrderModel(ValidOrderPlaced validOrderPlaced);

    @Named("getOrderCurrency")
    String getOrderCurrency(ValidOrderPlaced validOrderPlaced) {
        return StringUtils.isNotBlank(validOrderPlaced.getOrderDetails().getCurrency())
            ? validOrderPlaced.getOrderDetails().getCurrency()
            : validOrderPlaced.getPayments().stream().findFirst().map(Payment::getCurrency).orElse(null);
    }

    @Named("getShippingMethodOrDefault")
    String getShippingMethodOrDefault(String shippingMethod) {
        return StringUtils.isBlank(shippingMethod) ? DEFAULT_SHIPPING_METHOD : shippingMethod.toUpperCase();
    }

    @Named("getExternalOrderId")
    String getExternalOrderId(ValidOrderPlaced validOrderPlaced) {
        return Platform.isDemandware(validOrderPlaced.getStore()) ? null : validOrderPlaced.getOrderDetails().getExternalOrderNumber();
    }

    @Named("toAdditionalOrderInformationModel")
    List<AdditionalOrderInformationModel> toAdditionalOrderInformationModel(ValidOrderPlaced validOrderPlaced) {
        Set<AdditionalOrderInformationModel> additionalOrderInformationModels = new HashSet<>();

        //Shipping information can be null for virtual orders
        if (!VIRTUAL_SHIPPING_METHOD.equals(validOrderPlaced.getOrderDetails().getShippingMethod())) {
            addAdditionalInformationModels(validOrderPlaced.getShippingInformation().getAdditionalInformation(),
                EntityType.SHIPPING_INFORMATION,
                additionalOrderInformationModels);
        }

        addAdditionalInformationModels(validOrderPlaced.getOrderDetails().getAdditionalInformation(),
            EntityType.ORDER_DETAILS,
            additionalOrderInformationModels);

        return new ArrayList<>(additionalOrderInformationModels);
    }

    private void addAdditionalInformationModels(Set<AdditionalInformation> additionalInformations,
        EntityType entityType,
        Set<AdditionalOrderInformationModel> models) {
            additionalInformations.stream()
                .map(info -> convertAdditionalOrderInformationToModel(info, entityType))
                .forEach(models::add);
    }

    private AdditionalOrderInformationModel convertAdditionalOrderInformationToModel(
        AdditionalInformation additionalInformation,
        EntityType entityType) {
        return AdditionalOrderInformationModel.builder()
            .key(additionalInformation.getKey())
            .value(additionalInformation.getValue())
            .entityType(entityType)
            .build();
    }
}
