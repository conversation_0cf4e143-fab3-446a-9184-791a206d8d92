package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper
public interface OrderFulfillmentPartConverter {

    @Mapping(target = "eanQuantityPairs", source = "orderPartsRouted.orderLines",
        qualifiedByName = "mapOrderLinesToEanQuantityPairs")
    @Mapping(target = "partNumber", source = "orderPartsRouted.orderPartNumber")
    @Mapping(target = "order", source = "order")
    OrderFulfilmentPartModel toOrderFulfillmentPartModel(Order order, OrderPartsRouted orderPartsRouted);

    @Mapping(target = "eanQuantityPairs", source = "orderLineDispatched",
        qualifiedByName = "mapOrderLineDispatchedToEanQuantityPair")
    @Mapping(target = "returnTrackingNumber", source = "orderLineDispatched.returnShipmentId")
    @Mapping(target = "fulfillmentNode", source = "orderLineDispatched.warehouse")
    @Mapping(target = "order", source = "order")
    OrderFulfilmentPartModel toOrderFulfillmentPartModel(Order order, OrderLineDispatched orderLineDispatched);

    OrderFulfillmentPart toOrderFulfillmentPart(OrderFulfilmentPartModel orderFulfilmentPartModel);

    @Named("mapOrderLinesToEanQuantityPairs")
    default List<OrderFulfilmentPartModel.EanQuantityPair> mapOrderLinesToEans(List<OrderLine> orderLines) {
        return orderLines
            .stream()
            .map(orderLine -> OrderFulfilmentPartModel.EanQuantityPair.builder()
                .ean(orderLine.getEan())
                .quantity(orderLine.getQuantity())
                .build())
            .toList();
    }

    @Named("mapOrderLineDispatchedToEanQuantityPair")
    default List<OrderFulfilmentPartModel.EanQuantityPair> mapOrderLineDispatchedToEanQuantityPair(
        OrderLineDispatched orderLineDispatched) {
        return List.of(OrderFulfilmentPartModel.EanQuantityPair.builder()
            .ean(orderLineDispatched.getEan())
            .quantity(orderLineDispatched.getQuantity())
            .build());
    }
}
