package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.common.Payload;
import lombok.Getter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Clock;
import java.time.ZonedDateTime;

@Getter
@Mapper(imports = {OrderStatusUpdated.class, ZonedDateTime.class, Clock.class})
public abstract class OrderStatusUpdatedConverter {

    @Autowired
    private Clock utcClock;

    @Mapping(target = "type", source = "type")
    @Mapping(target = "orderId", source = "order.orderId")
    @Mapping(target = "updatedAt", source = "order.lastModifiedTS")
    @Mapping(target = "timestamp", expression = "java(ZonedDateTime.now(getUtcClock()))")
    public abstract OrderStatusUpdated convertTo(
        Order order, Payload payload, OrderStatusUpdated.Type type);
}
