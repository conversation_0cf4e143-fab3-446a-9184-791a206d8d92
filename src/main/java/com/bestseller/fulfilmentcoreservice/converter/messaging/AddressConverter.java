package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.converter.util.PostCodeUtil;
import com.bestseller.fulfilmentcoreservice.core.model.AdditionalOrderInformationModel;
import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import com.bestseller.fulfilmentcoreservice.core.model.EntityType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

@Mapper(imports = StringUtils.class)
public abstract class AddressConverter {

    @Named("toBillingAddress")
    @Mapping(target = "address1", source = "customerInformation.billingAddress",
        qualifiedByName = "getAddressLine1")
    @Mapping(target = "address2", source = "customerInformation.billingAddress.addressLine2")
    @Mapping(target = "address3", source = "customerInformation.billingAddress.addressLine3")
    @Mapping(target = "carrierVariant", source = "orderDetails.carrierVariant")
    @Mapping(target = "city", source = "customerInformation.billingAddress.city")
    @Mapping(target = "countryCode", source = "customerInformation.billingAddress.country")
    @Mapping(target = "firstName", source = "customerInformation.billingAddress.firstName")
    @Mapping(target = "houseNumber", source = "customerInformation.billingAddress.houseNumber")
    @Mapping(target = "houseNumberExt", source = "customerInformation.billingAddress.houseNumberExtended")
    @Mapping(target = "lastName", source = "customerInformation.billingAddress.lastName")
    @Mapping(target = "phone", source = "customerInformation.billingAddress.phoneNumber")
    @Mapping(target = "postCode", source = "customerInformation.billingAddress", qualifiedByName = "getPostCode")
    @Mapping(target = "salutation", source = "customerInformation.billingAddress.salutation")
    @Mapping(target = "state", source = "customerInformation.billingAddress.state")
    @BeanMapping(ignoreByDefault = true)
    public abstract AddressModel toBillingAddress(ValidOrderPlaced message);

    @Named("toShippingAddress")
    @Mapping(target = "address1", source = "shippingInformation.shippingAddress",
        qualifiedByName = "getAddressLine1")
    @Mapping(target = "address2", source = "shippingInformation.shippingAddress.addressLine2")
    @Mapping(target = "address3", source = "shippingInformation.shippingAddress.addressLine3")
    @Mapping(target = "carrierVariant",
        expression = "java(StringUtils.isNotBlank(message.getOrderDetails().getCarrierVariant()) "
            + "? message.getOrderDetails().getCarrierVariant() : null)")
    @Mapping(target = "city", source = "shippingInformation.shippingAddress.city")
    @Mapping(target = "countryCode", source = "shippingInformation.shippingAddress.country")
    @Mapping(target = "deliveryOption", source = "orderDetails.carrier")
    @Mapping(target = "firstName", source = "shippingInformation.shippingAddress.firstName")
    @Mapping(target = "houseNumber", source = "shippingInformation.shippingAddress.houseNumber")
    @Mapping(target = "houseNumberExt", source = "shippingInformation.shippingAddress.houseNumberExtended")
    @Mapping(target = "lastName", source = "shippingInformation.shippingAddress.lastName")
    @Mapping(target = "parcelLocker", source = "shippingInformation.parcelLocker")
    @Mapping(target = "phone", source = "shippingInformation.shippingAddress.phoneNumber")
    @Mapping(target = "physicalStoreId", source = "orderDetails.physicalStoreId")
    @Mapping(target = "postCode", source = "shippingInformation.shippingAddress", qualifiedByName = "getPostCode")
    @Mapping(target = "salutation", source = "shippingInformation.shippingAddress.salutation")
    @Mapping(target = "state", source = "shippingInformation.shippingAddress.state")
    @BeanMapping(ignoreByDefault = true)
    public abstract AddressModel toShippingAddress(ValidOrderPlaced message);

    @Named("getPostCode")
    String getPostCode(Address address) {
        if (address == null) {
            return null;
        }
        return PostCodeUtil.COUNTRY_NL.equals(address.getCountry())
            ? PostCodeUtil.adjustPostCode(address.getZipcode())
            : address.getZipcode();
    }

    @Named("getAddressLine1")
    @SuppressWarnings("NestedIfDepth")
    String getAddressLine1(Address address) {
        if (address == null) {
            return null;
        }
        if (StringUtils.isBlank(address.getHouseNumber())) {
            var pattern = Pattern.compile("\\d+.*");
            var addressLine1 = address.getAddressLine1();
            var matcher = pattern.matcher(addressLine1);
            if (matcher.find()) {
                var houseNumber = matcher.group().trim();
                if (houseNumber.equals(addressLine1.trim())) {
                    return addressLine1;
                }
                address.setHouseNumber(houseNumber);
                return addressLine1
                    .substring(0, matcher.start())
                    .trim()
                    .replaceAll("\\.$", "")
                    .trim();
            }
        }
        return address.getAddressLine1();
    }


    /**
     * Converts the additional information to the model.
     */
    @Named("toAdditionalInformationModels")
    List<AdditionalOrderInformationModel> toAdditionalInformationModels(
        Set<AdditionalInformation> additionalInformation) {
        return additionalInformation.stream()
            .map(ai ->
                new AdditionalOrderInformationModel(
                    ai.getKey(),
                    ai.getValue(),
                    EntityType.SHIPPING_INFORMATION
                )
            )
            .toList();
    }

}
