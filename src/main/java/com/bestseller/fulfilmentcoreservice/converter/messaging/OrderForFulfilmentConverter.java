package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.AdditionalOrderInformationModel;
import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import com.bestseller.fulfilmentcoreservice.core.model.CustomerInformationModel;
import com.bestseller.fulfilmentcoreservice.core.model.FulfillmentAdviceModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderDetailsModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderForFulfilmentModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderLineModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderType;
import com.bestseller.fulfilmentcoreservice.core.model.PaymentStatusUpdatedModel;
import com.bestseller.fulfilmentcoreservice.core.model.ShippingInformationModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.UserDeviceInfo;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.logistics.statetransition.Platform;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.logistics.statetransition.Platform.DEMANDWARE;
import static com.logistics.statetransition.Platform.TRADEBYTE;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderForFulfilmentConverter {

    @Mapping(target = "shippingInformation.additionalInformation", source = "additionalOrderInformation",
        qualifiedByName = "toAdditionalInformation")
    @Mapping(target = "shippingInformation", source = "shippingInformation")
    OrderForFulfillment toOrderForFulfilment(OrderForFulfilmentModel orderForFulfilmentModel);

    OrderDetails toOrderDetails(OrderDetailsModel orderDetails);

    CustomerInformation toCustomerInformation(CustomerInformationModel customerInformation);

    ShippingInformation toShippingInformation(ShippingInformationModel shippingInformation);

    @Mapping(source = "address1", target = "addressLine1")
    @Mapping(source = "address2", target = "addressLine2")
    @Mapping(source = "address3", target = "addressLine3")
    @Mapping(source = "countryCode", target = "country")
    @Mapping(source = "houseNumberExt", target = "houseNumberExtended")
    @Mapping(source = "phone", target = "phoneNumber")
    @Mapping(source = "postCode", target = "zipcode")
    @Mapping(source = "physicalStoreId", target = "storeId")
    Address toAddress(AddressModel address);

    @Mapping(target = "isGiftItem", source = "virtualProduct")
    OrderLine toOrderLine(OrderLineModel orderLine);

    @Mapping(target = "orderId", source = "payment.orderId")
    @Mapping(target = "payments", source = "payment.payments")
    @Mapping(target = "orderDetails", source = "order")
    @Mapping(target = "isTest", source = "order.test")
    @Mapping(target = "placedDate", source = "order.orderDate")
    @Mapping(target = "marketPlace", expression = "java("
        + "order.getPartnerChannel() == null ? null:String.valueOf(order.getPartnerChannel().getChannelId()))")
    @Mapping(target = "store", source = "order.platform")
    @Mapping(target = "orderLines", source = "order.orderLines", qualifiedByName = "toOrderLinesModel")
    @Mapping(target = "shippingInformation", source = "order.shippingAddress")
    @Mapping(target = "platform", source = "order", qualifiedByName = "getPlatform")
    @Mapping(target = "customerInformation", source = "order")
    @Mapping(target = "channel", expression = "java(order.getChannelType()"
        + "== null ? null : order.getChannelType().name().toLowerCase())")
    @Mapping(target = "brand", expression = "java("
        + "order.getBrand() == null ? null : order.getBrand().getBrandAbbreviation())")
    @Mapping(target = "actionCode", expression = "java("
        + "order.getPartnerChannel() == null ? null:String.valueOf(order.getPartnerChannel().getActionCode()))")
    @Mapping(target = "partnerChannel", expression = "java("
        + "order.getPartnerChannel() == null ? null:String.valueOf(order.getPartnerChannel().getDescription()))")
    @Mapping(target = "fulfillmentAdvice", source = "order", qualifiedByName = "createFulfillmentAdviceIfPossible")
    @Mapping(target = "announcedDeliveryDate", source = "order.announcedDeliveryDate")
    OrderForFulfilmentModel toOrderForFulfilmentModel(Order order, PaymentStatusUpdatedModel payment);

    @Mapping(target = "productName", source = "name")
    @Mapping(target = "quantity", source = "openQty")
    @Mapping(target = "brand", source = "brandDescription")
    OrderLineModel toOrderLineModel(com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine orderLine);

    @Mapping(target = "carrier", source = "shippingAddress.deliveryOption")
    @Mapping(target = "carrierVariant", source = "shippingAddress.carrierVariant")
    @Mapping(target = "shippingMethod", source = "shippingMethod")
    @Mapping(target = "externalOrderNumber", source = "externalOrderId")
    @Mapping(target = "orderCreationDate", source = "orderDate")
    @Mapping(target = "orderType", source = "order.platform", qualifiedByName = "getOrderType")
    @Mapping(target = "currency", source = "currency")
    @Mapping(target = "checkout", source = "checkoutScopeId")
    OrderDetailsModel toOrderDetailsModel(Order order);

    @Mapping(target = "shippingAddress", source = "shippingAddress")
    @Mapping(target = "parcelLocker", source = "parcelLocker")
    ShippingInformationModel createShippingInformation(
        com.bestseller.fulfilmentcoreservice.persistence.entity.Address shippingAddress);

    @Mapping(target = "email", source = "customer.email")
    @Mapping(target = "customerId", source = "customer.customerId")
    @Mapping(target = "customerLocale", source = "userDeviceInfo.locale")
    @Mapping(target = "externalCustomerNumber", source = "customer.externalCustomerId")
    CustomerInformationModel toCustomerInformationModel(Order order);

    @Named("createFulfillmentAdviceIfPossible")
    default FulfillmentAdviceModel createFulfillmentAdviceIfPossible(Order order) {
        return order.getOrderLines().stream()
            .map(com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine::getOrderLineQtyStatus)
            .filter(Objects::nonNull)
            .filter(orderLineQtyStatus -> !orderLineQtyStatus.isEmpty())
            .map(orderLineQtyStatus -> orderLineQtyStatus.getFirst().getOrderFulfillmentPart())
            .filter(Objects::nonNull)
            .filter(orderFulfillmentPart -> orderFulfillmentPart.getFulfillmentNode() != null)
            .map(orderFulfillmentPart -> FulfillmentAdviceModel.builder()
                .fulfillmentNode(orderFulfillmentPart.getFulfillmentNode())
                .holdFromRouting(orderFulfillmentPart.isHoldFromRouting())
                .build())
            .findFirst()
            .orElse(null);
    }

    @Named("getOrderType")
    default String getOrderType(Platform platform) {
        if (DEMANDWARE.equals(platform)) {
            return OrderType.STANDARD.name();
        } else if (TRADEBYTE.equals(platform)) {
            return OrderType.MARKETPLACE.name();
        }
        return null;
    }

    @Named("getPlatform")
    default String getPlatform(Order order) {
        return Optional.ofNullable(order.getUserDeviceInfo())
            .map(UserDeviceInfo::getPlatform)
            .filter(StringUtils::isNotBlank).orElse(null);
    }

    default void completeOrderDetails(
        PaymentStatusUpdatedModel payment, OrderForFulfilmentModel orderForFulfilmentModel) {
        if (payment.getOrderDetails() != null) {
            orderForFulfilmentModel.getOrderDetails().setOrderValue(payment.getOrderDetails().getOrderValue());
            orderForFulfilmentModel.getOrderDetails().setShippingFees(payment.getOrderDetails().getShippingFees());
            orderForFulfilmentModel.getOrderDetails().setShippingFeesTaxPercentage(
                payment.getOrderDetails().getShippingFeesTaxPercentage());
            orderForFulfilmentModel.getOrderDetails().setShippingFeesCancelled(
                payment.getOrderDetails().getShippingFeesCancelled());
        }
    }

    default void completeOrderForFulfilmentModel(PaymentStatusUpdatedModel payment,
                                                 OrderForFulfilmentModel orderForFulfilmentModel) {
        orderForFulfilmentModel.getOrderLines().forEach(orderLine ->
            payment.getOrderLines().stream()
                .filter(partialOrderLine -> orderLine.getEan().equals(partialOrderLine.getEan()))
                .findFirst()
                .ifPresent(second -> {
                    orderLine.setDiscountValue(second.getDiscountValue());
                    orderLine.setRetailPrice(second.getRetailPrice());
                    orderLine.setTaxPercentage(second.getTaxPercentage());
                })
        );
    }

    @Named("toAdditionalInformation")
    default Set<AdditionalInformation> toAdditionalInformation(
        List<AdditionalOrderInformationModel> additionanformationList) {
        return additionanformationList
            .stream()
            .filter(additionalInformation ->
                com.bestseller.fulfilmentcoreservice.core.model.EntityType
                    .SHIPPING_INFORMATION.equals(additionalInformation.getEntityType()))
            .map(additionalInformationModel -> new AdditionalInformation()
                .withKey(additionalInformationModel.getKey())
                .withValue(additionalInformationModel.getValue()))
            .collect(Collectors.toSet());
    }

    @Named("toOrderLinesModel")
    default List<OrderLineModel> toOrderLinesModel(
        List<com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine> orderLines) {
        return orderLines.stream()
            .filter(orderLine -> !orderLine.isVirtualProduct())
            .map(this::toOrderLineModel)
            .toList();
    }
}
