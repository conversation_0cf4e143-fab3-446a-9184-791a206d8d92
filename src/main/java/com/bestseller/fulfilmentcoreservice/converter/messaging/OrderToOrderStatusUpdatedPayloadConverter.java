package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine;
import com.logistics.statetransition.OrderState;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class OrderToOrderStatusUpdatedPayloadConverter implements Converter<Order, DefaultOrderStatusUpdatedPayload> {

    @Override
    public DefaultOrderStatusUpdatedPayload convert(Order order) {
        return DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(order.getOrderLines()
                .stream()
                .filter(com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine::isChanged)
                .map(orderLine -> OrderLine.builder()
                    .id(orderLine.getEcomId())
                    .states(
                        orderLine.getOrderLineQtyStatus()
                            .stream()
                            .map(OrderLineQtyStatus::getOrderState)
                            .map(OrderState::name)
                            .toList()
                    )
                    .build()
                )
                .toList()
            ).build();
    }

}
