package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper
public interface OrderFinalizedConverter {

    @Mapping(target = "orderLines", source = "order.orderLines", qualifiedByName = "toOrderFinalizedOrderLine")
    OrderFinalized toOrderFinalized(Order order);

    @Named("toOrderFinalizedOrderLine")
    @Mapping(target = "quantityStatuses", source = ".", qualifiedByName = "toOrderLineQuantityStatuses")
    com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine toOrderFinalizedOrderLine(
        com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine orderLine);

    @Named("toOrderLineQuantityStatuses")
    default List<String> toOrderLineQuantityStatuses(
        com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine orderLine) {
        return orderLine.getOrderLineQtyStatus()
            .stream()
            .map(orderLineQtyStatus -> orderLineQtyStatus.getOrderState().name())
            .toList();
    }
}
