package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.CustomerModel;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import java.util.Optional;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class CustomerConverter {

    @Named("toCustomerModel")
    @Mapping(target = "customerId", source = "customerInformation.customerId")
    @Mapping(target = "email", source = "customerInformation.email")
    @Mapping(target = "externalCustomerId", source = "customerInformation.externalCustomerId")
    @Mapping(target = "isLoggedIn", source = "customerInformation.isLoggedIn")
    @Mapping(target = "customerLocale", source = "customerInformation.customerLocale")
    @Mapping(target = "appVersion", source = "customerInformation.appVersion")
    @Mapping(target = "originalScopeId", source = "customerInformation.originalScopeId")
    @Mapping(target = "platform", source = "customerInformation.platform")
    abstract CustomerModel toCustomerModel(ValidOrderPlaced validOrderPlaced);

    /**
     * Complete the conversion with some custom data.
     *
     * @param source
     * @param target
     */
    @AfterMapping
    void completeMapping(ValidOrderPlaced source, @MappingTarget CustomerModel.CustomerModelBuilder target) {
        CustomerInformation customerInformation = source.getCustomerInformation();
        Address address = getAddress(source);
        target.name(getFullName(address));
        target.employeeId(customerInformation.getIsEmployee() ? customerInformation.getEmployeeNumber() : null);
        target.type(customerInformation.getIsEmployee() ? CustomerType.EMPLOYEE.name() : CustomerType.CUSTOMER.name());
    }

    private Address getAddress(ValidOrderPlaced source) {
        var shippingAddressIsAbsent = Optional.ofNullable(source.getShippingInformation())
            .map(ShippingInformation::getShippingAddress)
            // Checking the addressLine1 which cannot be null as a workaround in order for bypass mistake in OMS.
            .map(Address::getAddressLine1)
            .isEmpty();
        return shippingAddressIsAbsent
            ? source.getCustomerInformation().getBillingAddress()
            : source.getShippingInformation().getShippingAddress();
    }

    private String getFullName(Address address) {
        return address.getFirstName().trim() + " " + address.getLastName().trim();
    }

}
