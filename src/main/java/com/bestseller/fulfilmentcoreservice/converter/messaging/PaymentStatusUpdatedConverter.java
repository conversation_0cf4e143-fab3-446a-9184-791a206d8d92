package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.PaymentStatusUpdatedModel;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PaymentStatusUpdatedConverter {

    @Mapping(target = "orderId", source = "orderId")
    @Mapping(target = "payments", source = "payload.payments")
    @Mapping(target = "orderDetails", source = "payload.orderDetails")
    @Mapping(target = "orderLines", source = "payload.orderLines")
    PaymentStatusUpdatedModel toPaymentStatusUpdatedModel(
        String orderId, AuthorizedPayload payload);

}
