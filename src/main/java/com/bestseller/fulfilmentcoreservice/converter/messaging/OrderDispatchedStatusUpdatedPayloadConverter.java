package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderDispatchedStatusUpdatedPayloadConverter {

    public DefaultOrderStatusUpdatedPayload convert(
        List<OrderLine> orderLines,
        OrderLineDispatched orderLineDispatched
    ) {
        return DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                orderLines.stream()
                    .filter(com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine::isChanged)
                    .map(orderLine ->
                        com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                            .id(orderLine.getEcomId())
                            .states(
                                orderLine.getOrderLineQtyStatus()
                                    .stream()
                                    .map(OrderLineQtyStatus::getOrderState)
                                    .map(OrderState::name)
                                    .toList()
                            )
                            .trackingNumber(orderLineDispatched.getTrackingNumber())
                            .returnShipmentId(orderLineDispatched.getReturnShipmentId())
                            .build()
                    )
                    .toList()
            )
            .build();
    }

}
