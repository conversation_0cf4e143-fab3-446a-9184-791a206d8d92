package com.bestseller.fulfilmentcoreservice.converter.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PostCodeUtil {

    public static final String COUNTRY_NL = "NL";
    private static final String NON_ALPHA_NUMERIC_REGEX = "[^a-zA-Z0-9]";
    private static final int NL_ZIP_CODE_OFFSET_4 = 4;
    private static final int NL_ZIP_CODE_OFFSET_6 = 6;
    private static final String BLANK_SPACE = " ";

    /**
     * Method to adjust post code.
     *
     * @param postCode
     */
    public static String adjustPostCode(String postCode) {
        String postCodeWithoutWhiteSpace = postCode.replaceAll("\\s", "");
        if (postCodeWithoutWhiteSpace.toUpperCase().startsWith(COUNTRY_NL)) {
            char[] characters = postCodeWithoutWhiteSpace.toCharArray();
            for (int i = 0; i < characters.length; i++) {
                if (Character.isDigit(characters[i])) {
                    // first number found
                    postCodeWithoutWhiteSpace = postCodeWithoutWhiteSpace.substring(i);
                    break;
                }
            }
        }
        // remove non-alphanumeric characters from the rest of the string
        postCodeWithoutWhiteSpace = postCodeWithoutWhiteSpace.replaceAll(NON_ALPHA_NUMERIC_REGEX, "");
        return postCodeWithoutWhiteSpace.substring(0, NL_ZIP_CODE_OFFSET_4)
            .concat(BLANK_SPACE)
            .concat(postCodeWithoutWhiteSpace.substring(NL_ZIP_CODE_OFFSET_4, NL_ZIP_CODE_OFFSET_6));
    }

}
