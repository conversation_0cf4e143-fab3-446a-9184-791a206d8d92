package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderBlockView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderCancelView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.OrderStateTransition;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(uses = {
    AddressViewConverter.class,
    CustomerViewConverter.class,
    OrderLineViewConverter.class,
    ShipmentViewConverter.class,
    PartnerChannelViewConverter.class
})
public abstract class OrderViewConverter {

    @Mapping(target = "id", source = "orderId")
    @Mapping(target = "createdAt", source = "createdTS")
    @Mapping(target = "lastUpdate", source = "lastModifiedTS")
    @Mapping(target = "minStatus", expression = "java(order.getMinStatus().getIdentifier())")
    @Mapping(target = "maxStatus", expression = "java(order.getMaxStatus().getIdentifier())")
    @Mapping(target = "checkout", source = "checkoutScopeId")
    @Mapping(target = "store", source = "platform")
    @Mapping(target = "shipments", source = "orderFulfillmentParts")
    @Mapping(target = "orderCancel", source = "order", qualifiedByName = "orderToOrderCancelView")
    @Mapping(target = "brand", expression = "java(order.getBrand() != null ? order.getBrand().getDescription() : null)")
    @Mapping(
        target = "brandAbbreviation",
        expression = "java(order.getBrand() != null ? order.getBrand().getBrandAbbreviation() : null)"
    )
    @Mapping(target = "orderBlock", source = "order", qualifiedByName = "orderToOrderBlockView")
    @Mapping(
        target = "market",
        expression = "java(order.getMarket() != null ? order.getMarket().getMarketCode() : null)"
    )
    public abstract OrderView convert(Order order);

    @Named("orderToOrderCancelView")
    protected OrderCancelView orderToOrderCancelView(Order order) {
        return OrderCancelView.builder()
            .canBeCancelled(order.getMinStatus() == OrderState.BLOCKED)
            .build();
    }

    @Named("orderToOrderBlockView")
    protected OrderBlockView orderToOrderBlockView(Order order) {
        return OrderBlockView.builder()
            .canBeBlocked(defineIfCanBeBlocked(order))
            .canBeUnblocked(defineIfCanBeUnblocked(order))
            .build();
    }

    private boolean defineIfCanBeUnblocked(Order order) {
        return order.getMinStatus().isLessThan(OrderState.DISPATCHED)
            && order.getOrderBlock() != null;
    }

    private boolean defineIfCanBeBlocked(Order order) {
        boolean containsVirtualProducts = order.getOrderLines()
            .stream()
            .anyMatch(OrderLine::isVirtualProduct);
        if (containsVirtualProducts) {
            return false;
        }
        return OrderStateTransition
            .isTransitionValid(order.getPlatform(), order.getMinStatus(), OrderState.BLOCKED, false)
            && order.getOrderBlock() == null;
    }

}
