package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineQuantityStatusView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper
public interface OrderLineQuantityStatusViewConverter {

    @Mapping(target = "orderLineStatus", source = "orderState")
    @Mapping(target = "fulfillmentNode", source = "orderLineQtyStatus", qualifiedByName = "getFulfillmentNode")
    OrderLineQuantityStatusView convert(OrderLineQtyStatus orderLineQtyStatus);

    List<OrderLineQuantityStatusView> convert(List<OrderLineQtyStatus> orderLineQtyStatus);

    @Named("getFulfillmentNode")
    default String getFulfillmentNode(OrderLineQtyStatus orderLineQtyStatus) {
        return orderLineQtyStatus.getOrderFulfillmentPart() != null
            ? orderLineQtyStatus.getOrderFulfillmentPart().getFulfillmentNode()
            : null;
    }
}
