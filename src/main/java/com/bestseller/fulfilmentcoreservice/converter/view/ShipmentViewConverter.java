package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.ShipmentView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

@Mapper
public interface ShipmentViewConverter {

    @Mapping(target = "carrier", source = "carrierName")
    @Mapping(target = "returnShipmentId", source = "returnTrackingNumber")
    @Mapping(target = "fulfilmentNode", source = "fulfillmentNode")
    @Mapping(target = "dispatchDate", source = "dispatchDate", qualifiedByName = "localDateTimeToUtc")
    ShipmentView convert(OrderFulfillmentPart orderFulfillmentPart);

    @Named("localDateTimeToUtc")
    static ZonedDateTime localDateTimeToUtc(LocalDateTime localDateTime) {
        return localDateTime != null ? localDateTime.atZone(ZoneOffset.UTC) : null;
    }

}
