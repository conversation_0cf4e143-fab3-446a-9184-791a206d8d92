package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.core.model.TradeByteOrderStatusBaseModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.logistics.statetransition.OrderState;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(imports = OrderState.class)
public interface TradeByteOrderStatusBaseConverter {

    @Mapping(target = "orderState", expression = "java(OrderState.DISPATCHED)")
    TradeByteOrderStatusBaseModel toDispatched(OrderLineDispatched orderLineDispatched);

    @Mapping(target = "orderState", expression = "java(OrderState.RETURNED)")
    TradeByteOrderStatusBaseModel toReturned(OrderLineReturned orderLineReturned);

    @Mapping(target = "orderState", expression = "java(OrderState.CANCELLED)")
    TradeByteOrderStatusBaseModel toCancelled(OrderPartsCancelled orderPartsCancelled);

    @Mapping(target = "originalQty", source = "quantity")
    OrderLine getOrderline(
        com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine
            orderLine);
}
