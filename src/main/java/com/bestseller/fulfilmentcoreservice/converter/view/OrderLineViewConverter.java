package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = OrderLineQuantityStatusViewConverter.class)
public interface OrderLineViewConverter {

    @Mapping(target = "orderLineNumber", source = "lineNumber")
    @Mapping(target = "lineDescription", source = "name")
    @Mapping(target = "brand", source = "brandDescription")
    @Mapping(target = "orderLineQuantityStatus", source = "orderLineQtyStatus")
    OrderLineView convert(OrderLine orderLine);

}
