package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.CustomerView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface CustomerViewConverter {

    @Mapping(target = "id", source = "customerId")
    CustomerView convert(Customer customer);

}
