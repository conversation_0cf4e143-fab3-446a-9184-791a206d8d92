package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.core.convert.converter.Converter;

import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(unmappedTargetPolicy = IGNORE)
public interface AddressEntityConverter extends Converter<AddressModel, Address> {

    int SIZE_4 = 4;
    int SIZE_10 = 10;
    int SIZE_20 = 20;
    int SIZE_25 = 25;
    int SIZE_24 = 24;
    int SIZE_26 = 26;
    int SIZE_32 = 32;

    @Mapping(target = "firstName", expression = "java(trimValue(source.getFirstName(), SIZE_32))")
    @Mapping(target = "salutation", expression = "java(trimValue(source.getSalutation(), SIZE_10))")
    @Mapping(target = "lastName", expression = "java(trimValue(source.getLastName(), SIZE_26))")
    @Mapping(target = "city", expression = "java(trimValue(source.getCity(), SIZE_24))")
    @Mapping(target = "countryCode", expression = "java(trimValue(source.getCountryCode(), SIZE_4))")
    @Mapping(target = "postCode", expression = "java(trimValue(source.getPostCode(), SIZE_10))")
    @Mapping(target = "houseNumber", expression = "java(trimValue(source.getHouseNumber(), SIZE_32))")
    @Mapping(target = "state", expression = "java(trimValue(source.getState(), SIZE_10))")
    @Mapping(target = "houseNumberExt", expression = "java(trimValue(source.getHouseNumberExt(), SIZE_10))")
    @Mapping(target = "phone", expression = "java(trimValue(source.getPhone(), SIZE_20))")
    @Mapping(target = "address1", expression = "java(trimValue(source.getAddress1(), SIZE_32))")
    @Mapping(target = "address2", expression = "java(trimValue(source.getAddress2(), SIZE_32))")
    @Mapping(target = "address3", expression = "java(trimValue(source.getAddress3(), SIZE_25))")
    Address convert(AddressModel source);

    default String trimValue(String value, int size) {
        if (value == null || value.isBlank()) {
            return null;
        }
        String trimmedValue = value.strip();
        if (trimmedValue.length() > size) {
            return trimmedValue.substring(0, size);
        }
        return trimmedValue;
    }
}
