package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.fulfilmentcoreservice.core.model.ValidOrderPlacedOrderLineModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.core.convert.converter.Converter;

@Mapper(uses = {
    AddressEntityConverter.class,
    CustomerEntityConverter.class,
    OrderLineEntityConverter.class},
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    imports = {OrderState.class, Market.class})
public interface OrderEntityConverter extends Converter<OrderModel, Order> {
    @Override
    @Mapping(target = "market", expression = "java(Market.fromMarketCode(source.getMarket()))")
    @Mapping(target = "brand", source = ".", qualifiedByName = "getBrand")
    @Mapping(target = "shippingMethod", source = ".", qualifiedByName = "getShippingMethod")
    Order convert(OrderModel source);

    /**
     * Returns the {@link Brand} for the given {@link OrderModel} if the platform is Demandware.
     * Otherwise, returns null.
     */
    @Named("getBrand")
    default Brand getBrand(OrderModel source) {
        if (Platform.isDemandware(source.getPlatform())) {
            return StringUtils.isBlank(source.getBrandedShipping())
                ? Brand.getDefaultBrand()
                : Brand.valueOf(source.getBrandedShipping().toUpperCase());
        }
        return null;
    }

    /**
     * Returns the shipping method accordingly to the platform and checking if the order is virtual.
     */
    @Named("getShippingMethod")
    default ShippingMethod getShippingMethod(OrderModel source) {
        ShippingMethod shippingMethod = ShippingMethod.valueOf(source.getShippingMethod().toUpperCase());
        if (Platform.isTradebyte(source.getPlatform())) {
            return shippingMethod;
        }

        if (source.getOrderLines().stream().allMatch(ValidOrderPlacedOrderLineModel::isVirtualProduct)) {
            return ShippingMethod.VIRTUAL;
        }

        return shippingMethod;
    }
}
