package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.CustomerModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import org.mapstruct.Mapper;
import org.springframework.core.convert.converter.Converter;

import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(unmappedTargetPolicy = IGNORE)
public interface CustomerEntityConverter extends Converter<CustomerModel, Customer> {
}
