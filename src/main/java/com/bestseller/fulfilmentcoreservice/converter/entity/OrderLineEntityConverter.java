package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.ValidOrderPlacedOrderLineModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import org.mapstruct.Mapper;
import org.springframework.core.convert.converter.Converter;

import static org.mapstruct.ReportingPolicy.IGNORE;

@Mapper(unmappedTargetPolicy = IGNORE)
public interface OrderLineEntityConverter extends Converter<ValidOrderPlacedOrderLineModel, OrderLine> {

}
