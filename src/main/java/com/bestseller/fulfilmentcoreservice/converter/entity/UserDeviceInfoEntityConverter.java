package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.CustomerModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.UserDeviceInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface UserDeviceInfoEntityConverter {
    @Mapping(target = "locale", source = "customerModel.customerLocale")
    @Mapping(target = "platform", source = "customerModel.platform")
    @Mapping(target = "appVersion", source = "customerModel.appVersion")
    @Mapping(target = "originalScopeId", source = "customerModel.originalScopeId")
    UserDeviceInfo toUserDeviceInfo(CustomerModel customerModel);
}
