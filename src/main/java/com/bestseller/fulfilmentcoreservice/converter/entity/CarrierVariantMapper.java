package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ValueMapping;
import org.springframework.core.convert.converter.Converter;

@Mapper
public interface CarrierVariantMapper extends Converter<String, CarrierVariant> {

    @ValueMapping(source = "", target = MappingConstants.NULL)
    CarrierVariant convert(String carrierVariant);
}
