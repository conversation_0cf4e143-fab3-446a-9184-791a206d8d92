package com.bestseller.fulfilmentcoreservice.core.task;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderFinalizerTask {

    private final OrderService orderService;

    @Scheduled(cron = "${scheduled-tasks.order-finalizer.cron}")
    @SchedulerLock(name = "OrderFinalizerTask_finalizeOrders")
    public void run() {
        log.info("OrderFinalizerTask started");
        int affectedOrders = orderService.finalizeOrders();
        log.info("OrderFinalizerTask completed. Number of finalized orders = {}", affectedOrders);
    }
}
