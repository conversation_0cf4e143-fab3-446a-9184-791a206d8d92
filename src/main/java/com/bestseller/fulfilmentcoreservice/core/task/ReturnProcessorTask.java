package com.bestseller.fulfilmentcoreservice.core.task;

import com.bestseller.fulfilmentcoreservice.core.service.ReturnProcessorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ReturnProcessorTask {

    private final ReturnProcessorService returnProcessorService;

    @Scheduled(cron = "${scheduled-tasks.return-processor.cron}")
    @SchedulerLock(name = "ReturnProcessorTask_processReturn")
    public void processReturn() {
        log.info("Starting Processing returns");
        returnProcessorService.processReturns();
        log.info("Return processing completed");
    }
}
