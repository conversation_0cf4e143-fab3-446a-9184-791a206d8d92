package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * This class represents the Order Line, a subtype of OrderEntry. The Order Line
 * indicates whether the entry is a bonus product and has OrderLineQtyStatus
 * entries, one per quantity in the line allowing line quantities to occupy
 * different states within the order lifecycle process. It uses persistence
 * annotations to map the object to the database.
 */

@Getter
@Setter
@SuperBuilder
public class ValidOrderPlacedOrderLineModel extends OrderEntryModel {
    private UUID ecomId;
    private boolean bonusProduct;
    private Integer lineNumber;
    @Builder.Default
    private List<OrderLineQtyStatusModel> orderLineQtyStatus = new ArrayList<>();
    private String brandDescription;
    private boolean virtualProduct;
    private String skuId;
    private String partnerReference;
}
