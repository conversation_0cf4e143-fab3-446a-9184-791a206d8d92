package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * This class represents the Order Line Quantity Status. Each represents the order state of the quantity in the order
 * and allows line quantities to occupy different states within the order lifecycle process. It uses persistence
 * annotations to map the object to the database.
 */
@Getter
@Setter
@Builder
public class OrderLineQtyStatusModel {
    private TradebyteOrderLineQtyStatusModel tradebyteOrderLineQtyStatus;
}
