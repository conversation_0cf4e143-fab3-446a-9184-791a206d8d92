package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerModel {
    private String customerId;
    private String name;
    private String email;
    private String employeeId;
    private String type;
    private Boolean isLoggedIn;
    private String externalCustomerId;
    private String customerLocale;
    private String appVersion;
    private String originalScopeId;
    private String platform;
}
