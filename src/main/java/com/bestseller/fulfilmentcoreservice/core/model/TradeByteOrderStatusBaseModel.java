package com.bestseller.fulfilmentcoreservice.core.model;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.logistics.statetransition.OrderState;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class TradeByteOrderStatusBaseModel {
    private Integer lineNumber;
    private Integer quantity;
    private String orderId;
    private String ean;
    private OrderState orderState;
    private String trackingNumber;
    private String returnShipmentId;
    private List<OrderLine> orderLines;
}
