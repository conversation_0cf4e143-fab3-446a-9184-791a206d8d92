package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderForFulfilmentModel {
    private String orderId;
    private LocalDate announcedDeliveryDate;
    private CustomerInformationModel customerInformation;
    private Boolean isTest;
    private List<PaymentModel> payments;
    private OrderDetailsModel orderDetails;
    private List<OrderLineModel> orderLines;
    private ZonedDateTime placedDate;
    private String marketPlace;
    private String store;
    private String platform;
    private String channel;
    private String brand;
    private String actionCode;
    private ShippingInformationModel shippingInformation;
    private String partnerChannel;
    private FulfillmentAdviceModel fulfillmentAdvice;
    private List<AdditionalOrderInformationModel> additionalOrderInformation;
}
