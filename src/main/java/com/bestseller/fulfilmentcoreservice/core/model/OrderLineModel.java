package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.UUID;

@Getter
@Setter
@Builder
public class OrderLineModel {
    private UUID ecomId;
    private BigDecimal discountValue;
    private String ean;
    private Integer lineNumber;
    private String productName;
    private Integer quantity;
    private BigDecimal retailPrice;
    private BigDecimal taxPercentage;
    private String partnerReference;
    private String brand;
    private String promotionId;
    private Boolean virtualProduct;
}
