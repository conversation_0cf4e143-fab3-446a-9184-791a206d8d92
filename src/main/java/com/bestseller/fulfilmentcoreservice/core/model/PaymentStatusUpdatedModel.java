package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class PaymentStatusUpdatedModel {
    private String orderId;
    private List<PaymentModel> payments;
    private OrderDetailsModel orderDetails;
    private List<OrderLineModel> orderLines;
}
