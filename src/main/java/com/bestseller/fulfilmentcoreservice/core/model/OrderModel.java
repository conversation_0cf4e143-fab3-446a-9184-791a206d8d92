package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

@Getter
@Setter
@Builder
public class OrderModel {
    private String orderId;
    private ZonedDateTime orderDate;
    private LocalDate announcedDeliveryDate;
    private String checkoutScopeId;
    private String market;
    private String platform;
    private String channel;
    private String shippingMethod;
    private String externalOrderId;
    private boolean test;
    private Boolean billToMatchesShipTo;
    private AddressModel shippingAddress;
    private AddressModel billingAddress;
    private CustomerModel customer;
    private String brandedShipping;
    private List<ValidOrderPlacedOrderLineModel> orderLines;
    private String currency;
    private BigDecimal orderValue;
    private List<AdditionalOrderInformationModel> additionalOrderInformation;
    private String isoStoreId;
    private Boolean offlinePayment;
}
