package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * The OrderEntry contains basic information about the line/charge including taxrates, quantities and the ean (ID) of
 * the product or service. It uses persistence annotations to map the object to the database.
 */
@Getter
@Setter
@SuperBuilder
public abstract class OrderEntryModel {
    private String type;
    private String name;
    private String ean;
    private BigDecimal taxRate;
    private String vatClassId;
    private Integer originalQty;
    private Integer openQty;
    private String promotionId;
    private String campaignId;
    private String couponId;
    private BigDecimal standardRetailPrice;

}
