package com.bestseller.fulfilmentcoreservice.core.model;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderFulfilmentPartModel {

    private Integer partNumber;

    private String fulfillmentNode;

    private Order order;

    private Boolean holdFromRouting;

    private LocalDateTime dispatchDate;

    private String trackingNumber;

    private String returnTrackingNumber;

    private LocalDate orangePrinted;

    private String carrierName;

    private Integer storeId;

    private List<EanQuantityPair> eanQuantityPairs;

    @Builder
    @Getter
    @Setter
    public static class EanQuantityPair {
        private String ean;
        private Integer quantity;
    }
}
