package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddressModel {
    private String salutation;
    private String postCode;
    private String firstName;
    private String lastName;
    private String houseNumber;
    private String houseNumberExt;
    private String address1;
    private String address2;
    private String address3;
    private String carrierVariant;
    private String deliveryOption;
    private String countryCode;
    private String city;
    private String state;
    private String phone;
    private String parcelLocker;
    private String physicalStoreId;
}
