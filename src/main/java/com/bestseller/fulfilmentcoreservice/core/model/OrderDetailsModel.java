package com.bestseller.fulfilmentcoreservice.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class OrderDetailsModel {
    private String carrier;
    private String carrierVariant;
    private String shippingMethod;
    private String externalOrderNumber;
    private ZonedDateTime orderCreationDate;
    private BigDecimal orderValue;
    private String orderType;
    private BigDecimal shippingFees;
    private BigDecimal shippingFeesTaxPercentage;
    private Boolean shippingFeesCancelled;
    private String currency;
    private String checkout;
    private String isoStoreId;
}
