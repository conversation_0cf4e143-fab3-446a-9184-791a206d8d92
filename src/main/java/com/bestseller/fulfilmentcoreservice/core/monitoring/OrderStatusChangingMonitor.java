package com.bestseller.fulfilmentcoreservice.core.monitoring;

import com.logistics.statetransition.OrderState;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderStatusChangingMonitor {

    private static final String METRIC_NAME = "order.status.changing.total";
    private static final String TAG_NAME = "status";

    private final ConcurrentMap<OrderState, Counter> orderStatusToCounterMap;

    public OrderStatusChangingMonitor(MeterRegistry meterRegistry) {
        orderStatusToCounterMap = Arrays.stream(OrderState.values())
            .collect(Collectors.toConcurrentMap(
                    Function.identity(),
                    orderStatus -> Counter.builder(METRIC_NAME)
                        .description("Counter for order status changing")
                        .tag(TAG_NAME, orderStatus.name().toLowerCase())
                        .register(meterRegistry)
                )
            );
    }

    public void increment(OrderState orderState) {
        orderStatusToCounterMap.get(orderState).increment();
    }

}
