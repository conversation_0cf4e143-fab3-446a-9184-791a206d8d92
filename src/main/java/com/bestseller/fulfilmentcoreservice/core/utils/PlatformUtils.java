package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.exception.ValidationException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.logistics.statetransition.Platform;

public final class PlatformUtils {
    private static final String OLYMPUS_ORDER_PREFIX = "OL";
    private static final String TRADE_BYTE_ORDER_PREFIX = "TB";
    private static final int PHOENIX_ORDER_ID_SIZE = 11;

    private PlatformUtils() {
    }

    /**
     * parse platform.
     *
     * @param message
     * @return platform
     */
    public static Platform parsePlatform(OrderPlaced message) {
        if (isTradeByteOrder(message)) {
            return Platform.TRADEBYTE;
        } else if (isDemandwareOrder(message)) {
            return Platform.DEMANDWARE;
        } else {
            return getPlatformFromOrderId(message.getOrderId());
        }
    }

    private static boolean isDemandwareOrder(OrderPlaced message) {
        return Platform.DEMANDWARE.name().equals(message.getStore())
            || Platform.DEMANDWARE.name().equals(message.getCustomerInformation().getPlatform());
    }

    private static boolean isTradeByteOrder(OrderPlaced message) {
        return Platform.TRADEBYTE.name().equals(message.getStore());
    }

    private static Platform getPlatformFromOrderId(String orderId) {
        if (orderId.contains(TRADE_BYTE_ORDER_PREFIX)) {
            return Platform.TRADEBYTE;
        } else if (orderId.length() == PHOENIX_ORDER_ID_SIZE || orderId.contains(OLYMPUS_ORDER_PREFIX)) {
            return Platform.DEMANDWARE;
        } else {
            throw new ValidationException("Unable to determine platform from order id %s.".formatted(orderId));
        }
    }
}
