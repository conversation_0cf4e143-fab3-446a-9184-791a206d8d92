package com.bestseller.fulfilmentcoreservice.core.utils;

import com.logistics.statetransition.OrderState;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class OrderStateUtils {
    public static Set<OrderState> getGreaterThanOrEqualsStates(OrderState orderState) {
        return Stream.of(OrderState.values())
            .filter(s -> s.isGreaterThanOrEquals(orderState))
            .collect(Collectors.toSet());
    }
}
