package com.bestseller.fulfilmentcoreservice.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.text.MessageFormat;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ValidationException extends RuntimeException {
    /**
     * Constant that represent invalid field template.
     */
    public static final String INVALID_FIELD_TEMPLATE = "\"{0}\" must be valid!";

    /**
     * Constant that represent null or empty field template.
     */
    public static final String NULL_OR_EMPTY_FIELD_TEMPLATE = "\"{0}\" must not be empty or null!";

    /**
     * Constant that represent length field template.
     */
    public static final String LENGTH_FIELD_TEMPLATE = "\"{0}\" must be less than {1}!";

    /**
     * Default constructor.
     *
     * @param message for the exception.
     */
    public ValidationException(String message) {
        super(message);
    }

    /**
     * Constructor for template messages.
     *
     * @param messageTemplate string template for {@link MessageFormat}.
     * @param args            arguments to be populated in the template.
     * @see MessageFormat
     */
    public ValidationException(String messageTemplate, Object... args) {
        super(MessageFormat.format(messageTemplate, args));
    }
}
