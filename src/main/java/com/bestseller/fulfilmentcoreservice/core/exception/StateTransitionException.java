package com.bestseller.fulfilmentcoreservice.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * The exception is thrown when unappropriated order state is applied on the order.
 */
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class StateTransitionException extends RuntimeException {

    /**
     * Constructor.
     *
     * @param message
     * @param orderId
     */
    public StateTransitionException(String message, String orderId) {
        super(prepareMessage(message, orderId));
    }

    /**
     * Constructor.
     *
     * @param message
     */
    public StateTransitionException(String message) {
        super(prepareMessage(message, null));
    }

    private static String prepareMessage(String message, String orderId) {
        return "Order: %s | ".concat(message).formatted(orderId == null ? "Not present" : orderId);
    }
}
