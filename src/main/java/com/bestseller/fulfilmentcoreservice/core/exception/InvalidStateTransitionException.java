package com.bestseller.fulfilmentcoreservice.core.exception;

import com.logistics.statetransition.OrderState;

import java.util.List;
import java.util.stream.Collectors;

public class InvalidStateTransitionException extends StateTransitionException {

    public InvalidStateTransitionException(String orderId, String ean, OrderState fromState, OrderState toState) {
        super("Unacceptable state transition from %s to %s for orderId: %s and ean: %s"
            .formatted(fromState, toState, orderId, ean));
    }

    public InvalidStateTransitionException(String orderId,
                                           String ean,
                                           List<OrderState> fromStates,
                                           OrderState toState) {
        super("Unacceptable state transition from these %s to %s for orderId: %s and ean: %s"
            .formatted(fromStates.stream().map(OrderState::name).collect(Collectors.joining(",")),
                toState,
                orderId,
                ean
            ));
    }
}
