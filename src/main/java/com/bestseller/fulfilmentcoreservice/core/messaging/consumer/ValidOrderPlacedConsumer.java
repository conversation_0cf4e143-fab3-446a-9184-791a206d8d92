package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import lombok.extern.slf4j.Slf4j;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ValidOrderPlacedConsumer extends AbstractServiceDrivenConsumer<ValidOrderPlaced> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    public ValidOrderPlacedConsumer(
        MessageFilter<ValidOrderPlaced> messageFilter,
        MessageValidator<ValidOrderPlaced> messageValidator,
        IdempotencyChecker<ValidOrderPlaced> idempotencyChecker,
        MessageConsumerService<ValidOrderPlaced> messageConsumerService,
        QueueProducer<ValidOrderPlaced> queueProducer
    ) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(ValidOrderPlaced message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    protected String getMessageKey(ValidOrderPlaced message) {
        return message.getOrderId();
    }

}
