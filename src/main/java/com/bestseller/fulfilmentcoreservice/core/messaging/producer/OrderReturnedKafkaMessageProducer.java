package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class OrderReturnedKafkaMessageProducer extends AbstractKafkaMessageProducer<OrderReturned> {

    private static final String BINDING_NAME = "orderReturned-out-0";
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderReturnedKafkaMessageProducer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderReturnedKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(OrderReturned message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
