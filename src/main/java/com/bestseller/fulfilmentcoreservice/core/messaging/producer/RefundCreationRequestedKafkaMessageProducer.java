package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class RefundCreationRequestedKafkaMessageProducer extends AbstractKafkaMessageProducer<RefundCreationRequested> {

    private static final String BINDING_NAME = "refundCreationRequested-out-0";
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundCreationRequestedKafkaMessageProducer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundCreationRequestedId=%s";

    public RefundCreationRequestedKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(RefundCreationRequested message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getRefundCreationRequestedId());
    }

}
