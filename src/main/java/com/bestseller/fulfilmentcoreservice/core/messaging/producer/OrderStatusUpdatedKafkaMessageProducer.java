package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class OrderStatusUpdatedKafkaMessageProducer extends AbstractKafkaMessageProducer<OrderStatusUpdated> {

    private static final String BINDING_NAME = "orderStatusUpdated-out-0";
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatusUpdatedKafkaMessageProducer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, type=%s";

    public OrderStatusUpdatedKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(OrderStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getType());
    }
}
