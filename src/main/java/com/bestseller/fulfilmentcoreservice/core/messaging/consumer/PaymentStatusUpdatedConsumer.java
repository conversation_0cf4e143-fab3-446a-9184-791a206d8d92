package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PaymentStatusUpdatedConsumer extends AbstractServiceDrivenConsumer<PaymentStatusUpdated> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, paymentState=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .build();

    public PaymentStatusUpdatedConsumer(
        MessageFilter<PaymentStatusUpdated> messageFilter,
        MessageValidator<PaymentStatusUpdated> messageValidator,
        IdempotencyChecker<PaymentStatusUpdated> idempotencyChecker,
        MessageConsumerService<PaymentStatusUpdated> messageConsumerService,
        QueueProducer<PaymentStatusUpdated> queueProducer
    ) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(PaymentStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getPaymentState());
    }

    @Override
    protected String getMessageKey(PaymentStatusUpdated message) {
        return message.getOrderId();
    }
}
