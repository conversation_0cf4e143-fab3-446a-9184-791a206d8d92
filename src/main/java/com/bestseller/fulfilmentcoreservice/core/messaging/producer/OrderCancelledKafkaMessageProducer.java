package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class OrderCancelledKafkaMessageProducer extends AbstractKafkaMessageProducer<OrderCancelled> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCancelledKafkaMessageProducer.class);
    private static final String BINDING_NAME = "orderCancelled-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderCancelledKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(OrderCancelled message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
