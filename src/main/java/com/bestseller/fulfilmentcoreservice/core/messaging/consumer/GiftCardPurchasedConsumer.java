package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class GiftCardPurchasedConsumer extends AbstractServiceDrivenConsumer<GiftCardPurchased> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, giftCardNumber=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .build();

    protected GiftCardPurchasedConsumer(
        MessageFilter<GiftCardPurchased> messageFilter,
        MessageValidator<GiftCardPurchased> messageValidator,
        IdempotencyChecker<GiftCardPurchased> idempotencyChecker,
        MessageConsumerService<GiftCardPurchased> messageConsumerService,
        QueueProducer<GiftCardPurchased> queueProducer) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(GiftCardPurchased message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getGiftCardNumber());
    }

    @Override
    protected String getMessageKey(GiftCardPurchased message) {
        return message.getOrderId();
    }
}
