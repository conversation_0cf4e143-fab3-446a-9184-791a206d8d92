package com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.springframework.stereotype.Component;

@Component
public class ValidOrderPlacedIdempotencyCheck extends IdempotencyCheckHandler<ValidOrderPlaced> {

    public ValidOrderPlacedIdempotencyCheck(OrderService orderService) {
        super(orderService);
    }

    @Override
    public boolean isDuplicate(ValidOrderPlaced message) {
        return getOrderService().exists(message.getOrderId());
    }
}
