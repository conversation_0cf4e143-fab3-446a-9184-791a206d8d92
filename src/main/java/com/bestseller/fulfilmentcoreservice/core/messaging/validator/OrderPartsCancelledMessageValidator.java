package com.bestseller.fulfilmentcoreservice.core.messaging.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import jakarta.transaction.Transactional;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderPartsCancelledMessageValidator extends BaseMessageValidator<OrderPartsCancelled> {

    private final OrderService orderService;

    protected OrderPartsCancelledMessageValidator(Validator validator, OrderService orderService) {
        super(validator);
        this.orderService = orderService;
    }

    @Override
    @Transactional
    @SuppressWarnings("IllegalCatch")
    public boolean passesCustomValidation(OrderPartsCancelled message) {
        Order order = orderService.findById(message.getOrderId());
        message.getOrderLines()
            .forEach(cancelledOrderLine -> {
                order.getOrderLines().stream()
                    .filter(orderLine -> orderLine.getEan().equals(cancelledOrderLine.getEan()))
                    .forEach(orderLine -> {
                        if (orderLine.getOpenQty() < cancelledOrderLine.getQuantity()) {
                            throw new IllegalArgumentException(String.format("Cancelled quantity is greater "
                                    + "than available quantity for order with id"
                                    + " :%s and ean :%s and requested quantity :%d, available quantity :%d",
                                order.getOrderId(), orderLine.getEan(),
                                cancelledOrderLine.getQuantity(), orderLine.getOpenQty()));
                        }
                    });
            });
        return true;
    }
}
