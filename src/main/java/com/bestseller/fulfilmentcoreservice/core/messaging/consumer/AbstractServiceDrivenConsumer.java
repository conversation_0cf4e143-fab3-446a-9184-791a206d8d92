package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.classify.BinaryExceptionClassifier;

@Slf4j
public abstract class AbstractServiceDrivenConsumer<T> extends AbstractRetryableConsumer<T> {

    private final MessageConsumerService<T> messageConsumerService;

    protected AbstractServiceDrivenConsumer(
        MessageFilter<T> messageFilter,
        MessageValidator<T> messageValidator,
        IdempotencyChecker<T> idempotency<PERSON><PERSON><PERSON>,
        BinaryExceptionClassifier exceptionClassifier,
        QueueProducer<T> queueProducer,
        MessageConsumerService<T> messageConsumerService) {
        super(messageFilter, messageValidator, idempotencyChecker, exceptionClassifier, queueProducer);
        this.messageConsumerService = messageConsumerService;
    }

    @Override
    public final void consume(T message) {
        messageConsumerService.process(message);
    }
}
