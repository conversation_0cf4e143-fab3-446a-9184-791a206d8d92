package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class OrderStatusUpdateTradebyteKafkaMessageProducer
    extends AbstractKafkaMessageProducer<OrderStatusUpdateTradebyte> {

    private static final String BINDING_NAME = "orderStatusUpdateTradeByte-out-0";
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatusUpdateTradebyteKafkaMessageProducer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderStatusUpdateTradebyteKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(OrderStatusUpdateTradebyte message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
