package com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import org.springframework.stereotype.Component;

@Component
public class OrderPartsRoutedIdempotencyCheck extends IdempotencyCheckHandler<OrderPartsRouted> {

    public OrderPartsRoutedIdempotencyCheck(OrderService orderService) {
        super(orderService);
    }

    @Override
    public boolean isDuplicate(OrderPartsRouted orderPartsRouted) {
        return getOrderService().isDuplicateRequest(orderPartsRouted);
    }

}
