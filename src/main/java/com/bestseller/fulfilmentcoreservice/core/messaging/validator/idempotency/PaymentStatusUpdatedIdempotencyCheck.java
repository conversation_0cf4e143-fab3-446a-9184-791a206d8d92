package com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import org.springframework.stereotype.Component;

@Component
public class PaymentStatusUpdatedIdempotencyCheck extends IdempotencyCheckHandler<PaymentStatusUpdated> {

    public PaymentStatusUpdatedIdempotencyCheck(OrderService orderService) {
        super(orderService);
    }

    @Override
    public boolean isDuplicate(PaymentStatusUpdated message) {
        return getOrderService().isPaymentAuthorised(message.getOrderId());
    }

}
