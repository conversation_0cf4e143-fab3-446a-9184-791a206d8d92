package com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.logistics.statetransition.OrderState;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
@Component
@RequiredArgsConstructor
public class IdempotencyCheckHandler<T> implements IdempotencyChecker<T> {

    private static final Map<Class<?>, OrderState> STATE_MAP = new HashMap<>();

    static {
        STATE_MAP.put(OrderLineExported.class, OrderState.EXPORTED);
        STATE_MAP.put(OrderLineAcknowledged.class, OrderState.PROCESSING);
        STATE_MAP.put(OrderPartsCancelled.class, OrderState.CANCELLED);
        STATE_MAP.put(OrderLineDispatched.class, OrderState.DISPATCHED);
        STATE_MAP.put(GiftCardPurchased.class, OrderState.DISPATCHED);
        STATE_MAP.put(OrderLineReturned.class, OrderState.RETURNED);
        STATE_MAP.put(OrderReturnedInStore.class, OrderState.POS_RETURNED_IN_STORE);
    }

    private final OrderService orderService;

    protected String getOrderId(T message) {
        try {
            Method getOrderIdMethod = message.getClass().getMethod("getOrderId");
            return (String) getOrderIdMethod.invoke(message);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.debug("OrderId retrieval failed for {} : {}", message.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    private OrderState getTargetState(T message) {
        return STATE_MAP.get(message.getClass());
    }

    private String getEan(T message) {
        try {
            Method getEanMethod = message.getClass().getMethod("getEan");
            return (String) getEanMethod.invoke(message);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.debug("EAN retrieval failed for {} : {}", message.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    @Override
    public boolean isDuplicate(T message) {
        String ean = getEan(message);
        OrderState targetState = getTargetState(message);

        if (ean == null || ean.isBlank()) {
            return orderService.isDuplicateRequest(getOrderId(message), targetState);
        }
        return orderService.isDuplicateRequest(getOrderId(message), ean, targetState);
    }
}
