package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class OrderPartsRoutedConsumer extends AbstractServiceDrivenConsumer<OrderPartsRouted> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, orderLines=%s, fulfillmentNode=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .retryOn(InvalidStateTransitionException.class)
        .build();

    public OrderPartsRoutedConsumer(
        MessageFilter<OrderPartsRouted> messageFilter,
        MessageValidator<OrderPartsRouted> messageValidator,
        IdempotencyChecker<OrderPartsRouted> idempotencyChecker,
        MessageConsumerService<OrderPartsRouted> messageConsumerService,
        QueueProducer<OrderPartsRouted> queueProducer) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(OrderPartsRouted message) {
        return MESSAGE_DETAILS_TEMPLATE
            .formatted(message.getOrderId(), message.getOrderLines(), message.getFulfillmentNode());
    }

    @Override
    protected String getMessageKey(OrderPartsRouted message) {
        return message.getOrderId();
    }
}
