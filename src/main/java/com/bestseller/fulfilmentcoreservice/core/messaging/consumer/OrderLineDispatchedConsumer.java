package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class OrderLineDispatchedConsumer extends AbstractServiceDrivenConsumer<OrderLineDispatched> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .retryOn(InvalidStateTransitionException.class)
        .build();

    public OrderLineDispatchedConsumer(
        MessageFilter<OrderLineDispatched> messageFilter,
        MessageValidator<OrderLineDispatched> messageValidator,
        IdempotencyChecker<OrderLineDispatched> idempotencyChecker,
        QueueProducer<OrderLineDispatched> queueProducer,
        MessageConsumerService<OrderLineDispatched> messageConsumerService) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(OrderLineDispatched message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }

    @Override
    protected String getMessageKey(OrderLineDispatched message) {
        return message.getOrderId();
    }
}
