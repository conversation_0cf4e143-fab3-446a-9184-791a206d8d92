package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class OrderLineExportedConsumer extends AbstractServiceDrivenConsumer<OrderLineExported> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .retryOn(InvalidStateTransitionException.class)
        .build();

    public OrderLineExportedConsumer(
        MessageFilter<OrderLineExported> messageFilter,
        MessageValidator<OrderLineExported> messageValidator,
        IdempotencyChecker<OrderLineExported> idempotencyChecker,
        MessageConsumerService<OrderLineExported> messageConsumerService,
        QueueProducer<OrderLineExported> queueProducer) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(OrderLineExported message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }

    @Override
    protected String getMessageKey(OrderLineExported message) {
        return message.getOrderId();
    }
}
