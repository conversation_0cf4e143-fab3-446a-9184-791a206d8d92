package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderReturnedInStoreConsumer extends AbstractServiceDrivenConsumer<OrderReturnedInStore> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(OrderNotFoundException.class)
        .retryOn(TransientDataAccessException.class)
        .retryOn(InvalidStateTransitionException.class)
        .build();

    public OrderReturnedInStoreConsumer(
        MessageFilter<OrderReturnedInStore> messageFilter,
        MessageValidator<OrderReturnedInStore> messageValidator,
        IdempotencyChecker<OrderReturnedInStore> idempotencyChecker,
        MessageConsumerService<OrderReturnedInStore> messageConsumerService,
        @Qualifier("orderReturnedInStoreQueueProducer") QueueProducer<OrderReturnedInStore> queueProducer) {
        super(
            messageFilter,
            messageValidator,
            idempotencyChecker,
            EXCEPTION_CLASSIFIER,
            queueProducer,
            messageConsumerService
        );
    }

    @Override
    protected String getMessageDetails(OrderReturnedInStore message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getReturnId());
    }

    @Override
    protected String getMessageKey(OrderReturnedInStore message) {
        return message.getOrderId();
    }
}

