package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class RefundRequestedKafkaMessageProducer extends AbstractKafkaMessageProducer<OrderReturnedInStore> {

    private static final String BINDING_NAME = "refundRequested-out-0";
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundRequestedKafkaMessageProducer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s";

    public RefundRequestedKafkaMessageProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(OrderReturnedInStore message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getReturnId());
    }
}
