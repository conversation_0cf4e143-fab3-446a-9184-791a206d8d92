package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.api.dto.RefundOptionsResponse;
import com.bestseller.fulfilmentcoreservice.api.dto.ReturnReasonCodesResponse;

public interface OrderRefundService {
    void processRefund(String orderId);

    RefundOptionsResponse getInStoreRefundOptions(String orderId);

    ReturnReasonCodesResponse getReturnReasonCodes(String refundRequestId);
}
