package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderDispatchedStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLineDispatchedServiceImpl implements OrderLineDispatchedService {

    private final OrderService orderService;

    private final OrderFulfilmentPartService orderFulfillmentPartService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderDispatchedStatusUpdatedPayloadConverter payloadConverter;

    private final TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    private final OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    private final StateChangeService stateChangeService;

    @Override
    @Transactional
    public void process(OrderLineDispatched orderLineDispatched) {

        var order = orderService.findById(orderLineDispatched.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderLineDispatched, order);

        orderStateService.apply(order, orderStateChange);
        log.debug("Process orderLineDispatched has updated order status to DISPATCHED "
            + "and saved into db for orderId {}", order.getOrderId());

        orderFulfillmentPartService.handleOrderFulfilmentParts(order,
            orderFulfillmentPartConverter.toOrderFulfillmentPartModel(order, orderLineDispatched));

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdatedConverter.convertTo(
            order, payloadConverter.convert(
                order.getOrderLines(), orderLineDispatched), OrderStatusUpdated.Type.DISPATCHED)
        ));

        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(
            tradeByteOrderStatusBaseConverter.toDispatched(orderLineDispatched), order);
    }
}
