package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;
import com.bestseller.fulfilmentcoreservice.persistence.repository.PartnerChannelRepository;
import com.logistics.statetransition.Platform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PartnerChannelServiceImpl implements PartnerChannelService {

    private final PartnerChannelRepository partnerChannelRepository;

    @Override
    public PartnerChannel getPartnerChannel(Order order, String channel) {
        return order.getPlatform() == Platform.DEMANDWARE
            ? null
            : partnerChannelRepository.findById(channel)
            .orElseThrow(() -> new RuntimeException("PartnerChannel not found for id " + channel));
    }

}
