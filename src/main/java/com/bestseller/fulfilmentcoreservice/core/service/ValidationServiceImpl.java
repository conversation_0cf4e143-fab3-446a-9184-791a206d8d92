package com.bestseller.fulfilmentcoreservice.core.service;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Set;

@Service
@Validated
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

    private final Validator validator;

    @Override
    public void validate(@Valid @NotNull Object object) throws ConstraintViolationException {
    }

    @Override
    public <T> Set<ConstraintViolation<T>> getValidationErrors(T t) {
        return validator.validate(t);
    }

}
