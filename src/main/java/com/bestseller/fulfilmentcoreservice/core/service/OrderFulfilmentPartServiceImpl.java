package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderFulfillmentPartServiceException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFulfillmentPartRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderFulfilmentPartServiceImpl implements OrderFulfilmentPartService {

    private final OrderFulfillmentPartRepository orderFulfillmentPartRepository;

    private final OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    private final OrderFulfillmentPartUpdaterService orderFulfillmentPartUpdaterService;

    private final OrderRepository orderRepository;

    @Override
    public void handleOrderFulfilmentParts(Order order, OrderFulfilmentPartModel orderFulfillmentPartModel)
        throws OrderFulfillmentPartServiceException {

        var orderFulfillmentPart =
            orderFulfillmentPartConverter.toOrderFulfillmentPart(orderFulfillmentPartModel);

        order.getOrderLines()
            .stream()
            .filter(orderLine -> orderFulfillmentPartModel.getEanQuantityPairs().stream()
                .anyMatch(eanQuantityPair -> eanQuantityPair.getEan().equals(orderLine.getEan())))
            .peek(orderLine -> {
                if (CollectionUtils.isEmpty(orderLine.getOrderLineQtyStatus())) {
                    throw new OrderFulfillmentPartServiceException("Order line quantity status is zero",
                        order.getOrderId(), orderLine.getEan());
                }
            })
            .forEach(orderLine -> {
                var hasTrackingNumber = orderFulfillmentPartModel.getTrackingNumber() != null;
                var quantity = orderFulfillmentPartModel.getEanQuantityPairs().stream()
                    .filter(eanQuantityPair -> eanQuantityPair.getEan().equals(orderLine.getEan()))
                    .findFirst()
                    .map(OrderFulfilmentPartModel.EanQuantityPair::getQuantity)
                    .get();
                if (hasTrackingNumber) {
                    orderLine.getOrderLineQtyStatus().stream()
                        .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderFulfillmentPart() != null)
                        .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderFulfillmentPart().isActive())
                        .limit(quantity)
                        .forEach(orderLineQtyStatus ->
                            createOrUpdate(order, orderLineQtyStatus, orderFulfillmentPart));
                } else {
                    orderLine.getOrderLineQtyStatus()
                        .forEach(orderLineQtyStatus ->
                            createOrUpdate(order, orderLineQtyStatus, orderFulfillmentPart));
                }
            });

        orderRepository.save(order);
    }

    private void createOrUpdate(Order order,
                                OrderLineQtyStatus orderLineQtyStatus,
                                OrderFulfillmentPart orderFulfillmentPart) {

        OrderFulfillmentPart orderFulfillmentPartFromDB = orderLineQtyStatus.getOrderFulfillmentPart();
        // Check if the new fulfillment node is different from the one already assigned to the line.
        // If it is, deactivate the existing node and detach it from all order lines.
        // ASSUMPTION: Once an order part is created, it's never split.
        // So, if one product ID (EAN) is sent to a new warehouse,
        // all other products in the same order are sent to that same warehouse,
        // and the old order part is no longer active.
        if (isDifferentFulfillmentNode(orderFulfillmentPartFromDB, orderFulfillmentPart)) {
            deactivateAndSaveOrderFulfillmentPart(order, orderFulfillmentPartFromDB);
            orderFulfillmentPartFromDB = null;
        }

        if (shouldAttachNewOrderFulfillmentPart(orderFulfillmentPart, orderFulfillmentPartFromDB)) {
            log.info("Attaching OrderFulfillmentPart to OrderLine for the order {}", order.getOrderId());
            orderLineQtyStatus.setOrderFulfillmentPart(orderFulfillmentPart);
        } else {
            orderLineQtyStatus.setOrderFulfillmentPart(orderFulfillmentPartFromDB);
            orderFulfillmentPartUpdaterService.update(orderFulfillmentPartFromDB, orderFulfillmentPart);
        }
    }

    private boolean isDifferentFulfillmentNode(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                               OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        return orderFulfillmentPartFromDB != null
            && orderFulfillmentPartFromDB.getFulfillmentNode() != null
            && !orderFulfillmentPartFromDB.getFulfillmentNode()
            .equals(orderFulfillmentPartFromMessage.getFulfillmentNode());
    }

    private void deactivateAndSaveOrderFulfillmentPart(Order order, OrderFulfillmentPart orderFulfillmentPartFromDB) {
        deactivateOrderFulfillmentPart(order, orderFulfillmentPartFromDB);
        orderFulfillmentPartRepository.save(orderFulfillmentPartFromDB);
    }

    private boolean shouldAttachNewOrderFulfillmentPart(OrderFulfillmentPart orderFulfillmentPart,
                                                        OrderFulfillmentPart orderFulfillmentPartFromDB) {
        boolean noActiveSavedPartsExists = orderFulfillmentPartFromDB == null || !orderFulfillmentPartFromDB.isActive();
        if (noActiveSavedPartsExists) {
            return true;
        }

        if (orderFulfillmentPartFromDB.getTrackingNumber() == null
            && orderFulfillmentPart.getTrackingNumber() != null) {
            return false;
        }

        return !StringUtils.equals(orderFulfillmentPartFromDB.getTrackingNumber(),
            orderFulfillmentPart.getTrackingNumber());
    }

    private void deactivateOrderFulfillmentPart(Order order, OrderFulfillmentPart fulfillmentPartForDeactivation) {
        // Iterates given warehouses and deactivates all OrderFulfillmentParts
        // found for specific warehouse attached to given order.
        fulfillmentPartForDeactivation.setActive(false);
        log.info("OrderFulfillmentPart {} has been deactivated.", fulfillmentPartForDeactivation.getId());
        fulfillmentPartForDeactivation.getOrderLineQtyStatus().forEach(orderLineQtyStatus -> {
            orderLineQtyStatus.setOrderFulfillmentPart(null);
        });
    }
}
