package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFinalizedConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.OrderViewConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderIdOnly;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Clock;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {

    private static final String ORDER_MANUAL_UPDATE_EMAIL_KEY = "email";
    private static final Set<OrderState> CANCELLABLE_ORDER_STATES = Set.of(
        OrderState.PLACED,
        OrderState.BLOCKED
    );

    private final OrderRepository orderRepository;
    private final OrderViewConverter orderViewConverter;
    private final OrderFinalizedConverter orderFinalizedConverter;
    private final QueueProducer<OrderFinalized> orderFinalizedProducerQueueProducer;
    private final TransactionTemplate transactionTemplate;
    private final QueueProducer<OrderManualUpdate> orderManualUpdateProducerQueueProducer;
    private final Clock utcClock;

    @Value("${scheduled-tasks.order-finalizer.query-page-size}")
    private int finalizeOrdersQueryPageSize;

    @Override
    public Order findById(String orderId) {
        return orderRepository.findById(orderId).orElseThrow(() -> new OrderNotFoundException(orderId));
    }

    @Override
    public Optional<Order> findOrderById(String orderId) {
        return orderRepository.findById(orderId);
    }

    @Override
    public Optional<OrderView> findOrder(String orderId) {
        return orderRepository.findById(orderId).map(orderViewConverter::convert);
    }

    @Override
    public boolean exists(String orderId) {
        return orderRepository.existsById(orderId);
    }

    /**
     * Saving action doesn't validate the object because it has been validated before.
     */
    @Override
    public Order save(Order order) {
        return orderRepository.save(order);
    }

    @Override
    @Transactional
    public void authorizePayment(Order order) {
        order.setOrderPaymentAuthorised(Boolean.TRUE);
        orderRepository.save(order);
    }

    @SuppressWarnings("IllegalCatch")
    @Override
    public int finalizeOrders() {
        AtomicInteger finalizedOrdersCount = new AtomicInteger();
        int pageIndex = 0;
        List<OrderIdOnly> orders;

        do {
            orders = orderRepository.findOrdersToBeFinalized(pageIndex++, finalizeOrdersQueryPageSize);
            log.info("Finalizing {} orders", orders.size());

            for (OrderIdOnly orderIdOnly : orders) {
                try {
                    finalizeOrder(orderIdOnly.getOrderId());
                    finalizedOrdersCount.getAndIncrement();
                } catch (Exception e) {
                    log.error("Error finalizing order {}", orderIdOnly.getOrderId(), e);
                }
            }
        } while (!orders.isEmpty());

        return finalizedOrdersCount.get();
    }

    @SuppressWarnings("IllegalCatch")
    private void finalizeOrder(String orderId) {
        transactionTemplate.execute(status -> {
            try {
                var order = findById(orderId);
                order.setFinalized(true);
                save(order);
                orderFinalizedProducerQueueProducer
                    .enqueue(EnqueueParams.create(orderFinalizedConverter.toOrderFinalized(order)));
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public boolean isPaymentAuthorised(String orderId) {
        return Boolean.TRUE.equals(orderRepository.isPaymentAuthorised(orderId));
    }

    @Override
    public boolean isDuplicateRequest(String orderId, String ean, OrderState targetState) {
        var orderStates = orderRepository.getOrderStateByOrderId(orderId, ean);
        return orderStates.stream()
            .map(OrderState::valueOf)
            .min(OrderState::compareTo)
            .map(minOrderState -> minOrderState.isGreaterThanOrEquals(targetState))
            .orElse(false);
    }

    @Override
    public boolean isDuplicateRequest(String orderId, OrderState targetState) {
        var minOrderStatus = orderRepository.getMinOrderStatus(orderId);
        if (minOrderStatus == null) {
            return false;
        }
        return OrderState.valueOf(minOrderStatus).isGreaterThanOrEquals(targetState);
    }

    @Transactional(readOnly = true, noRollbackFor = Exception.class)
    @Override
    public boolean isDuplicateRequest(OrderPartsRouted orderPartsRouted) {
        var order = orderRepository.findById(orderPartsRouted.getOrderId());
        if (order.isEmpty()) {
            return false;
        }
        List<String> incomingEans = orderPartsRouted.getOrderLines()
            .stream()
            .map(OrderLine::getEan)
            .toList();

        String savedFulfilmentNode = order.get().getOrderLines()
            .stream()
            .filter(orderLine -> incomingEans.contains(orderLine.getEan()))
            .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
            .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderFulfillmentPart() != null)
            .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderFulfillmentPart().isActive())
            .map(orderLineQtyStatus -> orderLineQtyStatus.getOrderFulfillmentPart().getFulfillmentNode())
            .findFirst()
            .orElse(null);

        if (savedFulfilmentNode == null) {
            return false;
        }

        String incomingFulfilmentNode = orderPartsRouted.getFulfillmentNode();
        return savedFulfilmentNode.equals(incomingFulfilmentNode);
    }

    @Override
    @Transactional
    public void updateEmailAddress(String orderId, String emailAddress) {
        var order = findById(orderId);
        order.getCustomer().setEmail(emailAddress);
        save(order);
        orderManualUpdateProducerQueueProducer.enqueue(EnqueueParams.create(
            new OrderManualUpdate()
                .withOrderId(orderId)
                .withTimestamp(ZonedDateTime.now(utcClock))
                .withKey(ORDER_MANUAL_UPDATE_EMAIL_KEY)
                .withValue(emailAddress)
        ));
        log.info("Update email address for orderId {} has done successfully", orderId);
    }

    @Override
    public boolean validateIfOrderCanBeCancelled(String orderId) {
        try {
            var order = findById(orderId);
            return CANCELLABLE_ORDER_STATES.contains(order.getMinStatus());
        } catch (OrderNotFoundException e) {
            log.warn("Order with id {} not found", orderId);
            return false;
        }
    }
}
