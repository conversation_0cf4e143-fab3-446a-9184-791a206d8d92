package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
public class OrderPartsCancelledStrategy implements StateChangeStrategy<OrderPartsCancelled> {

    private final OrderService orderService;

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderPartsCancelled orderPartsCancelled, Order order) {
        List<OrderStatusUpdateStateChange> stateChanges = new ArrayList<>();
        order.getOrderLines().stream()
            .flatMap(orderLine ->
                orderPartsCancelled.getOrderLines().stream()
                    .filter(partLine -> partLine.getEan().equals(orderLine.getEan()))
                    .map(partLine -> new AbstractMap.SimpleEntry<>(orderLine, partLine)))
            .forEach(pair -> {
                OrderLine orderLine1 = pair.getKey();
                com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine
                    partLine = pair.getValue();


                stateChanges.addAll(
                    Collections.nCopies(
                        partLine.getQuantity(),
                        OrderStatusUpdateStateChange.builder()
                            .ean(orderLine1.getEan())
                            .orderStatusUpdateInfo(
                                createOrderStatusUpdateInfo(partLine.getCancelReason(),
                                    orderPartsCancelled.getCancellationDate().toLocalDate(),
                                    order.getOrderId()))
                            .orderState(OrderState.CANCELLED)
                            .build())
                );
            });
        return stateChanges;
    }

    private OrderStatusUpdateInfo createOrderStatusUpdateInfo(String cancelReason,
                                                              LocalDate cancellationDate,
                                                              String orderId) {
        return OrderStatusUpdateInfo.builder()
            .order(orderService.findById(orderId))
            .cancelReason(CancelReason.valueOf(cancelReason))
            .cancellationDate(cancellationDate)
            .build();
    }

    @Override
    public Class<OrderPartsCancelled> getMessageType() {
        return OrderPartsCancelled.class;
    }
}
