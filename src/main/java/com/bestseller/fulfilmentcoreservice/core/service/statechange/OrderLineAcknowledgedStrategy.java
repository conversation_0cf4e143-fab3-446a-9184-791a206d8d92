package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class OrderLineAcknowledgedStrategy implements StateChangeStrategy<OrderLineAcknowledged> {

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderLineAcknowledged orderLineAcknowledged, Order order) {
        return Collections.nCopies(
            orderLineAcknowledged.getQuantity(),
            OrderStatusUpdateStateChange.builder()
                .ean(findEan(orderLineAcknowledged, order))
                .orderState(OrderState.PROCESSING)
                .build());
    }

    private String findEan(OrderLineAcknowledged orderLineAcknowledged, Order order) {
        return order.getOrderLines()
            .stream()
            .filter(orderLine -> Objects.equals(orderLine.getEan(), orderLineAcknowledged.getEan()))
            .findFirst()
            .map(OrderLine::getEan)
            .orElseThrow(() -> new IllegalArgumentException(
                "EAN %s not found in order lines for orderId %s".formatted(
                    orderLineAcknowledged.getEan(),
                    orderLineAcknowledged.getOrderId())));
    }

    @Override
    public Class<OrderLineAcknowledged> getMessageType() {
        return OrderLineAcknowledged.class;
    }
}
