package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLineReturnedServiceImpl implements OrderLineReturnedService {

    private final OrderService orderService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    private final OrderStateService orderStateService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final StateChangeService stateChangeService;

    private final OrderReturnInfoService orderReturnInfoService;

    @Override
    @Transactional
    public void process(OrderLineReturned orderLineReturned) {

        Order order = orderService.findById(orderLineReturned.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderLineReturned, order);

        orderStateService.apply(order, orderStateChange);

        orderReturnInfoService.updateLatestReturnDate(
            order.getOrderId(),
            orderLineReturned.getEffectiveDate().toLocalDate());

        log.debug("Process OrderLineReturned has updated order status to RETURNED and saved into db for orderId {}",
            order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(
                order, payloadConverter.convert(order), OrderStatusUpdated.Type.RETURNED)
        ));
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(
            tradeByteOrderStatusBaseConverter.toReturned(orderLineReturned), order);
    }
}
