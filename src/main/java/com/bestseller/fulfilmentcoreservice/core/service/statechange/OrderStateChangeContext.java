package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class OrderStateChangeContext {

    private final Map<Class<?>, StateChangeStrategy<?>> strategies;

    @Autowired
    public OrderStateChangeContext(List<StateChangeStrategy<?>> strategyBeans) {
        strategies = strategyBeans.stream()
            .collect(Collectors.toMap(StateChangeStrategy::getMessageType, Function.identity()));
    }

    @SuppressWarnings("unchecked")
    public <T> List<OrderStatusUpdateStateChange> executeStrategy(T message, Order order) {
        StateChangeStrategy<T> strategy = (StateChangeStrategy<T>) strategies.get(message.getClass());
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported type of message: " + message.getClass().getSimpleName());
        }
        return strategy.defineOrderStateChange(message, order);
    }
}
