package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import org.springframework.stereotype.Service;

@Service
public class OrderFulfillmentPartUpdaterServiceImpl implements OrderFulfillmentPartUpdaterService {

    @Override
    public void update(OrderFulfillmentPart orderFulfillmentPartFromDB,
                       OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        updatePartNumberWhenItIsMissing(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateDispatchDate(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateTrackingNumber(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateReturnTrackingNumber(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateOrangePrinted(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateCarrierName(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateStoreId(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
        updateFulfillmentNode(orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);
    }

    private void updatePartNumberWhenItIsMissing(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                                 OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getPartNumber() != null
            && (orderFulfillmentPartFromDB.getPartNumber() == null
            || orderFulfillmentPartFromDB.getPartNumber() == 0)) {
            orderFulfillmentPartFromDB.setPartNumber(orderFulfillmentPartFromMessage.getPartNumber());
        }
    }

    private void updateFulfillmentNode(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                       OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        orderFulfillmentPartFromDB.setFulfillmentNode(orderFulfillmentPartFromMessage.getFulfillmentNode());
    }

    private void updateStoreId(OrderFulfillmentPart orderFulfillmentPartFromDB,
                               OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getStoreId() != null) {
            orderFulfillmentPartFromDB.setStoreId(orderFulfillmentPartFromMessage.getStoreId());
        }
    }

    private void updateCarrierName(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                   OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getCarrierName() != null) {
            orderFulfillmentPartFromDB.setCarrierName(orderFulfillmentPartFromMessage.getCarrierName());
        }
    }

    private void updateOrangePrinted(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                     OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getOrangePrinted() != null) {
            orderFulfillmentPartFromDB.setOrangePrinted(orderFulfillmentPartFromMessage.getOrangePrinted());
        }
    }

    private void updateReturnTrackingNumber(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                            OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getReturnTrackingNumber() != null) {
            orderFulfillmentPartFromDB.setReturnTrackingNumber(
                orderFulfillmentPartFromMessage.getReturnTrackingNumber());
        }
    }

    private void updateTrackingNumber(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                      OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        orderFulfillmentPartFromDB.setTrackingNumber(orderFulfillmentPartFromMessage.getTrackingNumber());
    }

    private void updateDispatchDate(OrderFulfillmentPart orderFulfillmentPartFromDB,
                                    OrderFulfillmentPart orderFulfillmentPartFromMessage) {
        if (orderFulfillmentPartFromMessage.getDispatchDate() != null) {
            orderFulfillmentPartFromDB.setDispatchDate(orderFulfillmentPartFromMessage.getDispatchDate());
        }
    }
}
