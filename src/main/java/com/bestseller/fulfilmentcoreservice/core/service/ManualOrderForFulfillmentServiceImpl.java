package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.InvalidPaymentStatusUpdatedMessageException;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ManualOrderForFulfillmentServiceImpl implements ManualOrderForFulfillmentService {

    private final PaymentService paymentService;
    private final PaymentStatusUpdatedService paymentStatusUpdatedService;

    @Override
    public void fulfillOrder(Order order) {
        var paymentStatusUpdated = paymentService.getPaymentStatusUpdatedMessage(order.getOrderId());

        if (!isAuthorizedPayment(paymentStatusUpdated)) {
            log.warn("Because Order {} is not authorized, skipping fulfillment", order.getOrderId());
            return;
        }
        if (paymentStatusUpdated.getPayload() == null) {
            throw new InvalidPaymentStatusUpdatedMessageException(order.getOrderId());
        }

        paymentStatusUpdatedService.process(paymentStatusUpdated);
        log.info("OrderForFulfilment triggered for order {}", order.getOrderId());
    }

    private boolean isAuthorizedPayment(PaymentStatusUpdated paymentStatusUpdated) {
        return paymentStatusUpdated != null
            && paymentStatusUpdated.getPaymentState() == PaymentStatusUpdated.PaymentState.AUTHORISED;
    }
}
