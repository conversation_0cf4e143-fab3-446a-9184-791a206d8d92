package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLineExportedServiceImpl implements OrderLineExportedService {

    private final OrderService orderService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final StateChangeService stateChangeService;

    @Override
    @Transactional
    public void process(OrderLineExported orderLineExported) {

        var order = orderService.findById(orderLineExported.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderLineExported, order);

        orderStateService.apply(order, orderStateChange);
        log.debug("Process orderLineExported has updated order status to EXPORTED and saved into db for orderId {}",
            order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdatedConverter.convertTo(
            order, payloadConverter.convert(order), OrderStatusUpdated.Type.EXPORTED)
        ));
    }

}
