package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrderLineDispatchedStrategy implements StateChangeStrategy<OrderLineDispatched> {

    private final OrderService orderService;

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderLineDispatched orderLineDispatched, Order order) {
        String dispatchedWarehouse = orderLineDispatched.getWarehouse();
        List<OrderStatusUpdateStateChange> stateChanges = new ArrayList<>();

        validateEan(orderLineDispatched, order);

        order.getOrderLines()
            .stream()
            .filter(orderLine -> Objects.equals(orderLine.getEan(), orderLineDispatched.getEan()))
            .filter(orderLine -> isLineDispatchedInSameWarehouse(orderLine, dispatchedWarehouse))
            .findFirst()
            .ifPresent(orderLine -> {
                stateChanges.addAll(
                    Collections.nCopies(
                        orderLineDispatched.getQuantity(),
                        OrderStatusUpdateStateChange.builder()
                            .ean(orderLine.getEan())
                            .orderStatusUpdateInfo(createOrderStatusUpdateInfo(orderLineDispatched))
                            .orderState(OrderState.DISPATCHED)
                            .build()
                    ));
            });
        return stateChanges;
    }

    private void validateEan(OrderLineDispatched orderLineDispatched, Order order) {
        order.getOrderLines()
            .stream()
            .filter(orderLine -> Objects.equals(orderLine.getEan(), orderLineDispatched.getEan()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException(
                "EAN %s not found in order lines for orderId %s".formatted(
                    orderLineDispatched.getEan(), orderLineDispatched.getOrderId())));

    }

    private boolean isLineDispatchedInSameWarehouse(OrderLine orderLine, String warehouse) {
        return orderLine.getOrderLineQtyStatus().stream()
            .map(OrderLineQtyStatus::getOrderFulfillmentPart)
            .filter(Objects::nonNull)
            .anyMatch(orderFulfillmentPart -> orderFulfillmentPart.getFulfillmentNode().equalsIgnoreCase(warehouse));
    }

    private OrderStatusUpdateInfo createOrderStatusUpdateInfo(OrderLineDispatched orderLineDispatched) {
        return OrderStatusUpdateInfo.builder()
            .order(orderService.findById(orderLineDispatched.getOrderId()))
            .carrierName(orderLineDispatched.getCarrierName())
            .trackingNumber(orderLineDispatched.getTrackingNumber())
            .returnShipmentId(orderLineDispatched.getReturnShipmentId())
            .dispatchDate(orderLineDispatched.getDispatchDate().toLocalDateTime())
            .actualDispatchDate(LocalDateTime.now())
            .build();
    }

    @Override
    public Class<OrderLineDispatched> getMessageType() {
        return OrderLineDispatched.class;
    }
}
