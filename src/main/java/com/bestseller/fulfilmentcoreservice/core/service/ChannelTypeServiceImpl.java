package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.logistics.statetransition.Platform.TRADEBYTE;

@Slf4j
@Service
public class ChannelTypeServiceImpl implements ChannelTypeService {

    /**
     * Returns the {@link ChannelType} for the given {@link Order} and channel.
     */
    @Override
    public ChannelType getChannelType(Order order, String channel) {
        return TRADEBYTE == order.getPlatform() ? ChannelType.STOREFRONT : ChannelType.fromDemandwareOrderType(channel);
    }
}
