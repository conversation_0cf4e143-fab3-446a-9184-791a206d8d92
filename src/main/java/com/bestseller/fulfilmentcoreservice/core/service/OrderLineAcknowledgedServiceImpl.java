package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLineAcknowledgedServiceImpl implements OrderLineAcknowledgedService {

    private final OrderService orderService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    private final StateChangeService stateChangeService;

    @Override
    @Transactional
    public void process(OrderLineAcknowledged orderLineAcknowledged) {

        var order = orderService.findById(orderLineAcknowledged.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderLineAcknowledged, order);

        orderStateService.apply(order, orderStateChange);
        log.debug("Process orderLineAcknowledged has updated order status to PROCESSING"
            + " and saved into db for orderId {}", order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdatedConverter.convertTo(
            order, orderToOrderStatusUpdatedPayloadConverter.convert(order), OrderStatusUpdated.Type.ACKNOWLEDGED)
        ));
    }
}
