package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.logistics.statetransition.Platform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CurrencyServiceImpl implements CurrencyService {
    private static final String DEFAULT_CURRENCY = "EUR";

    @Override
    public String getCurrency(Order order, String currencyFromPayments) {
        String currency = order.getPlatform() == Platform.TRADEBYTE
            ? order.getPartnerChannel().getCurrency()
            : currencyFromPayments;

        if (StringUtils.isBlank(currency)) {
            log.warn("Using default currency {} for order {}, please check why currency is not set in source order",
                DEFAULT_CURRENCY, order.getOrderId());
            currency = DEFAULT_CURRENCY;
        }
        return currency;
    }
}
