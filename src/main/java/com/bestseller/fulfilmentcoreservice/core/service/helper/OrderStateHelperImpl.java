package com.bestseller.fulfilmentcoreservice.core.service.helper;

import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.logistics.statetransition.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.logistics.statetransition.OrderState.CANCELLED;
import static com.logistics.statetransition.OrderState.PURGE;

@Service
@Slf4j
public class OrderStateHelperImpl implements OrderStateHelper {

    /**
     * Create a map from the input state changes as EAN is used as a key. The
     * map is used to simplify the further state processing.
     *
     * @param stateChanges
     * @return
     */
    @Override
    public <T extends StateChange> Map<String, List<StateChange>> getMapEanToStateChange(List<T> stateChanges) {
        return Optional.ofNullable(stateChanges)
            .stream()
            .flatMap(Collection::stream)
            .collect(Collectors.groupingBy(StateChange::getEan));
    }

    public Map<String, List<StateChange>> getMapEanToStateChangeFromOrderLine(List<OrderLine> orderLines,
                                                                              OrderState stateChange) {
        Map<String, List<StateChange>> eanPlacedStateChanges = new HashMap<>();
        Optional.ofNullable(orderLines)
            .stream()
            .flatMap(Collection::stream)
            .forEach(orderLine -> {
                int statusQuantity = orderLine.getOrderLineQtyStatus().isEmpty()
                    ? orderLine.getOriginalQty()
                    : orderLine.getOrderLineQtyStatus().size();

                eanPlacedStateChanges.put(orderLine.getEan(),
                    IntStream.range(0, statusQuantity)
                        .mapToObj(i -> StateChange.builder()
                            .ean(orderLine.getEan())
                            .orderState(stateChange)
                            .build())
                        .collect(Collectors.toList()));
            });
        return eanPlacedStateChanges;
    }

    @Override
    public void updateMinMax(Order order) {
        OrderState minState = null;
        OrderState maxState = null;
        for (OrderLine orderLine : order.getOrderLines()) {
            Integer lineOpenQty = orderLine.getOriginalQty();
            for (var orderLineQtyStatus : orderLine.getOrderLineQtyStatus()) {
                OrderState currOrderLineState = orderLineQtyStatus.getOrderState();
                minState = getMinMaxState(minState, currOrderLineState, false);
                maxState = getMinMaxState(maxState, currOrderLineState, true);
                if (currOrderLineState.isGreaterThanOrEquals(CANCELLED) && currOrderLineState.isLessThan(PURGE)) {
                    lineOpenQty--;
                }
            }
            orderLine.setOpenQty(lineOpenQty);
        }
        order.setMinStatus(minState);
        order.setMaxStatus(maxState);
    }

    private OrderState getMinMaxState(OrderState minMaxState, OrderState state, boolean isMax) {
        if (minMaxState == null) {
            return state;
        } else if (isMax) {
            if (minMaxState.isLessThan(state)) {
                return state;
            }
        } else if (minMaxState.isGreaterThan(state)) {
            return state;
        }
        return minMaxState;
    }
}
