package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderReturnInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.chrono.ChronoLocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class RefundShipmentFeeServiceImpl implements RefundShipmentFeeService {
    private final OrderReturnInfoService orderReturnInfoService;

    @Value("${days-for-free-return-policy}")
    private int daysForFreeReturn;

    @Value("${countries-excluded-from-refund-shipment-fee}")
    private Set<String> countriesExcludedFromRefundShipmentFee;

    @Override
    @Transactional
    public boolean shouldRefundShipmentFee(Order order) {

        // conditions
        if (isShippingCountryCodeExcludedFromRefundShipmentFeePolicy(order)) {
            return false;
        }

        // if all items in an order are returned, shipping/payment fee is refunded under additional
        boolean allReturned = areAllItemsReturned(order);
        if (!allReturned) {
            return false;
        }

        var latestReturnedDate = getLatestReturnDate(order);

        return order.getOrderLines()
            .stream()
            .filter(orderLine -> !orderLine.isVirtualProduct())
            .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
            .map(OrderLineQtyStatus::getOrderFulfillmentPart)
            .filter(Objects::nonNull)
            .map(OrderFulfillmentPart::getDispatchDate)
            .filter(Objects::nonNull)
            .max(Comparator.naturalOrder())
            .map(
                date -> {
                    // if the latest return date is after the free return limit, then the shipment fee is not refunded
                    var freeReturnLimit = date.plusDays(daysForFreeReturn);
                    return !latestReturnedDate.isAfter(ChronoLocalDate.from(freeReturnLimit));
                })
            .orElse(true);
    }

    private boolean isShippingCountryCodeExcludedFromRefundShipmentFeePolicy(Order order) {
        return Optional.ofNullable(order.getShippingAddress())
            .map(Address::getCountryCode)
            .map(String::toUpperCase)
            .map(countriesExcludedFromRefundShipmentFee::contains)
            .orElse(false);
    }

    private LocalDate getLatestReturnDate(Order order) {
        return order.getOrderLines()
            .stream()
            .filter(item -> !item.isVirtualProduct())
            .map(OrderLine::getOrderLineQtyStatus)
            .flatMap(List::stream)
            .map(OrderLineQtyStatus::getOrderStatusUpdateInfo)
            .filter(Objects::nonNull)
            .map(OrderStatusUpdateInfo::getReturnDate)
            .filter(Objects::nonNull)
            .max(Comparator.naturalOrder())

            // This part can be removed after complete OMS to FCS data migration
            .or(() -> orderReturnInfoService.getOrderReturnInfo(order.getOrderId())
                .map(OrderReturnInfo::getLatestReturnDate))
            .orElse(null);

    }

    private boolean areAllItemsReturned(Order order) {
        List<OrderLine> orderLines = order.getOrderLines()
            .stream()
            .filter(OrderLine::isRegularProduct)
            .toList();

        for (OrderLine orderLine : orderLines) {
            List<OrderLineQtyStatus> qtyStatuses = orderLine.getOrderLineQtyStatus();

            // check if all items of the order are in a RETURNED state or above (i.e. 5000+)
            // in this case shipping and payment fees are eligible for refund
            for (OrderLineQtyStatus qtyStatus : qtyStatuses) {
                if (qtyStatus.getOrderState().isLessThan(OrderState.RETURNED)) {
                    return false;
                }
            }
        }

        return true;
    }
}
