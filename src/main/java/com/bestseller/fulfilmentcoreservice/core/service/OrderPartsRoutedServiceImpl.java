package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPartsRoutedServiceImpl implements OrderPartsRoutedService {

    private final OrderService orderService;

    private final OrderFulfilmentPartService orderFulfillmentPartService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final StateChangeService stateChangeService;

    @Override
    @Transactional
    public void process(OrderPartsRouted orderPartsRouted) {

        Order order = orderService.findById(orderPartsRouted.getOrderId());
        log.info("Incoming orderPartsRouted for updating order status by orderId: {}", order.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderPartsRouted, order);

        orderStateService.apply(order, orderStateChange);
        log.debug("Process orderPartsRouted has updated order status to ROUTED and saved into db for orderId {}",
            order.getOrderId());

        orderFulfillmentPartService.handleOrderFulfilmentParts(order,
            orderFulfillmentPartConverter.toOrderFulfillmentPartModel(order, orderPartsRouted));

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(
                order, payloadConverter.convert(order), OrderStatusUpdated.Type.ROUTED))
        );
    }
}
