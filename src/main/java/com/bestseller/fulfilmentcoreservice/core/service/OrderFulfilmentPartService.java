package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.OrderFulfillmentPartServiceException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;

public interface OrderFulfilmentPartService {
    void handleOrderFulfilmentParts(Order order, OrderFulfilmentPartModel orderFulfilmentPartModel)
        throws OrderFulfillmentPartServiceException;
}
