package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.ValidationException;
import com.bestseller.fulfilmentcoreservice.core.service.validator.PickupPointValidator;
import com.bestseller.fulfilmentcoreservice.core.utils.PlatformUtils;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderPlacedConfig;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingCharge;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingInformation;
import com.logistics.statetransition.Platform;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.bestseller.fulfilmentcoreservice.core.exception.ValidationException.INVALID_FIELD_TEMPLATE;
import static com.bestseller.fulfilmentcoreservice.core.exception.ValidationException.NULL_OR_EMPTY_FIELD_TEMPLATE;
import static com.bestseller.fulfilmentcoreservice.persistence.entity.OrderPlacedConfig.MAX_LENGTH_COUNTRY;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderPlacedValidationServiceImpl implements OrderPlacedValidationService {
    private static final String NL = "NL";
    private static final String ORDER_PLACED = "OrderPlaced";
    private static final String ORDER_PLACED_CUSTOMER_INFO_BILLING_ADDRESS_ZIPCODE =
        "OrderPlaced.customerInformation.billingAddress.zipcode";
    private static final String ORDER_PLACED_SHIPPING_INFORMATION_SHIPPING_ADDRESS_LINE_3 =
        "OrderPlaced.shippingInformation.shippingAddress.addressLine3";
    private static final String ORDER_PLACED_SHIPPING_INFORMATION_SHIPPING_ADDRESS =
        "OrderPlaced.shippingInformation.shippingAddress";
    private static final String ORDER_PLACED_SHIPPING_INFO_SHIPPING_ADDRESS_ZIPCODE =
        "OrderPlaced.shippingInformation.shippingAddress.zipcode";
    private static final String ORDER_PLACED_ORDER_LINES_EAN = "OrderPlaced.orderLines.ean";
    private static final String ORDER_PLACED_SHIPPING_METHOD = "OrderPlaced.orderDetails.shippingMethod";

    private final PickupPointValidator pickupPointValidator;
    private final ValidationService validationService;

    /**
     * Validates fields of an OrderPlaced Kafka message.
     */
    @Override
    public void validate(OrderPlaced message) throws ConstraintViolationException, ValidationException {
        validationService.validate(message);

        doGeneralValidation(message);
        Platform platform = PlatformUtils.parsePlatform(message);

        if (Platform.DEMANDWARE.equals(platform)) {
            log.info("Validating order {} as demandware order", message.getOrderId());
            doDemandwareOrderValidation(message);
        } else {
            log.info("Validating order {} as trade byte order", message.getOrderId());
            doTradeByteOrderValidation(message);
        }
    }

    private void doGeneralValidation(OrderPlaced message) throws ValidationException {

        validateOrderPlacedMessage(message);

        //Validate billing address
        validateCustomerInformation(message.getCustomerInformation());

        //Validate there is fulfillment node.
        if (message.getFulfillmentAdvice() != null
            && StringUtils.isBlank(message.getFulfillmentAdvice().getFulfillmentNode())) {
            throw new ValidationException(
                NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.getFulfillmentAdvice.fulfillmentNode"
            );
        }
    }

    private void validateOrderPlacedMessage(OrderPlaced message) throws ValidationException {
        if (message == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, ORDER_PLACED);
        }

        // Validate there is store
        if (StringUtils.isBlank(message.getStore())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPlaced.store");
        }

        for (OrderLine orderLine : message.getOrderLines()) {
            if (StringUtils.isBlank(orderLine.getEan())) {
                throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, ORDER_PLACED_ORDER_LINES_EAN);
            }
        }

        // Validate there is shipping information
        if (message.getShippingInformation() == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPlaced.shippingInformation");
        }

    }

    private void doDemandwareOrderValidation(OrderPlaced message) throws ValidationException {
        validateShippingMethod(message);
        validateAddressMaxLength(message);
        validateShippingInformationDemandware(message);
    }

    private void doTradeByteOrderValidation(OrderPlaced message) throws ValidationException {
        // Validate that it is not a virtual order
        if (containsVirtualProducts(message)) {
            throw new ValidationException("Virtual order now allowed for trade byte orders.Order id: %s"
                .formatted(message.getOrderId()));
        }

        // Validate that shipping address is there
        validateShippingInformation(message);

        // Validate there is external order number.
        if (StringUtils.isBlank(message.getOrderDetails().getExternalOrderNumber())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, "OrderPlaced.orderDetails.externalOrderNumber");
        }
    }

    private void validateShippingMethod(OrderPlaced message) {
        String shippingMethod = message.getOrderDetails().getShippingMethod();
        if (shippingMethod == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE, ORDER_PLACED_SHIPPING_METHOD);
        } else if (ShippingMethod.getShippingMethod(shippingMethod) == null) {
            throw new ValidationException(INVALID_FIELD_TEMPLATE, ORDER_PLACED_SHIPPING_METHOD);
        } else if (isVirtualShippingMethod(shippingMethod) && isAllProductsVirtual(message.getOrderLines())) {
            throw new ValidationException(
                "Virtual shipping method is only allowed if all order lines are virtual products."
                    + " Order id: %s".formatted(message.getOrderId()));
        }
    }

    private void validateAddressMaxLength(OrderPlaced message) {
        String orderId = message.getOrderId();
        validateCustomerInformation(message.getCustomerInformation(), orderId);

        if (isShippingAddressFilled(message.getShippingInformation())) {
            validateShippingInformation(message.getShippingInformation(), orderId);
        } else {
            log.info("Shipping information not filled, validation of shipping info skipped for order {}",
                message.getOrderId());
        }
    }

    private void validateCustomerInformation(CustomerInformation customerInformation, String orderId) {
        String billingAddress1 = customerInformation.getBillingAddress().getAddressLine1();
        validateNotExceedMaxLength(orderId, billingAddress1, "billing address line 1",
            OrderPlacedConfig.ADDRESS_MAX_SIZE);

        String billingAddress2 = customerInformation.getBillingAddress().getAddressLine2();
        validateNotExceedMaxLength(orderId, billingAddress2, "billing address line 2",
            OrderPlacedConfig.ADDRESS_MAX_SIZE);

        String billingAddress3 = customerInformation.getBillingAddress().getAddressLine3();
        validateNotExceedMaxLength(orderId, billingAddress3, "billing address line 3",
            OrderPlacedConfig.ADDRESS_3_MAX_SIZE);

        String billingCity = customerInformation.getBillingAddress().getCity();
        validateNotExceedMaxLength(orderId, billingCity, "billing address city",
            OrderPlacedConfig.CITY_MAX_SIZE);
    }

    /**
     * Checks for correctness of BillingAddress.
     */
    private void validateCustomerInformation(CustomerInformation customerInformation) throws ValidationException {
        validateBillingAddress(customerInformation.getBillingAddress());

        // Validate NL zip code.
        if (NL.equals(customerInformation.getBillingAddress().getCountry())
            && isInvalidNLPostCode(customerInformation.getBillingAddress().getZipcode())) {
            throw new ValidationException(INVALID_FIELD_TEMPLATE, ORDER_PLACED_CUSTOMER_INFO_BILLING_ADDRESS_ZIPCODE);
        }
    }

    private void validateBillingAddress(Address billingAddress) {
        //Validate there is customer last name.
        if (StringUtils.isBlank(billingAddress.getLastName())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.customerInformation.billingAddress.lastName");
        }

        //Validate there is customer first name.
        if (StringUtils.isBlank(billingAddress.getFirstName())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.customerInformation.billingAddress.firstName");
        }

        //Validate there is city.
        if (StringUtils.isBlank(billingAddress.getCity())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.customerInformation.billingAddress.city");
        }

        //Validate there is address line 1.
        if (StringUtils.isBlank(billingAddress.getAddressLine1())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.customerInformation.billingAddress.addressLine1");
        }

        //Validate there is country.
        if (StringUtils.isBlank(billingAddress.getCountry())
            || StringUtils.length(billingAddress.getCountry()) > MAX_LENGTH_COUNTRY) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.customerInformation.billingAddress.country");
        }

        // Validate there is zip code.
        if (StringUtils.isBlank(billingAddress.getZipcode())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                ORDER_PLACED_CUSTOMER_INFO_BILLING_ADDRESS_ZIPCODE);
        }
    }

    private void validateShippingInformation(ShippingInformation shippingInformation, String orderId) {
        String shippingAddress1 = shippingInformation.getShippingAddress().getAddressLine1();
        validateNotExceedMaxLength(orderId, shippingAddress1, "shipping address line 1",
            OrderPlacedConfig.ADDRESS_MAX_SIZE);

        String shippingAddress2 = shippingInformation.getShippingAddress().getAddressLine2();
        validateNotExceedMaxLength(orderId, shippingAddress2, "shipping address line 2",
            OrderPlacedConfig.ADDRESS_MAX_SIZE);

        String shippingAddress3 = shippingInformation.getShippingAddress().getAddressLine3();
        validateNotExceedMaxLength(orderId, shippingAddress3, "shipping address line 3",
            OrderPlacedConfig.ADDRESS_3_MAX_SIZE);

        String shippingCity = shippingInformation.getShippingAddress().getCity();
        validateNotExceedMaxLength(orderId, shippingCity, "shipping address city",
            OrderPlacedConfig.CITY_MAX_SIZE);
    }

    /**
     * Checks for correctness of Shipping information.
     */
    private void validateShippingInformation(OrderPlaced message) {

        Address shippingAddress = message.getShippingInformation().getShippingAddress();

        // Validate there is customer shipping address.
        validateShippingAddress(shippingAddress);

        // Allow PUDO orders without parcel shop address
        if (Objects.equals(message.getOrderDetails().getCarrierVariant(), CarrierVariant.PICKUP.name())
            && message.getOrderDetails().getIsoStoreId() != null) {
            return;
        }

        // Validate parcel shop address
        if (isParcelShop(message.getOrderDetails().getCarrierVariant())
            && StringUtils.isBlank(shippingAddress.getAddressLine3())) {
            throw new ValidationException("For parcel shop shipping: " + NULL_OR_EMPTY_FIELD_TEMPLATE,
                ORDER_PLACED_SHIPPING_INFORMATION_SHIPPING_ADDRESS_LINE_3);
        }
    }

    private void validateShippingAddress(Address shippingAddress) {
        // Validate there is customer shipping address.
        if (shippingAddress == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                ORDER_PLACED_SHIPPING_INFORMATION_SHIPPING_ADDRESS);
        }

        // Validate there is customer last name.
        if (StringUtils.isBlank(shippingAddress.getLastName())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingAddress.lastName");
        }

        // Validate there is customer first name.
        if (StringUtils.isBlank(shippingAddress.getFirstName())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingAddress.firstName");
        }

        // Validate there is city.
        if (StringUtils.isBlank(shippingAddress.getCity())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingAddress.city");
        }

        // Validate there is address line 1.
        if (StringUtils.isBlank(shippingAddress.getAddressLine1())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingAddress.addressLine1");
        }

        // Validate there is country.
        if (StringUtils.isBlank(shippingAddress.getCountry())
            || StringUtils.length(shippingAddress.getCountry()) > MAX_LENGTH_COUNTRY) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingAddress.country");
        }

        // Validate there is zip code.
        if (StringUtils.isBlank(shippingAddress.getZipcode())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                ORDER_PLACED_SHIPPING_INFO_SHIPPING_ADDRESS_ZIPCODE);
        }

        // Validate NL zip code.
        if (NL.equals(shippingAddress.getCountry()) && isInvalidNLPostCode(shippingAddress.getZipcode())) {
            throw new ValidationException(INVALID_FIELD_TEMPLATE, ORDER_PLACED_SHIPPING_INFO_SHIPPING_ADDRESS_ZIPCODE);
        }
    }

    private boolean isShippingAddressFilled(ShippingInformation shippingInformation) {
        return shippingInformation.getShippingAddress() != null;
    }

    private void validateNotExceedMaxLength(String orderId, String subfield, String subfieldName, int maxSize) {
        if (subfield != null && subfield.length() > maxSize) {
            throw new ValidationException("%s max length is %s for orderId %s"
                .formatted(subfieldName, maxSize, orderId));
        }
    }

    private void validateShippingInformationDemandware(OrderPlaced message) {
        validateShippingCharges(message.getShippingInformation());

        if (isAllProductsVirtual(message.getOrderLines())) {
            validateShippingInformation(message);
            validatePickupPoint(message);
        }
    }

    private void validateShippingCharges(ShippingInformation shippingInformation) {
        if (shippingInformation.getShippingCharges() == null) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingCharges");
        }
        if (!areShippingChargesValid(shippingInformation.getShippingCharges())) {
            throw new ValidationException(NULL_OR_EMPTY_FIELD_TEMPLATE,
                "OrderPlaced.shippingInformation.shippingCharges.shippingName");
        }
    }

    private boolean areShippingChargesValid(List<ShippingCharge> shippingCharges) {
        return shippingCharges.stream().allMatch(shippingCharge -> shippingCharge.getShippingName() != null);
    }

    public boolean isInvalidNLPostCode(String postCode) {
        return !postCode.matches(OrderPlacedConfig.NL_POST_CODE_REGEX);
    }

    private boolean isParcelShop(String carrierVariant) {
        return StringUtils.isNotEmpty(carrierVariant) && CarrierVariant.findByName(carrierVariant)
            .map(CarrierVariant::isParcelShop)
            .orElse(false);
    }

    private void validatePickupPoint(OrderPlaced message) {
        Address shippingAddress = message.getShippingInformation().getShippingAddress();
        if (pickupPointValidator.isPickupPointIdInvalid(shippingAddress.getAddressLine3(), shippingAddress.getCountry(),
            message.getOrderDetails().getCarrier())) {
            throw new ValidationException(
                "Pickup point id must not contain letters, only digits: " + INVALID_FIELD_TEMPLATE,
                ORDER_PLACED_SHIPPING_INFORMATION_SHIPPING_ADDRESS_LINE_3);
        }
    }

    private boolean containsVirtualProducts(OrderPlaced message) {
        return isVirtualShippingMethod(message.getOrderDetails().getShippingMethod())
            || isAnyProductsVirtual(message.getOrderLines());
    }

    private boolean isAnyProductsVirtual(List<OrderLine> orderLines) {
        return orderLines.stream()
            .anyMatch(orderLine -> Boolean.TRUE.equals(orderLine.getVirtualProduct()));
    }

    private boolean isAllProductsVirtual(List<OrderLine> orderLines) {
        return !orderLines.stream()
            .allMatch(orderLine -> Boolean.TRUE.equals(orderLine.getVirtualProduct()));
    }

    private boolean isVirtualShippingMethod(String shippingMethod) {
        return shippingMethod != null && shippingMethod.contains(ShippingMethod.VIRTUAL.name());
    }
}
