package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFilter;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFilterRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OrderFilterServiceImpl implements OrderFilterService {

    private final OrderFilterRepository orderFilterRepository;

    @Override
    public void createFilter(String orderId) {
        orderFilterRepository
            .findByOrderId(orderId)
            .orElseGet(() -> orderFilterRepository.save(
                    OrderFilter.builder()
                        .orderId(orderId)
                        .build()
                )
            );
    }

    @Override
    public void removeFilter(String orderId) {
        orderFilterRepository
            .findByOrderId(orderId)
            .ifPresent(orderFilterRepository::delete);
    }

    @Override
    public boolean isFilterExist(String orderId) {
        return orderFilterRepository.existsByOrderId(orderId);
    }
}
