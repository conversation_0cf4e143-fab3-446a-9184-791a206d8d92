package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class OrderLineExportedStrategy implements StateChangeStrategy<OrderLineExported> {

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderLineExported orderLineExported, Order order) {
        return Collections.nCopies(
            orderLineExported.getQuantity(),
            OrderStatusUpdateStateChange.builder()
                .ean(findEan(orderLineExported, order))
                .orderState(OrderState.EXPORTED)
                .build());
    }

    private String findEan(OrderLineExported orderLineExported, Order order) {
        return order.getOrderLines()
            .stream()
            .filter(orderLine -> Objects.equals(orderLine.getEan(), orderLineExported.getEan()))
            .findFirst()
            .map(OrderLine::getEan)
            .orElseThrow(() -> new IllegalArgumentException(
                "EAN %s not found in order lines for orderId %s".formatted(
                    orderLineExported.getEan(),
                    orderLineExported.getOrderId())));
    }

    @Override
    public Class<OrderLineExported> getMessageType() {
        return OrderLineExported.class;
    }
}
