package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.ReturnFeeDecisionId;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.EcomCountry;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.repository.ReturnFeeDecisionRepository;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChargeReturnFeeServiceImpl implements ChargeReturnFeeService {

    public static final String REFUND_DECISION_NOT_FOUND = "Refund decision not found for ecomCountry: %s "
        + "and customerReturnReason: %s";

    private final ReturnFeeDecisionRepository returnFeeDecisionRepository;

    @Override
    @Transactional
    public boolean shouldChargeReturnFee(Order order) {

        // Not going to charge employees
        if (Optional.ofNullable(order.getCustomer())
            .map(Customer::getType)
            .map(CustomerType.EMPLOYEE::equals)
            .orElse(false)) {
            return false;
        }

        // Not going to charge a return fee if it is already charged
        if (order.isReturnFeeCharged()) {
            return false;
        }


        EcomCountry ecomCountry = determineCountry(order.getShippingAddress(), order.getMarket());
        for (OrderLine orderLine : order.getOrderLines()) {
            for (OrderLineQtyStatus qtyStatus : orderLine.getOrderLineQtyStatus()) {
                // do not consider non-returned items
                // assuming that there surely are returned items if this trigger has been called
                // all quantities should have a proposition code with "charge a return fee" flag set to true

                if (qtyStatus.getOrderState() == OrderState.RETURNED
                    && (qtyStatus.getOrderStatusUpdateInfo() == null
                    || !shouldChargeReturnFee(
                    ecomCountry,
                    qtyStatus.getOrderStatusUpdateInfo().getCustomerReturnReason()))) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean shouldChargeReturnFee(EcomCountry ecomCountry, CustomerReturnReason customerReturnReason) {
        return returnFeeDecisionRepository.findById(new ReturnFeeDecisionId(customerReturnReason, ecomCountry))
            .orElseThrow(() -> new IllegalStateException(REFUND_DECISION_NOT_FOUND
                .formatted(ecomCountry, customerReturnReason)))
            .isChargeReturnFee();
    }

    private EcomCountry determineCountry(Address address, Market market) {
        EcomCountry countryCode = EcomCountry.findByCountryCode(address.getCountryCode());
        if (Market.BSE_WORLD.getMarketCode().equalsIgnoreCase(market.getMarketCode())) {
            countryCode = EcomCountry.ROTW;
        }
        return countryCode;
    }

}
