package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPartsCancelledServiceImpl implements OrderPartsCancelledService {

    private final OrderService orderService;

    private final StateChangeService stateChangeService;

    private final OrderStateService orderStateService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    @Override
    @Transactional
    public void process(OrderPartsCancelled orderPartsCancelled) {

        var order = orderService.findById(orderPartsCancelled.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderPartsCancelled, order);

        orderStateService.apply(order, orderStateChange);
        log.debug("Process orderPartsCancelled has updated order status to CANCELLED "
                + "and saved into db for orderId {}",
            order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(
                order, payloadConverter.convert(order), OrderStatusUpdated.Type.CANCELLED))
        );
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(
            tradeByteOrderStatusBaseConverter.toCancelled(orderPartsCancelled), order);
    }
}
