package com.bestseller.fulfilmentcoreservice.core.service;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.Set;

public interface ValidationService {
    void validate(@Valid @NotNull Object object) throws ConstraintViolationException;

    <T> Set<ConstraintViolation<T>> getValidationErrors(T t);
}
