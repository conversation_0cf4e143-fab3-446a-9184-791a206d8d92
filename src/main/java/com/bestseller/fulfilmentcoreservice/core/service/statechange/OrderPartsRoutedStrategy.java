package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
public class OrderPartsRoutedStrategy implements StateChangeStrategy<OrderPartsRouted> {

    private final OrderService orderService;

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderPartsRouted orderPartsRouted, Order order) {
        List<OrderStatusUpdateStateChange> stateChanges = new ArrayList<>();

        order.getOrderLines().stream()
            .flatMap(orderLine ->
                orderPartsRouted.getOrderLines().stream()
                    .filter(partLine -> partLine.getEan().equals(orderLine.getEan()))
                    .map(partLine -> new AbstractMap.SimpleEntry<>(orderLine, partLine)))
            .forEach(pair -> {
                OrderLine orderLine1 = pair.getKey();
                stateChanges.addAll(
                    Collections.nCopies(
                        orderLine1.getOriginalQty(),
                        OrderStatusUpdateStateChange.builder()
                            .ean(orderLine1.getEan())
                            .orderStatusUpdateInfo(createOrderStatusUpdateInfo(order.getOrderId()))
                            .orderState(OrderState.ROUTED)
                            .build())
                );
            });
        return stateChanges;
    }

    private OrderStatusUpdateInfo createOrderStatusUpdateInfo(
        String orderId) {
        return OrderStatusUpdateInfo.builder()
            .order(orderService.findById(orderId))
            .build();
    }

    @Override
    public Class<OrderPartsRouted> getMessageType() {
        return OrderPartsRouted.class;
    }
}
