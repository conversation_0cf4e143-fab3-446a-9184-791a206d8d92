package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdateTradeByteConverter;
import com.bestseller.fulfilmentcoreservice.core.model.TradeByteOrderStatusBaseModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class OrderStatusUpdatedTradeByteServiceImpl implements OrderStatusUpdatedTradeByteService {

    private final QueueProducer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerQueueProducer;
    private final OrderStatusUpdateTradeByteConverter orderStatusUpdateTradeByteConverter;

    @Override
    public void produceOrderStatusUpdatedTradeByte(TradeByteOrderStatusBaseModel baseModel, Order order) {
        if (!Platform.isTradebyte(order.getPlatform().name())) {
            return;
        }

        if (baseModel.getOrderState() == OrderState.CANCELLED) {
            processCancelledOrder(baseModel, order);
        } else if (baseModel.getOrderState() == OrderState.DISPATCHED
            || baseModel.getOrderState() == OrderState.RETURNED) {
            processDispatchedOrReturnedOrder(baseModel, order);
        }
    }

    private void processCancelledOrder(TradeByteOrderStatusBaseModel baseModel, Order order) {
        if (baseModel.getOrderLines().isEmpty()) {
            return;
        }

        List<Map.Entry<OrderLine, OrderLineQtyStatus>> entries = baseModel.getOrderLines().stream()
            .flatMap(orderLine -> mapOrderLinesToEntries(orderLine, order, baseModel.getOrderState()))
            .toList();

        entries.forEach(entry -> processCancelledOrderLineQtyStatus(entry, order));
    }

    private void processDispatchedOrReturnedOrder(TradeByteOrderStatusBaseModel baseModel, Order order) {
        List<Map.Entry<OrderLine, OrderLineQtyStatus>> entries = order.getOrderLines().stream()
            .filter(orderLine -> orderLineMatchesBaseModel(baseModel, orderLine))
            .flatMap(orderLineDB -> orderLineDB.getOrderLineQtyStatus().stream()
                .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderState().equals(baseModel.getOrderState()))
                .filter(OrderLineQtyStatus::isChanged)
                .map(orderLineQtyStatus -> new AbstractMap.SimpleEntry<>(orderLineDB, orderLineQtyStatus)))
            .collect(Collectors.toList());

        entries.forEach(entry -> {
            if (baseModel.getOrderState() == OrderState.DISPATCHED) {
                processDispatchedOrderLineQtyStatus(entry, baseModel, order);
            }
            if (baseModel.getOrderState() == OrderState.RETURNED) {
                processReturnedOrderLineQtyStatus(entry, order);
            }
        });
    }

    private Stream<Map.Entry<OrderLine, OrderLineQtyStatus>> mapOrderLinesToEntries(
        OrderLine baseOrderLine, Order order, OrderState orderState) {
        String ean = baseOrderLine.getEan();
        return order.getOrderLines().stream()
            .filter(orderLineDB -> orderLineDB.getEan().equalsIgnoreCase(ean)
                && orderLineDB.getLineNumber() == baseOrderLine.getLineNumber())
            .flatMap(orderLineDB -> orderLineDB.getOrderLineQtyStatus().stream()
                .filter(orderLineQtyStatus -> orderLineQtyStatus.getOrderState().equals(orderState))
                .limit(baseOrderLine.getOriginalQty())
                .map(orderLineQtyStatus -> new AbstractMap.SimpleEntry<>(orderLineDB, orderLineQtyStatus)));
    }

    private void processCancelledOrderLineQtyStatus(Map.Entry<OrderLine, OrderLineQtyStatus> entry, Order order) {
        OrderLine orderLine = entry.getKey();
        OrderLineQtyStatus orderLineQtyStatus = entry.getValue();
        orderStatusUpdateTradebyteProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdateTradeByteConverter.toCancelled(order, orderLine, orderLineQtyStatus)));
    }

    private void processDispatchedOrderLineQtyStatus(Map.Entry<OrderLine, OrderLineQtyStatus> entry,
                                                     TradeByteOrderStatusBaseModel baseModel, Order order) {
        OrderLine orderLine = entry.getKey();
        OrderLineQtyStatus orderLineQtyStatus = entry.getValue();
        orderStatusUpdateTradebyteProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdateTradeByteConverter.toDispatched(order,
                orderLine, orderLineQtyStatus, baseModel.getTrackingNumber())));
    }

    private void processReturnedOrderLineQtyStatus(Map.Entry<OrderLine, OrderLineQtyStatus> entry, Order order) {
        OrderLine orderLine = entry.getKey();
        OrderLineQtyStatus orderLineQtyStatus = entry.getValue();
        orderStatusUpdateTradebyteProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdateTradeByteConverter.toReturned(order, orderLine, orderLineQtyStatus)));
    }

    private boolean orderLineMatchesBaseModel(TradeByteOrderStatusBaseModel baseModel, OrderLine orderLine) {
        return doOrderLineAndEanExistAndEqual(baseModel, orderLine) || doesEanOnlyExistAndEqual(baseModel, orderLine);
    }

    private boolean doOrderLineAndEanExistAndEqual(TradeByteOrderStatusBaseModel baseModel, OrderLine orderLine) {
        return baseModel.getLineNumber() != null
            && baseModel.getLineNumber() > 0
            && orderLine.getLineNumber() == baseModel.getLineNumber()
            && orderLine.getEan().equalsIgnoreCase(baseModel.getEan());
    }

    private boolean doesEanOnlyExistAndEqual(TradeByteOrderStatusBaseModel baseModel, OrderLine orderLine) {
        return (baseModel.getLineNumber() == null || baseModel.getLineNumber() <= 0)
            && orderLine.getEan().equalsIgnoreCase(baseModel.getEan());
    }
}

