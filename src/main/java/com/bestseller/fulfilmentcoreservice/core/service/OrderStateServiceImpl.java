package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.monitoring.OrderStatusChangingMonitor;
import com.bestseller.fulfilmentcoreservice.core.service.helper.OrderStateHelper;
import com.bestseller.fulfilmentcoreservice.core.service.validator.OrderStateValidationService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.OrderStateTransition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.IntStream;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.VIRTUAL;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStateServiceImpl implements OrderStateService {

    private static final String ALL_ORDERLINES_MUST_BE_INITIATED_MESSAGE =
        "All order lines must be initiated with the same state.";
    private static final String UNACCEPTABLE_STATE_MESSAGE =
        "Order line: %s - unacceptable state transition: the count of the change states is different from the count "
            + "of the order line states.";

    private static final String APPLY_STATE_MESSAGE = "Start applying for the order: %s";

    private final OrderStateHelper orderStateHelper;

    private final OrderStateValidationService orderStateValidationService;

    private final OrderRepository orderRepository;

    private final OrderStatusChangingMonitor orderStatusChangingMonitor;

    /**
     * Apply order state to an order preparing a list of states for each orderline accordingly to the
     * OrderLineQuantityStatuses or original quantity if OrderLineQuantityStatuses is empty.
     *
     * @param order        the order
     * @param stateChanges the state changes
     */
    @Override
    @Transactional
    public void applyOrderState(Order order, OrderState stateChanges)
        throws StateTransitionException {
        List<StateChange> eanPlacedStateChanges = new ArrayList<>();
        for (OrderLine orderLine : order.getOrderLines()) {
            int statusQuantity = orderLine.getOrderLineQtyStatus().isEmpty()
                ? orderLine.getOriginalQty()
                : orderLine.getOrderLineQtyStatus().size();

            IntStream.range(0, statusQuantity)
                .boxed()
                .forEach(counter -> eanPlacedStateChanges.add(StateChange.builder()
                    .ean(orderLine.getEan())
                    .orderState(stateChanges)
                    .build()));
        }
        apply(order, eanPlacedStateChanges);
    }

    /**
     * Apply order state to an order line.
     *
     * @param order              the order
     * @param orderLine          the order line
     * @param orderLineQtyStatus the order line quantity status
     * @param toState            the to state
     */
    public void applyOrderState(Order order,
                                OrderLine orderLine,
                                OrderLineQtyStatus orderLineQtyStatus,
                                OrderState toState) {
        OrderState oldOrderState = orderLineQtyStatus.getOrderState();
        OrderState newOrderState = getOrderState(order, orderLine, orderLineQtyStatus, toState);
        boolean isTransitionAllowed = OrderStateTransition.isTransitionValid(
            order.getPlatform(),
            oldOrderState,
            newOrderState, orderLine.isVirtualProduct());

        if (!isTransitionAllowed) {
            throw new InvalidStateTransitionException(order.getOrderId(),
                orderLine.getEan(),
                oldOrderState,
                newOrderState);
        }
        orderLineQtyStatus.setPrevStatus(oldOrderState);
        orderLineQtyStatus.setOrderState(newOrderState);
        orderLineQtyStatus.setChanged(true);
        orderLine.setChanged(true);
        orderStateHelper.updateMinMax(order);
        orderStatusChangingMonitor.increment(newOrderState);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public <T extends StateChange> void apply(Order order, List<T> stateChanges) throws StateTransitionException {

        var nonVirtualOrderLines = order.getOrderLines().stream()
            .filter(orderLine -> !orderLine.isVirtualProduct())
            .toList();

        log.debug(APPLY_STATE_MESSAGE.formatted(order.getOrderId()));

        Map<String, List<StateChange>> changes = orderStateHelper.getMapEanToStateChange(
            stateChanges);

        applyStateTransition(order, nonVirtualOrderLines, changes);

    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public <T extends StateChange> void applyOnAllOrderLines(Order order, OrderState stateChange)
        throws StateTransitionException {

        var orderLines = order.getOrderLines().stream()
            .toList();

        log.debug(APPLY_STATE_MESSAGE.formatted(order.getOrderId()));

        Map<String, List<StateChange>> changes = orderStateHelper.getMapEanToStateChangeFromOrderLine(orderLines,
            stateChange);

        applyStateTransition(order, orderLines, changes);
    }

    private <T extends StateChange> void applyStateTransition(Order order,
                                                              List<OrderLine> orderLines,
                                                              Map<String, List<StateChange>> changes) {

        log.debug("Validating for the order {}, orderLines {}, changes {}",
            order.getOrderId(), orderLines.size(), changes.size()
        );

        orderStateValidationService.validate(order, changes);

        for (OrderLine orderLine : orderLines) {

            List<OrderLineQtyStatus> orderLineQtyStatusList = orderLine.getOrderLineQtyStatus();
            List<StateChange> changeList = changes.getOrDefault(orderLine.getEan(), new ArrayList<>());

            if (orderLineQtyStatusList.isEmpty()) {
                initializeOrderLineQuantityStatus(order, orderLine, changeList);
            } else {
                log.debug("ChangeList size: {}", changeList.size());

                for (StateChange change : changeList) {
                    OrderState toState = change.getOrderState();

                    OrderLineQtyStatus orderLineQtyStatus = null;
                    log.debug("Trying to find a valid orderLineQtyStatus for the StateChange");
                    orderLineQtyStatus = findValidOrderLineQtyStatusForStateChange(
                        order,
                        orderLine,
                        orderLineQtyStatusList,
                        toState);

                    log.debug("Found orderLineQtyStatus: {}", orderLineQtyStatus);
                    applyOrderState(order, orderLine, orderLineQtyStatus, toState);
                    if (!orderLine.isVirtualProduct()) {
                        updateOrderStatusUpdateInfoIfNecessary(change, orderLineQtyStatus);
                    }
                }
            }
        }
        orderStateHelper.updateMinMax(order);
        orderRepository.save(order);
    }

    @Override
    public OrderState getOrderState(Order order, OrderLineQtyStatus orderLineQtyStatus, OrderState desiredState) {
        if (order.getOrderBlock() != null) {
            var orderLine = orderLineQtyStatus.getOrderLine();
            boolean isTransitionValid = OrderStateTransition
                .isTransitionValid(
                    order.getPlatform(),
                    OrderState.BLOCKED,
                    orderLineQtyStatus.getOrderState(),
                    orderLine.isVirtualProduct()
                );
            if (isTransitionValid) {
                return OrderState.BLOCKED;
            }
        }

        if (isTheOrderLineGiftCardAndDispatchFromNorway(
            order, orderLineQtyStatus.getOrderLine(), orderLineQtyStatus, desiredState)
            || isTheOrderVirtualAndExported(order, orderLineQtyStatus, desiredState)) {
            return OrderState.DISPATCHED;
        }

        return desiredState;
    }

    /**
     * Returns a valid order state given OrderLineQuantity state and a desired state.
     *
     * @param order
     * @param orderLineQtyStatus
     * @param desiredState
     * @return
     */
    protected OrderState getOrderState(
        Order order,
        OrderLine orderLine,
        OrderLineQtyStatus orderLineQtyStatus,
        OrderState desiredState) {
        if (isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine, orderLineQtyStatus, desiredState)
            || isTheOrderVirtualAndExported(order, orderLineQtyStatus, desiredState)) {
            return OrderState.DISPATCHED;
        }
        return desiredState;
    }

    private void updateOrderStatusUpdateInfoIfNecessary(
        StateChange change,
        OrderLineQtyStatus orderLineQtyStatus) {
        if (change instanceof OrderStatusUpdateStateChange orderStatusUpdateInfoStateChange) {
            if (change.getOrderState().equals(OrderState.EXPORTED)
                || change.getOrderState().equals(OrderState.PROCESSING)) {
                return;
            }

            if (orderLineQtyStatus.getOrderStatusUpdateInfo() == null) {
                orderLineQtyStatus.setOrderStatusUpdateInfo(new OrderStatusUpdateInfo());
            }

            orderLineQtyStatus.getOrderStatusUpdateInfo()
                .merge(orderStatusUpdateInfoStateChange.getOrderStatusUpdateInfo());
        }
    }

    private void initializeOrderLineQuantityStatus(Order order, OrderLine orderLine, List<StateChange> changeList) {
        if (!changeList.isEmpty() && changeList.size() == orderLine.getOriginalQty()) {
            OrderState toState = null;
            for (StateChange change : changeList) {
                if (toState == null) {
                    toState = change.getOrderState();
                }
                if (toState != change.getOrderState()) {
                    throw new StateTransitionException(ALL_ORDERLINES_MUST_BE_INITIATED_MESSAGE, order.getOrderId());
                }
            }

            int quantity = orderLine.getOriginalQty();
            log.debug("initOrderLineState for the order {}, Orderline {}, OrderState {}, OriginalQty {}",
                order.getOrderId(), orderLine, toState.toString(), quantity);
            List<OrderLineQtyStatus> orderLineQtyStatusList = new ArrayList<>(quantity);
            for (int i = 0; i < quantity; i++) {
                OrderLineQtyStatus orderLineQtyStatus = new OrderLineQtyStatus();
                orderLineQtyStatus.setOrderLine(orderLine);
                OrderState newState = getOrderState(order, orderLine, orderLineQtyStatus, toState);
                log.debug("A new state for the order {} is {}", order.getOrderId(), newState);
                orderLineQtyStatus.setOrderState(newState);
                orderLineQtyStatusList.add(orderLineQtyStatus);
                orderStatusChangingMonitor.increment(newState);
            }
            orderLine.setOrderLineQtyStatus(orderLineQtyStatusList);
            orderLine.setChanged(true);
        } else {
            throw new StateTransitionException(UNACCEPTABLE_STATE_MESSAGE.formatted(orderLine.getLineNumber()),
                order.getOrderId());
        }
    }

    /**
     * Find the first line qty states which allow transition from it to the new state take one of them.
     *
     * @param order
     * @param orderLine
     * @param orderLineQtyStatusList
     * @param targetNewState
     * @return OrderLineQtyStatus
     * @throws StateTransitionException
     */
    protected OrderLineQtyStatus findValidOrderLineQtyStatusForStateChange(
        Order order, OrderLine orderLine,
        List<OrderLineQtyStatus> orderLineQtyStatusList,
        OrderState targetNewState)
        throws StateTransitionException {

        var allowedStateTransitionStates = OrderStateTransition
            .getAllowedStatesToTargetState(order.getPlatform(),
                targetNewState,
                orderLine.isVirtualProduct());

        /*  Sort the allowed states ascending.
         *  It will help to find the correct quantity line when the quantity is more than 1.
         */
        Set<OrderState> orderStateTreeSet = new TreeSet<>(
            Comparator.comparingInt(OrderState::getIdentifier));
        orderStateTreeSet.addAll(List.of(OrderState.values()));

        var sortedAllowedStateTransitionStates = List.copyOf(orderStateTreeSet)
            .stream()
            .filter(orderState -> allowedStateTransitionStates
                .stream()
                .anyMatch(allowedState -> allowedState.getIdentifier() == orderState.getIdentifier()))
            .toList();

        for (OrderState allowedState : sortedAllowedStateTransitionStates) {
            for (OrderLineQtyStatus orderLineQtyStatus : orderLineQtyStatusList) {
                if (orderLineQtyStatus.getOrderState().equals(allowedState)) {
                    return orderLineQtyStatus;
                }
            }
        }

        throw new InvalidStateTransitionException(order.getOrderId(), orderLine.getEan(), orderLineQtyStatusList
            .stream()
            .map(OrderLineQtyStatus::getOrderState)
            .toList(),
            targetNewState);
    }

    /**
     * Check if the order is virtual and its status exported.
     *
     * @param order
     * @param status
     * @param state
     * @return
     */
    protected boolean isTheOrderVirtualAndExported(Order order, OrderLineQtyStatus status, OrderState state) {
        return isExported(status, state)
            && order.getShippingMethod() != null
            && order.getShippingMethod() == VIRTUAL;
    }

    /**
     * Check if the order is Norwegian and the order status is exported.
     *
     * @param order
     * @param status
     * @param state
     * @return
     */
    protected boolean isTheOrderLineGiftCardAndDispatchFromNorway(Order order,
                                                                  OrderLine orderLine,
                                                                  OrderLineQtyStatus status,
                                                                  OrderState state) {
        return isExported(status, state) && isANorwegianOrder(order) && orderLine.isVirtualProduct();
    }

    private boolean isExported(OrderLineQtyStatus status, OrderState state) {
        return status != null && state == OrderState.EXPORTED;
    }

    private boolean isANorwegianOrder(Order order) {
        return order.getShippingAddress() != null
            && order.getShippingAddress().getCountryCode() != null
            && order.getShippingAddress().getCountryCode().equalsIgnoreCase("NO");
    }
}
