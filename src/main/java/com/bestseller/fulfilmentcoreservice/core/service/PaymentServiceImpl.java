package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.api.dto.outbound.PaymentCancellationValidation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.util.retry.Retry;

import java.time.Duration;

@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    private static final String API_VERSION = "/api/v1";
    private static final int MAX_ATTEMPTS = 3;

    private final WebClient paymentServiceClient;
    private final Duration minBackoff;

    public PaymentServiceImpl(
        WebClient paymentServiceClient,
        @Value("${pms.webclient.backoff.delay}") Duration minBackoff) {
        this.paymentServiceClient = paymentServiceClient;
        this.minBackoff = minBackoff;
    }

    @Override
    public PaymentCancellationValidation validateIfPaymentCanBeCancelled(String orderId) {
        log.info("Checking if order {} can be cancelled", orderId);
        return paymentServiceClient.get()
            .uri(uriBuilder -> uriBuilder.path("%s/payments/{orderId}/validate-cancellation".formatted(API_VERSION))
                .build(orderId))
            .retrieve()
            .bodyToMono(PaymentCancellationValidation.class)
            .retryWhen(Retry.backoff(MAX_ATTEMPTS, minBackoff).filter(this::isRetryableException))
            .doOnSuccess(canBeCancelled -> log.info("Order {} can be cancelled: {}", orderId, canBeCancelled))
            .block();
    }

    @Override
    public PaymentStatusUpdated getPaymentStatusUpdatedMessage(String orderId) {
        log.info("Receiving paymentStatusUpdated message for order {}", orderId);
        return paymentServiceClient.get()
            .uri(uriBuilder -> uriBuilder.path("%s/orders/{orderId}/payment-status-updated-message"
                    .formatted(API_VERSION))
                .build(orderId))
            .retrieve()
            .bodyToMono(PaymentStatusUpdated.class)
            .retryWhen(Retry.backoff(MAX_ATTEMPTS, minBackoff).filter(this::isRetryableException))
            .doOnSuccess(paymentStatusUpdated -> log.info("paymentStatusUpdated message received for order {}",
                orderId))
            .block();
    }

    // All exceptions excluding 4xx, but not Request Timeout, errors are retryable.
    private boolean isRetryableException(Throwable throwable) {
        return !(throwable instanceof WebClientResponseException exception
            && exception.getStatusCode().is4xxClientError()
            && exception.getStatusCode() != HttpStatus.REQUEST_TIMEOUT);
    }
}
