package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderForFulfilmentConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderForFulfilmentModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderForFulfilmentServiceImpl implements OrderForFulfilmentService {

    private final OrderRepository orderRepository;
    private final OrderStateService orderStateService;
    private final QueueProducer<OrderForFulfillment> orderForFulfilmentProducerQueueProducer;
    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;
    private final OrderForFulfilmentConverter orderForFulfilmentConverter;
    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;
    private final OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Override
    public void process(OrderForFulfilmentModel orderForFulfilmentModel) {
        Order order = orderRepository.findById(orderForFulfilmentModel.getOrderId())
            .orElseThrow(() -> new OrderNotFoundException(orderForFulfilmentModel.getOrderId()));
        log.info("Incoming update order status by orderId: {}", order.getOrderId());
        updateOrderStatus(order);
        orderForFulfilmentProducerQueueProducer.enqueue(EnqueueParams.create(
            orderForFulfilmentConverter.toOrderForFulfilment(orderForFulfilmentModel)));
        log.info("OrderForFulfilment message produced successfully for orderId: {}",
            order.getOrderId());
    }

    private void updateOrderStatus(Order order) {
        orderStateService.applyOrderState(order, OrderState.ROUTING);
        log.debug("Process orderForFulfilment has updated order status to ROUTING and saved into db for orderId {}",
            order.getOrderId());
        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(
                order, orderToOrderStatusUpdatedPayloadConverter.convert(order), OrderStatusUpdated.Type.ROUTING))
        );
    }
}
