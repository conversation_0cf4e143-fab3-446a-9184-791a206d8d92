package com.bestseller.fulfilmentcoreservice.core.service.validator;

import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

public interface OrderStateValidationService {

    void validate(@NotNull(message = "Order must not be null") Order order,
                  @NotNull(message = "Order Changes must not be null")
                  @NotEmpty(message = "Order Changes must not be empty") Map<String, List<StateChange>> changes);
}
