package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderBlock;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.VIRTUAL;
import static com.logistics.statetransition.OrderState.EXPORTED;
import static com.logistics.statetransition.OrderState.RESUBMIT;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderBlockServiceImpl implements OrderBlockService {

    private static final Set<OrderState> RESUBMIT_STATES = Set.of(RESUBMIT, EXPORTED);

    private final OrderService orderService;

    private final OrderStateService orderStateService;

    private final OrderFilterService orderFilterService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    private final ManualOrderForFulfillmentService manualOrderForFulfillmentService;

    @Override
    @Transactional
    public void block(String orderId) {
        log.info("Starting block to order {}", orderId);
        orderService.findOrderById(orderId)
            .ifPresentOrElse(order -> {
                    addOrderBlock(order);
                    var orderStatusUpdatedPayload = orderToOrderStatusUpdatedPayloadConverter.convert(order);
                    var orderBlocked = orderStatusUpdatedConverter.convertTo(
                        order, orderStatusUpdatedPayload, OrderStatusUpdated.Type.BLOCKED);
                    orderService.save(order);
                    orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderBlocked));
                    log.info("Block for order successfully completed {}", orderId);
                },
                () -> {
                    log.info("Creating black list to order {}", orderId);
                    orderFilterService.createFilter(orderId);
                    log.info("Black list to order {} successfully created", orderId);
                }
            );
    }

    @Override
    @Transactional
    public void unblock(String orderId) {
        log.info("Starting unblock to order {}", orderId);
        orderService.findOrderById(orderId).ifPresent(order -> {
            var orderBlock = order.getOrderBlock();
            if (orderBlock != null) {
                order.setOrderBlock(null);
                applyOrderState(order, orderBlock);
                orderService.save(order);

                // If the payment has been authorized while blocking the order,
                // we need to publish orderForFulfillment message manually.
                manualOrderForFulfillmentService.fulfillOrder(order);
            }
            log.info("Unblock for order successfully completed {}", orderId);
        });
        log.info("Removing possible filter to order {}", orderId);
        orderFilterService.removeFilter(orderId);
    }

    @Override
    @Transactional
    public void applyOrderBlock(Order order) {
        if (orderFilterService.isFilterExist(order.getOrderId()) && !isTheOrderVirtual(order)) {
            addOrderBlock(order);
        }
    }

    private void applyOrderState(Order order, OrderBlock orderBlock) {
        if (order.getMinStatus() == OrderState.BLOCKED) {
            if (orderBlock.getResumeState() == RESUBMIT) {
                orderStateService.applyOrderState(order, RESUBMIT);
            } else {
                orderStateService.applyOrderState(order, OrderState.PLACED);
            }
        }
    }

    private void addOrderBlock(Order order) {
        var orderBlock = new OrderBlock();
        if (RESUBMIT_STATES.contains(order.getMinStatus())) {
            orderBlock.setResumeState(RESUBMIT);
        } else {
            orderBlock.setResumeState(OrderState.PLACED);
        }
        order.setOrderBlock(orderBlock);
        orderStateService.applyOrderState(order, OrderState.BLOCKED);
    }

    private boolean isTheOrderVirtual(Order order) {
        return VIRTUAL == order.getShippingMethod();
    }

}
