package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.ReturnItem;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class OrderReturnedInStoreStrategy implements StateChangeStrategy<OrderReturnedInStore> {

    private final OrderService orderService;

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderReturnedInStore orderReturnedInStore, Order order) {
        List<OrderStatusUpdateStateChange> stateChanges = new ArrayList<>();
        order.getOrderLines().stream()
            .flatMap(orderLine ->
                orderReturnedInStore.getReturnItems().stream()
                    .filter(returnItem -> returnItem.getOrderLine().getEan().equals(orderLine.getEan()))
                    .map(returnItem -> new AbstractMap.SimpleEntry<>(orderLine, returnItem))
            )
            .forEach(pair -> {
                var returnItem = pair.getValue();

                stateChanges.addAll(
                    Collections.nCopies(
                        returnItem.getOrderLine().getQuantity(),
                        OrderStatusUpdateStateChange.builder()
                            .ean(returnItem.getOrderLine().getEan())
                            .orderStatusUpdateInfo(createOrderStatusUpdateInfo(order.getOrderId()))
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()
                    )
                );
            });

        orderReturnedInStore.getReturnItems().stream()
            .map(ReturnItem::getOrderLine)
            .filter(returnItem -> order.getOrderLines().stream()
                .noneMatch(orderLine -> Objects.equals(orderLine.getEan(), returnItem.getEan())))
            .findFirst()
            .ifPresent(returnItem -> {
                throw new IllegalArgumentException("Order line not found for ean: %s".formatted(returnItem.getEan()));
            });
        return stateChanges;
    }

    private OrderStatusUpdateInfo createOrderStatusUpdateInfo(String orderId) {
        return OrderStatusUpdateInfo.builder()
            .order(orderService.findById(orderId))
            .customerReturnReason(CustomerReturnReason.RETURNED_IN_STORE_35)
            .returnType(ReturnType.CUSTOMER_RETURN)
            .returnDate(LocalDate.now())
            .build();
    }

    @Override
    public Class<OrderReturnedInStore> getMessageType() {
        return OrderReturnedInStore.class;
    }
}
