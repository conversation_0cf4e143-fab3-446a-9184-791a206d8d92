package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.logistics.statetransition.OrderState;

import java.util.Optional;

public interface OrderService {

    Order findById(String orderId) throws OrderNotFoundException;

    Order findByIdWithOrderLines(String orderId) throws OrderNotFoundException;

    Optional<Order> findOrderById(String orderId);

    Optional<OrderView> findOrder(String orderId);

    boolean exists(String orderId);

    Order save(Order order);

    void authorizePayment(Order order);

    int finalizeOrders();

    boolean isPaymentAuthorised(String orderId);

    boolean isDuplicateRequest(String orderId, String ean, OrderState targetState);

    boolean isDuplicateRequest(String orderId, OrderState targetState);

    boolean isDuplicateRequest(OrderPartsRouted orderPartsRouted);

    void updateEmailAddress(String orderId, String emailAddress);

    boolean validateIfOrderCanBeCancelled(String orderId);
}
