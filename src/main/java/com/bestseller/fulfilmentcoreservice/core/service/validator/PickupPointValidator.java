package com.bestseller.fulfilmentcoreservice.core.service.validator;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiFunction;

@Component
@RequiredArgsConstructor
public class PickupPointValidator {

    private static final String NUMBER_REGEX = "[0-9]+";
    private static final String DHL = "DHL";
    private static final String CORREOS = "CORREOS";
    private static final String INPOST = "INPOST";
    private static final String MONDIAL_RELAY = "MONDIALRELAY";
    private static final String DHL_CONNECT = "DHLCON";
    private static final String ES = "ES";
    private static final String PL = "PL";
    private static final String NL = "NL";
    private static final String DE = "DE";
    private static final String FR = "FR";
    private static final String EE = "EE";
    private static final String LT = "LT";
    private static final String HU = "HU";
    private static final String CZ = "CZ";
    private static final Set<String> DHL_CONNECT_COUNTRIES = Set.of(ES, EE, LT, HU, CZ);
    private static final List<BiFunction<String, String, Boolean>> EXCEPTION_RULES = List.of(
        (countryCode, carrier) -> Objects.equals(countryCode, NL),
        (countryCode, carrier) -> Objects.equals(countryCode, DE) && Objects.equals(carrier, DHL),
        (countryCode, carrier) -> Objects.equals(countryCode, ES) && Objects.equals(carrier, CORREOS),
        (countryCode, carrier) -> Objects.equals(countryCode, PL) && Objects.equals(carrier, INPOST),
        (countryCode, carrier) -> Objects.equals(countryCode, FR) && Objects.equals(carrier, MONDIAL_RELAY),
        (countryCode, carrier) -> Objects.equals(carrier, DHL_CONNECT) && DHL_CONNECT_COUNTRIES.contains(countryCode)
    );

    public boolean isPickupPointIdInvalid(String addressLine3, String countryCode, String carrier) {
        if (StringUtils.isNotEmpty(addressLine3)) {
            boolean isAnyExceptionRuleApplied = EXCEPTION_RULES.stream()
                .anyMatch(validation -> validation.apply(countryCode, carrier));
            return !isAnyExceptionRuleApplied && !containsOnlyNumbers(addressLine3);
        }
        return false;
    }

    private static boolean containsOnlyNumbers(String value) {
        return value.matches(NUMBER_REGEX);
    }
}
