package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderReturnInfo;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderReturnInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderReturnInfoServiceImpl implements OrderReturnInfoService {

    private final OrderReturnInfoRepository orderReturnInfoRepository;

    @Override
    public void updateLatestReturnDate(String orderId, LocalDate returnDate) {
        orderReturnInfoRepository.upsertLatestReturnDate(orderId, returnDate);
    }

    @Override
    public Optional<OrderReturnInfo> getOrderReturnInfo(String orderId) {
        return orderReturnInfoRepository.findById(orderId);
    }

}
