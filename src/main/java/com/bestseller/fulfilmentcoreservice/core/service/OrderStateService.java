package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.logistics.statetransition.OrderState;

import java.util.List;

public interface OrderStateService {
    void applyOrderState(Order order, OrderState stateChange)
        throws StateTransitionException;

    void applyOrderState(Order order,
                         OrderLine orderLine,
                         OrderLineQtyStatus orderLineQtyStatus,
                         OrderState toState);

    <T extends StateChange> void applyOnAllOrderLines(Order order, OrderState stateChange)
        throws StateTransitionException;

    <T extends StateChange> void apply(Order order, List<T> stateChanges) throws StateTransitionException;

    OrderState getOrderState(Order order, OrderLineQtyStatus status, OrderState state);
}
