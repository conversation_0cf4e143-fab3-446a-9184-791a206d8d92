package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderCancelledConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderForFulfilmentConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.PaymentStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.MessageNotProcessedException;
import com.bestseller.fulfilmentcoreservice.core.model.PaymentStatusUpdatedModel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated.PaymentState;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentStatusUpdatedServiceImpl implements PaymentStatusUpdatedService {

    private final OrderStateService orderStateService;

    private final OrderForFulfilmentService orderForFulfilmentService;

    private final OrderForFulfilmentConverter orderForFulfilmentConverter;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final QueueProducer<OrderCancelled> orderCancelledProducerQueueProducer;

    private final OrderCancelledConverter orderCancelledConverter;

    private final PaymentStatusUpdatedConverter paymentStatusUpdatedConverter;

    private final OrderService orderService;

    @Transactional
    @Override
    public void process(PaymentStatusUpdated paymentStatusUpdated) {
        Order order = orderService.findById(paymentStatusUpdated.getOrderId());

        if (order.isOrderPaymentAuthorised()
            && PaymentState.CANCELLED.equals(paymentStatusUpdated.getPaymentState())) {

            if (order.getMinStatus().isGreaterThanOrEquals(OrderState.EXPORTED)) {
                log.info("PaymentStatusUpdated with CANCELLED status is coming from the warehouse... "
                        + "No need to do anything for order: {}",
                    order.getOrderId());
                return;
            }

            throw new MessageNotProcessedException(String.format(
                "The payment status has already been updated to AUTHORISED,"
                    + " so payment cancellation is no longer possible for order: %s"
                    .formatted(order.getOrderId())));
        }

        if (PaymentState.AUTHORISED.equals(paymentStatusUpdated.getPaymentState())) {
            authoriseOrder(order, paymentStatusUpdatedConverter
                .toPaymentStatusUpdatedModel(paymentStatusUpdated.getOrderId(),
                    (AuthorizedPayload) paymentStatusUpdated.getPayload()));
        }

        if (PaymentState.CANCELLED.equals(paymentStatusUpdated.getPaymentState())) {
            cancelOrder(order);
        }
    }

    private void authoriseOrder(Order order, PaymentStatusUpdatedModel paymentStatusUpdated) {
        log.info("Incoming paymentStatusUpdated for AUTHORISED payment status by orderId: {}", order.getOrderId());
        orderService.authorizePayment(order);
        log.info("Payment status has been updated to AUTHORISED for orderId: {}", order.getOrderId());

        if (isVirtualOrder(order)) {
            log.info("Order is virtual, no need to process order for fulfilment for orderId: {}", order.getOrderId());
            return;
        }

        var orderForFulfilmentModel = orderForFulfilmentConverter.toOrderForFulfilmentModel(
            order, paymentStatusUpdated);
        orderForFulfilmentConverter.completeOrderDetails(paymentStatusUpdated, orderForFulfilmentModel);
        orderForFulfilmentConverter.completeOrderForFulfilmentModel(paymentStatusUpdated, orderForFulfilmentModel);

        log.info("OrderForFulfilment model has been created for orderId: {}", order.getOrderId());
        orderForFulfilmentService.process(orderForFulfilmentModel);
    }

    private void cancelOrder(Order order) {
        log.info("Incoming paymentStatusUpdated for Canceled payment status by orderId: {}",
            order.getOrderId());

        orderStateService.applyOnAllOrderLines(order, OrderState.CANCELLED);
        log.debug("Process orderStatusUpdated has updated order status to Canceled and saved into db for orderId {}",
            order.getOrderId());

        orderCancelledProducerQueueProducer.enqueue(EnqueueParams.create(orderCancelledConverter.toCancelled(order)));

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(
                order, payloadConverter.convert(order), OrderStatusUpdated.Type.CANCELLED))
        );
    }

    private boolean isVirtualOrder(Order order) {
        return order.getShippingMethod().equals(ShippingMethod.VIRTUAL);
    }
}
