package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.api.dto.outbound.PaymentCancellationValidation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;

public interface PaymentService {

    PaymentCancellationValidation validateIfPaymentCanBeCancelled(String orderId);

    PaymentStatusUpdated getPaymentStatusUpdatedMessage(String orderId);

}
