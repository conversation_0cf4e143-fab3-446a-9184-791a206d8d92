package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.logistics.statetransition.OrderState;

import java.util.List;

public interface StateChangeService {
    <T> List<OrderStatusUpdateStateChange> captureOrderStateChange(T message, Order order);

    List<StateChange> createCopiesOfStateChange(String ean, int quantity, OrderState orderState);
}
