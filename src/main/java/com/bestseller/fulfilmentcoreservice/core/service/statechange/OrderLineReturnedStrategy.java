package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class OrderLineReturnedStrategy implements StateChangeStrategy<OrderLineReturned> {

    private final OrderService orderService;

    @Override
    public List<OrderStatusUpdateStateChange> defineOrderStateChange(
        OrderLineReturned orderLineReturned, Order order) {
        return switch (ReturnType.valueOf(orderLineReturned.getReturnType())) {
            case REJECTION -> defineOrderStatesToCustomerRejection(orderLineReturned, order);
            case CUSTOMER_RETURN -> defineOrderStatesToCustomerReturn(orderLineReturned, order);
        };
    }

    private List<OrderStatusUpdateStateChange> defineOrderStatesToCustomerReturn(
        OrderLineReturned orderLineReturned, Order order) {
        return Collections.nCopies(
            orderLineReturned.getQuantity(),
            OrderStatusUpdateStateChange.builder()
                .ean(findEan(orderLineReturned, order))
                .orderStatusUpdateInfo(createOrderStatusUpdateInfo(orderLineReturned))
                .orderState(OrderState.RETURNED)
                .build());
    }

    /**
     * All items are returned to the shipping warehouse.
     *
     * @param orderLineReturned
     * @param order
     * @return List of WarehouseReferenceStateChange
     */
    private List<OrderStatusUpdateStateChange> defineOrderStatesToCustomerRejection(
        OrderLineReturned orderLineReturned, Order order) {
        String returnedWarehouse = orderLineReturned.getWarehouse();
        List<OrderStatusUpdateStateChange> stateChanges = new ArrayList<>();
        order.getOrderLines()
            .forEach(orderLine -> {
                if (orderLine.getOpenQty() > 0
                    && isLineDispatchedAndReturnedToSameWarehouse(orderLine, returnedWarehouse)) {
                    stateChanges.addAll(
                        Collections.nCopies(
                            orderLine.getOpenQty(),
                            OrderStatusUpdateStateChange.builder()
                                .ean(orderLine.getEan())
                                .orderState(OrderState.RETURNED)
                                .orderStatusUpdateInfo(createOrderStatusUpdateInfo(orderLineReturned))
                                .build()));
                }
            });
        return stateChanges;
    }

    private boolean isLineDispatchedAndReturnedToSameWarehouse(OrderLine orderLine, String warehouse) {
        return orderLine.getOrderLineQtyStatus().stream()
            .map(OrderLineQtyStatus::getOrderFulfillmentPart)
            .filter(Objects::nonNull)
            .anyMatch(orderFulfillmentPart -> orderFulfillmentPart.getFulfillmentNode().equalsIgnoreCase(warehouse));
    }

    private String findEan(OrderLineReturned orderLineReturned, Order order) {
        return order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getLineNumber() == orderLineReturned.getLineNumber())
            .filter(orderLine -> !orderLine.isVirtualProduct())
            .findFirst()
            .map(OrderLine::getEan)
            .orElseThrow(() -> new IllegalArgumentException(
                "EAN %s not found in order lines for line number %d".formatted(
                    orderLineReturned.getEan(),
                    orderLineReturned.getLineNumber())));
    }

    private OrderStatusUpdateInfo createOrderStatusUpdateInfo(OrderLineReturned orderLineReturned) {
        return OrderStatusUpdateInfo.builder()
            .order(orderService.findById(orderLineReturned.getOrderId()))
            .customerReturnReason(
                CustomerReturnReason.findByName(orderLineReturned.getReturnReason())
                    .orElseThrow(() -> new IllegalArgumentException("Invalid return reason %s"
                        .formatted(orderLineReturned.getReturnReason()))))
            .returnType(ReturnType.valueOf(orderLineReturned.getReturnType()))
            .returnDate(orderLineReturned.getEffectiveDate().toLocalDate())
            .build();
    }

    @Override
    public Class<OrderLineReturned> getMessageType() {
        return OrderLineReturned.class;
    }
}
