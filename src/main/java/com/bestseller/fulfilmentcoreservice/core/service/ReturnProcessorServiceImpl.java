package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ReturnProcessorServiceImpl implements ReturnProcessorService {

    private final OrderRepository orderRepository;

    private final OrderRefundService orderRefundService;

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @SuppressWarnings("IllegalCatch")
    public void processReturns() {
        try {
            List<String> orderIds = orderRepository.findAllReturnableOrderIds();
            log.info("Number of returnable orders found: {}", orderIds.size());
            orderIds.forEach(orderId -> {
                try {
                    orderRefundService.processRefund(orderId);
                } catch (Exception e) {
                    log.error("Error processing returns for order {}", orderId, e);
                }
            });
        } catch (Exception e) {
            log.error("Error retrieving the returns", e);
        }
    }

}
