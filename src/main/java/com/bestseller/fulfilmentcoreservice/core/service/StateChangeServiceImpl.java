package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@RequiredArgsConstructor
public class StateChangeServiceImpl implements StateChangeService {

    private final OrderStateChangeContext context;

    @Override
    public <T> List<OrderStatusUpdateStateChange> captureOrderStateChange(T message, Order order) {
        return context.executeStrategy(message, order);
    }

    @Override
    public List<StateChange> createCopiesOfStateChange(String ean, int quantity, OrderState orderState) {
        return Collections.nCopies(
            quantity,
            StateChange.builder()
                .orderState(orderState)
                .ean(ean)
                .build()
        );
    }
}
