package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.api.dto.RefundOptionsResponse;
import com.bestseller.fulfilmentcoreservice.api.dto.ReturnReasonCodesResponse;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundServiceImpl implements OrderRefundService {

    private final OrderRepository orderRepository;

    private final QueueProducer<RefundCreationRequested> refundCreationRequestedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final StateChangeService stateChangeService;

    private final RefundShipmentFeeService refundShipmentFeeService;

    private final ChargeReturnFeeService chargeReturnFeeService;

    private final QueueProducer<OrderReturned> orderReturnedProducerQueueProducer;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    private final OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Override
    @Transactional
    public void processRefund(String orderId) {
        var order = orderRepository.getReferenceById(orderId);

        var itemsToBeRefund = new ArrayList<ItemsToRefund>();
        var orderReturned = new OrderReturned()
            .withOrderId(orderId);

        var refundRequestId = UUID.randomUUID();
        List<StateChange> stateChanges = new ArrayList<>();
        order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.isRegularProduct())
            .forEach(orderLine -> {
                int totalReturned = 0;
                for (var orderLineQuantityStatus : orderLine.getOrderLineQtyStatus()) {
                    if (orderLineQuantityStatus.getOrderState() == OrderState.RETURNED) {
                        orderLineQuantityStatus.getOrderStatusUpdateInfo().setRefundRequestId(refundRequestId);
                        totalReturned++;
                    }
                }
                if (totalReturned > 0) {
                    itemsToBeRefund.add(new ItemsToRefund(orderLine.getEan(), totalReturned));

                    int alreadyReturnedCount = getAlreadyReturnedCount(orderLine);

                    orderReturned.getOrderLines().add(new OrderLine()
                        .withEan(orderLine.getEan())
                        .withQuantity(totalReturned + alreadyReturnedCount));
                    stateChanges.addAll(stateChangeService.createCopiesOfStateChange(
                        orderLine.getEan(),
                        totalReturned,
                        OrderState.RETURN_PROCESSED));
                }
            });

        // We must pass the original order to the service to avoid missing any business logic
        boolean refundShipmentFee = refundShipmentFeeService.shouldRefundShipmentFee(order);
        boolean chargeReturnFee = chargeReturnFeeService.shouldChargeReturnFee(order);

        orderStateService.apply(order, stateChanges);

        refundCreationRequestedProducerQueueProducer.enqueue(EnqueueParams.create(new RefundCreationRequested()
            .withOrderId(orderId)
            .withRefundCreationRequestedId(refundRequestId)
            .withItemsToRefund(itemsToBeRefund)
            .withChargeReturnFee(chargeReturnFee)
            .withRefundShippingFee(refundShipmentFee)));
        orderReturnedProducerQueueProducer.enqueue(EnqueueParams.create(orderReturned));

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
                orderStatusUpdatedConverter.convertTo(order,
                    orderToOrderStatusUpdatedPayloadConverter.convert(order), OrderStatusUpdated.Type.RETURN_PROCESSED
                )
            )
        );

        order.setReturnFeeCharged(order.isReturnFeeCharged() || chargeReturnFee);
        orderRepository.save(order);

        log.info("Refund was processed for order: {}", orderId);
    }

    private static int getAlreadyReturnedCount(
        com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine orderLine) {
        return (int) orderLine.getOrderLineQtyStatus().stream()
            .filter(orderLineQuantityStatus ->
                orderLineQuantityStatus.getOrderState() == OrderState.RETURN_PROCESSED)
            .count();
    }

    @Override
    public RefundOptionsResponse getInStoreRefundOptions(String orderId) {
        var order = orderRepository.findById(orderId).orElseThrow(() -> new OrderNotFoundException(orderId));
        return RefundOptionsResponse.builder()
            .refundShippingFee(refundShipmentFeeService.shouldRefundShipmentFee(order))
            .chargeReturnFee(false) // This is a POS return, so we don't charge a return fee
            .build();
    }

    @Override
    public ReturnReasonCodesResponse getReturnReasonCodes(String refundRequestId) {
        return ReturnReasonCodesResponse.builder()
            .returnReasonCodes(orderStatusUpdateInfoRepository
                .findAllCustomerReturnReasonCodesByRefundRequestId(UUID.fromString(refundRequestId))
                .stream()
                .map(CustomerReturnReason::getValue)
                .collect(Collectors.toList())
            ).build();
    }

}
