package com.bestseller.fulfilmentcoreservice.core.service.helper;

import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.logistics.statetransition.OrderState;

import java.util.List;
import java.util.Map;

public interface OrderStateHelper {
    <T extends StateChange> Map<String, List<StateChange>> getMapEanToStateChange(List<T> stateChanges);

    Map<String, List<StateChange>> getMapEanToStateChangeFromOrderLine(List<OrderLine> orderLines,
                                                                       OrderState stateChange);

    void updateMinMax(Order order);
}
