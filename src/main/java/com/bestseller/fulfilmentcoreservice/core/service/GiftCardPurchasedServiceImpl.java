package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RequiredArgsConstructor
@Service
public class GiftCardPurchasedServiceImpl implements GiftCardPurchasedService {
    private final OrderService orderService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStateService orderStateService;

    private final OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Transactional
    @Override
    public void process(GiftCardPurchased message) {
        var order = orderService.findById(message.getOrderId());

        var orderLineQtyStatus = order.getOrderLines()
            .stream()
            .filter(OrderLine::isVirtualProduct)
            .filter(orderLine -> orderLine.getLineNumber() == message.getOrderLineNumber())
            .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
            .filter(lineQtyStatus -> lineQtyStatus.getIndexCol() == message.getIndex())
            .findFirst()
            .orElse(null);

        if (orderLineQtyStatus == null) {
            log.warn("GiftCardPurchased message with orderId {} and orderLineNumber {} and index {}"
                    + " does not match any orderLineQtyStatus in order", message.getOrderId(),
                message.getOrderLineNumber(), message.getIndex());
            return;
        }

        orderStateService.applyOrderState(order,
            orderLineQtyStatus.getOrderLine(),
            orderLineQtyStatus,
            OrderState.DISPATCHED);
        log.debug("Processing GiftCardPurchased has updated order status to DISPATCHED"
            + " and saved into db for orderId {}", order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(order,
                orderToOrderStatusUpdatedPayloadConverter.convert(order), OrderStatusUpdated.Type.DISPATCHED))
        );
    }
}
