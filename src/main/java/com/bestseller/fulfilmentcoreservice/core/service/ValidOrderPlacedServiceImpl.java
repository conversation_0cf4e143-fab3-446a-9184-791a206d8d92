package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.converter.entity.OrderEntityConverter;
import com.bestseller.fulfilmentcoreservice.converter.entity.UserDeviceInfoEntityConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.ValidOrderPlacedConverter;
import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.fulfilmentcoreservice.core.monitoring.OrderStatusChangingMonitor;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ValidOrderPlacedServiceImpl implements ValidOrderPlacedService {

    private final ValidOrderPlacedConverter validOrderPlacedConverter;

    private final OrderEntityConverter orderEntityConverter;

    private final UserDeviceInfoEntityConverter userDeviceInfoEntityConverter;

    private final PartnerChannelService partnerChannelService;

    private final CurrencyService currencyService;

    private final OrderService orderService;

    private final ChannelTypeService channelTypeService;

    private final OrderStatusChangingMonitor orderStatusChangingMonitor;

    private final OrderBlockService orderBlockService;

    @Override
    @Transactional
    public void process(ValidOrderPlaced message) {
        var orderModel = validOrderPlacedConverter.toOrderModel(message);
        Order order = orderEntityConverter.convert(orderModel);
        addOrderInfo(order, orderModel);
        processOrderFiltering(order);
        orderService.save(order);
        // The order placement flow does not go through the apply method in the OrderStateService
        // so we have to update the order status changing monitor in this class.
        order.getOrderLines().stream()
            .flatMap(orderLine -> orderLine.getOrderLineQtyStatus().stream())
            .forEach(orderLineQtyStatus -> orderStatusChangingMonitor.increment(orderLineQtyStatus.getOrderState()));
    }

    private void addOrderInfo(Order order, OrderModel orderModel) {
        order.setPartnerChannel(partnerChannelService.getPartnerChannel(order, orderModel.getChannel()));
        order.setCurrency(currencyService.getCurrency(order, orderModel.getCurrency()));
        order.setChannelType(channelTypeService.getChannelType(order, orderModel.getChannel()));
        order.setUserDeviceInfo(userDeviceInfoEntityConverter.toUserDeviceInfo(orderModel.getCustomer()));

        order.getAdditionalOrderInformation()
            .forEach(additionalInformation -> additionalInformation.setOrder(order));

    }

    private void processOrderFiltering(Order order) {
        orderBlockService.applyOrderBlock(order);
    }
}
