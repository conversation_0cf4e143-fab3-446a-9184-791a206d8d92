package com.bestseller.fulfilmentcoreservice.core.service.validator;

import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@Validated
public class OrderStateValidationServiceImpl implements OrderStateValidationService {

    @Override
    public void validate(Order order, Map<String, List<StateChange>> changes) {
        List<OrderLine> orderLines = order.getOrderLines();
        if (orderLines.isEmpty()) {
            throw new StateTransitionException("Order line list is empty!", order.getOrderId());
        }
        Set<String> changesKeySet = new HashSet<>(changes.keySet());
        orderLines.forEach(line -> {
            String ean = line.getEan();
            changesKeySet.remove(ean);
        });

        if (!changesKeySet.isEmpty()) {
            throw new StateTransitionException(
                "The applied changes contain EANs which do not belong to this order's lines.",
                order.getOrderId() + ", invalid EANs: " + changesKeySet);
        }
    }
}
