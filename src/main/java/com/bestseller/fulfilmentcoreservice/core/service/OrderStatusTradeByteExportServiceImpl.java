package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.TradebyteOrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Clock;
import java.time.ZonedDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStatusTradeByteExportServiceImpl implements OrderStatusTradeByteExportService {
    private static final String TRADEBYTE_ORDER_ID_PREFIX = "TB";
    private final Clock utcClock;
    private final OrderService orderService;
    private final QueueProducer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerQueueProducer;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void export(List<String> orderIds) {
        orderIds.forEach(orderId -> transactionTemplate
            .executeWithoutResult(status -> export(orderId)));
    }

    protected void export(String orderId) {
        if (!orderId.startsWith(TRADEBYTE_ORDER_ID_PREFIX)) {
            log.info("Order {} is not a TradeByte order, skipping export.", orderId);
            return;
        }

        Order order = orderService.findById(orderId);

        if (order.getMaxStatus().isLessThan(OrderState.DISPATCHED)) {
            log.info("None of the order lines reaches the final state, skipping export for order {}.", orderId);
            return;
        }

        order.getOrderLines()
            .forEach(orderLine -> orderLine.getOrderLineQtyStatus()
                .forEach(orderLineQtyStatus -> {
                    OrderState qtyStatus = orderLineQtyStatus.getOrderState();
                    String trackingNumber = null;
                    String returnShipmentId = null;
                    if (OrderState.CANCELLED != qtyStatus) {
                        trackingNumber = orderLineQtyStatus.getOrderStatusUpdateInfo().getTrackingNumber();
                        returnShipmentId = orderLineQtyStatus.getOrderStatusUpdateInfo().getReturnShipmentId();
                    }

                    if (OrderState.RETURNED == qtyStatus) {
                        // For order lines in return state, we need to send two messages,
                        // one for the return and one for the dispatched state
                        var dispatchedMessage = createOrderStatusUpdateTradebyte(
                            OrderState.DISPATCHED,
                            trackingNumber,
                            returnShipmentId,
                            orderLine.getEan(),
                            orderId,
                            order.getPartnerChannel().getChannelId(),
                            getTbOrderItemId(
                                orderLineQtyStatus.getTradebyteOrderLineQtyStatus(), orderLine.getLineNumber()));
                        orderStatusUpdateTradebyteProducerQueueProducer.enqueue(
                            EnqueueParams.create(dispatchedMessage));
                    }
                    var message = createOrderStatusUpdateTradebyte(
                        qtyStatus,
                        trackingNumber,
                        returnShipmentId,
                        orderLine.getEan(),
                        orderId,
                        order.getPartnerChannel().getChannelId(),
                        getTbOrderItemId(
                            orderLineQtyStatus.getTradebyteOrderLineQtyStatus(), orderLine.getLineNumber()));
                    orderStatusUpdateTradebyteProducerQueueProducer.enqueue(EnqueueParams.create(message));
                }));
        log.info("Order {} exported to Tradebyte successfully.", orderId);
    }

    @SuppressWarnings("checkstyle:ParameterNumber")
    private OrderStatusUpdateTradebyte createOrderStatusUpdateTradebyte(OrderState status,
                                                                        String trackingNumber,
                                                                        String returnShipmentId,
                                                                        String sku,
                                                                        String orderId,
                                                                        String channelId,
                                                                        Integer tbOrderItemId) {
        return new OrderStatusUpdateTradebyte()
            .withChannelSign(channelId)
            .withDateCreated(ZonedDateTime.now(utcClock))
            .withIdCode(getIdCode(status, trackingNumber, returnShipmentId))
            .withIdCodeReturnProposal(getIdCodeReturnProposal(status, returnShipmentId))
            .withStatus(status.getIdentifier())
            .withSku(sku)
            .withOrderId(orderId)
            .withTbOrderItemId(tbOrderItemId);
    }

    private String getIdCodeReturnProposal(OrderState status, String returnShipmentId) {
        return status == OrderState.DISPATCHED ? returnShipmentId : null;
    }

    private String getIdCode(OrderState status, String trackingNumber, String returnShipmentId) {
        return switch (status) {
            case OrderState.DISPATCHED -> trackingNumber;
            case OrderState.RETURNED -> returnShipmentId;
            default -> null;
        };
    }

    protected Integer getTbOrderItemId(TradebyteOrderLineQtyStatus tbOrderLineQtyStatus, Integer orderLineNumber) {
        if (tbOrderLineQtyStatus != null
            && tbOrderLineQtyStatus.getOriginalLineNumber() != null
            && tbOrderLineQtyStatus.getOriginalLineNumber() > 0
        ) {
            return tbOrderLineQtyStatus.getOriginalLineNumber();
        }
        return orderLineNumber;
    }
}
