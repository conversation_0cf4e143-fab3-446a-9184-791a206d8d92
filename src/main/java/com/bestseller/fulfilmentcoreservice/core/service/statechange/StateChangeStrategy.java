package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;

import java.util.List;

public interface StateChangeStrategy<T> {

    List<OrderStatusUpdateStateChange> defineOrderStateChange(T message, Order order);

    Class<T> getMessageType();
}
