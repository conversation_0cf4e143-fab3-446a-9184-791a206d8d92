package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderReturnedInStoreServiceImpl implements OrderReturnedInStoreService {

    private final OrderService orderService;

    private final StateChangeService stateChangeService;

    private final OrderStateService orderStateService;

    private final QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    private final OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    private final OrderToOrderStatusUpdatedPayloadConverter payloadConverter;

    private final QueueProducer<OrderReturnedInStore> refundRequestedProducerQueueProducer;

    @Override
    @Transactional
    public void process(OrderReturnedInStore orderReturnedInStore) {

        var order = orderService.findById(orderReturnedInStore.getOrderId());

        var orderStateChange = stateChangeService.captureOrderStateChange(orderReturnedInStore, order);

        orderStateService.apply(order, orderStateChange);

        log.debug("Process OrderReturnedInStore has updated order status to POS_RETURNED_IN_STORE for orderId {}",
            order.getOrderId());

        orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(order,
                payloadConverter.convert(order), OrderStatusUpdated.Type.POS_RETURNED_IN_STORE)
        ));

        refundRequestedProducerQueueProducer.enqueue(EnqueueParams.create(orderReturnedInStore));
    }
}
