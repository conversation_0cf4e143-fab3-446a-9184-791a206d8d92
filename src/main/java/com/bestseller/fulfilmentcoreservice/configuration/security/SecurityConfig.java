package com.bestseller.fulfilmentcoreservice.configuration.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

import static org.springframework.security.config.http.SessionCreationPolicy.STATELESS;

/**
 * Configuration for Spring Security.
 */
@Slf4j
@Configuration
public class SecurityConfig {

    @Value("${credentials.admin.username}")
    private String adminUsername;

    @Value("${credentials.admin.password}")
    private String adminPassword;

    /**
     * Password encoder.
     *
     * @return PasswordEncoder instance
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return PasswordEncoderFactories.createDelegatingPasswordEncoder();
    }

    /**
     * Configures http security.
     *
     * @param http Http security builder class.
     * @return filter chain
     * @throws Exception Throws when error occurs in setup.
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        String admin = UserRole.ADMIN.toString();

        return http.sessionManagement(sessionManagement -> sessionManagement
                .sessionCreationPolicy(STATELESS))
            .httpBasic(Customizer.withDefaults())
            .authorizeHttpRequests(authorize -> authorize
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/actuator/**").hasRole(admin)
                .requestMatchers("/features/**").hasRole(admin)
                .anyRequest().authenticated())
            .formLogin(formLogin -> formLogin.disable())
            .csrf(csrf -> csrf.disable())
            .build();
    }


    /**
     * User details service to config authentication.
     *
     * @return UserDetailsService
     */
    @Bean
    public UserDetailsService userDetailsService() {
        // The builder will ensure the passwords are encoded before saving in memory
        UserDetails admin = User.builder()
            .username(adminUsername)
            .password(adminPassword)
            .roles(UserRole.ADMIN.toString())
            .build();
        return new InMemoryUserDetailsManager(admin);
    }

    /**
     * User Roles enum.
     */
    public enum UserRole {
        ADMIN
    }
}
