package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.PaymentStatusUpdatedIdempotencyCheck;
import com.bestseller.fulfilmentcoreservice.core.service.PaymentStatusUpdatedService;
import com.bestseller.fulfilmentcoreservice.queue.UpdatePaymentStatusQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UpdatePaymentStatusQueueConfiguration {

    private final QueueProperties queueProperties;

    public UpdatePaymentStatusQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.updatePaymentStatusQueueProperties();
    }

    @Bean
    public QueueConfig updatePaymentStatusQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<PaymentStatusUpdated> updatePaymentStatusTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentStatusUpdated.class);
    }

    @Bean
    public QueueProducer<PaymentStatusUpdated> updatePaymentStatusQueueProducer(
        QueueConfig updatePaymentStatusQueueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> updatePaymentStatusTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            updatePaymentStatusQueueConfig,
            updatePaymentStatusTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public UpdatePaymentStatusQueueConsumer updatePaymentStatusQueueConsumer(
        PaymentStatusUpdatedIdempotencyCheck idempotencyCheck,
        QueueConfig updatePaymentStatusQueueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> updatePaymentStatusTaskPayloadTransformer,
        PaymentStatusUpdatedService paymentStatusUpdatedService) {
        return new UpdatePaymentStatusQueueConsumer(
            idempotencyCheck,
            updatePaymentStatusQueueConfig,
            updatePaymentStatusTaskPayloadTransformer,
            paymentStatusUpdatedService);
    }

}
