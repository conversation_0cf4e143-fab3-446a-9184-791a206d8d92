package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderForFulfilmentKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderForFulfilmentProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderForFulfilmentProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderForFulfilmentProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderForFulfilmentProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderForFulfilmentProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderForFulfillment> orderForFulfilmentProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderForFulfillment.class);
    }

    @Bean
    public QueueProducer<OrderForFulfillment> orderForFulfilmentProducerQueueProducer(
        QueueConfig orderForFulfilmentProducerQueueConfig,
        TaskPayloadTransformer<OrderForFulfillment> orderForFulfilmentProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderForFulfilmentProducerQueueConfig,
            orderForFulfilmentProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderForFulfilmentProducerQueueConsumer orderForFulfilmentProducerQueueConsumer(
        QueueConfig orderForFulfilmentProducerQueueConfig,
        TaskPayloadTransformer<OrderForFulfillment> orderForFulfilmentProducerTaskPayloadTransformer,
        OrderForFulfilmentKafkaMessageProducer orderForFulfilmentKafkaMessageProducer) {
        return new OrderForFulfilmentProducerQueueConsumer(
            orderForFulfilmentProducerQueueConfig,
            orderForFulfilmentProducerTaskPayloadTransformer,
            orderForFulfilmentKafkaMessageProducer
        );
    }
}
