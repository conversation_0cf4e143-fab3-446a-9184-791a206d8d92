package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.ValidOrderPlacedService;
import com.bestseller.fulfilmentcoreservice.queue.ValidOrderPlacedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ValidOrderPlacedQueueConfiguration {

    private final QueueProperties queueProperties;

    public ValidOrderPlacedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.validOrderPlacedQueueProperties();
    }

    @Bean
    public QueueConfig validOrderPlacedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, ValidOrderPlaced.class);
    }

    @Bean
    public QueueProducer<ValidOrderPlaced> validOrderPlacedQueueProducer(
        QueueConfig validOrderPlacedQueueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            validOrderPlacedQueueConfig,
            validOrderPlacedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public ValidOrderPlacedQueueConsumer validOrderPlacedQueueConsumer(
        IdempotencyCheckHandler<ValidOrderPlaced> idempotencyCheck,
        QueueConfig validOrderPlacedQueueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer,
        ValidOrderPlacedService validOrderPlacedService) {
        return new ValidOrderPlacedQueueConsumer(
            idempotencyCheck,
            validOrderPlacedQueueConfig,
            validOrderPlacedTaskPayloadTransformer,
            validOrderPlacedService
        );
    }
}
