package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderStatusUpdatedKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderStatusUpdatedProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderStatusUpdatedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderStatusUpdatedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderStatusUpdatedProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderStatusUpdatedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderStatusUpdated> orderStatusUpdatedProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderStatusUpdated.class);
    }

    @Bean
    public QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer(
        QueueConfig orderStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<OrderStatusUpdated> orderStatusUpdatedProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderStatusUpdatedProducerQueueConfig,
            orderStatusUpdatedProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderStatusUpdatedProducerQueueConsumer orderStatusUpdatedProducerQueueConsumer(
        QueueConfig orderStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<OrderStatusUpdated> orderStatusUpdatedProducerTaskPayloadTransformer,
        OrderStatusUpdatedKafkaMessageProducer orderStatusUpdatedKafkaMessageProducer) {
        return new OrderStatusUpdatedProducerQueueConsumer(
            orderStatusUpdatedProducerQueueConfig,
            orderStatusUpdatedProducerTaskPayloadTransformer,
            orderStatusUpdatedKafkaMessageProducer
        );
    }
}
