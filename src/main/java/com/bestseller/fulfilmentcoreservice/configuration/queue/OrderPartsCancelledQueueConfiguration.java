package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderPartsCancelledService;
import com.bestseller.fulfilmentcoreservice.queue.OrderPartsCancelledQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderPartsCancelledQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderPartsCancelledQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderPartsCancelledQueueProperties();
    }

    @Bean
    public QueueConfig orderPartsCancelledQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderPartsCancelled> orderPartsCancelledTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderPartsCancelled.class);
    }

    @Bean
    public QueueProducer<OrderPartsCancelled> orderPartsCancelledQueueProducer(
        QueueConfig orderPartsCancelledQueueConfig,
        TaskPayloadTransformer<OrderPartsCancelled> orderPartsCancelledTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderPartsCancelledQueueConfig,
            orderPartsCancelledTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderPartsCancelledQueueConsumer orderPartsCancelledQueueConsumer(
        IdempotencyCheckHandler<OrderPartsCancelled> idempotencyCheck,
        QueueConfig orderPartsCancelledQueueConfig,
        TaskPayloadTransformer<OrderPartsCancelled> orderPartsCancelledTaskPayloadTransformer,
        OrderPartsCancelledService orderPartsCancelledService) {
        return new OrderPartsCancelledQueueConsumer(
            idempotencyCheck,
            orderPartsCancelledQueueConfig,
            orderPartsCancelledTaskPayloadTransformer,
            orderPartsCancelledService);
    }
}
