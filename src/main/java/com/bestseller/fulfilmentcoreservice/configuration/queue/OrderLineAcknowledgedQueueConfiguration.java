package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineAcknowledgedService;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineAcknowledgedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderLineAcknowledgedQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderLineAcknowledgedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderLineAcknowledgedQueueProperties();
    }

    @Bean
    public QueueConfig orderLineAcknowledgedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderLineAcknowledged> orderLineAcknowledgedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderLineAcknowledged.class);
    }

    @Bean
    public QueueProducer<OrderLineAcknowledged> orderLineAcknowledgedQueueProducer(
        QueueConfig orderLineAcknowledgedQueueConfig,
        TaskPayloadTransformer<OrderLineAcknowledged> orderLineAcknowledgedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderLineAcknowledgedQueueConfig,
            orderLineAcknowledgedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderLineAcknowledgedQueueConsumer orderLineAcknowledgedQueueConsumer(
        IdempotencyCheckHandler<OrderLineAcknowledged> idempotencyChecker,
        QueueConfig orderLineAcknowledgedQueueConfig,
        TaskPayloadTransformer<OrderLineAcknowledged> orderLineAcknowledgedTaskPayloadTransformer,
        OrderLineAcknowledgedService orderLineAcknowledgedService) {
        return new OrderLineAcknowledgedQueueConsumer(
            idempotencyChecker,
            orderLineAcknowledgedQueueConfig,
            orderLineAcknowledgedTaskPayloadTransformer,
            orderLineAcknowledgedService);
    }
}
