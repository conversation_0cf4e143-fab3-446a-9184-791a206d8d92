package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.OrderPartsRoutedIdempotencyCheck;
import com.bestseller.fulfilmentcoreservice.core.service.OrderPartsRoutedService;
import com.bestseller.fulfilmentcoreservice.queue.OrderPartsRoutedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderPartsRoutedQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderPartsRoutedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderPartsRoutedQueueProperties();
    }

    @Bean
    public QueueConfig orderPartsRoutedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderPartsRouted> orderPartsRoutedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderPartsRouted.class);
    }

    @Bean
    public QueueProducer<OrderPartsRouted> orderPartsRoutedQueueProducer(
        QueueConfig orderPartsRoutedQueueConfig,
        TaskPayloadTransformer<OrderPartsRouted> orderPartsRoutedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderPartsRoutedQueueConfig,
            orderPartsRoutedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderPartsRoutedQueueConsumer orderPartsRoutedQueueConsumer(
        OrderPartsRoutedIdempotencyCheck idempotencyCheck,
        QueueConfig orderPartsRoutedQueueConfig,
        TaskPayloadTransformer<OrderPartsRouted> orderPartsRoutedTaskPayloadTransformer,
        OrderPartsRoutedService orderPartsRoutedService) {
        return new OrderPartsRoutedQueueConsumer(
            idempotencyCheck,
            orderPartsRoutedQueueConfig,
            orderPartsRoutedTaskPayloadTransformer,
            orderPartsRoutedService);
    }
}
