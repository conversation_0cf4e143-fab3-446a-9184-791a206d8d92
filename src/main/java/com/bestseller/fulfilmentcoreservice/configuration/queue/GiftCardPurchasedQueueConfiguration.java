package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.GiftCardPurchasedService;
import com.bestseller.fulfilmentcoreservice.queue.GiftCardPurchasedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GiftCardPurchasedQueueConfiguration {
    private final QueueProperties queueProperties;

    public GiftCardPurchasedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.giftCardPurchasedQueueProperties();
    }

    @Bean
    public QueueConfig giftCardPurchasedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<GiftCardPurchased> giftCardPurchasedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, GiftCardPurchased.class);
    }

    @Bean
    public QueueProducer<GiftCardPurchased> giftCardPurchasedQueueProducer(
        QueueConfig giftCardPurchasedQueueConfig,
        TaskPayloadTransformer<GiftCardPurchased> giftCardPurchasedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            giftCardPurchasedQueueConfig,
            giftCardPurchasedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public GiftCardPurchasedQueueConsumer giftCardPurchasedQueueConsumer(
        IdempotencyCheckHandler<GiftCardPurchased> idempotencyCheck,
        QueueConfig giftCardPurchasedQueueConfig,
        TaskPayloadTransformer<GiftCardPurchased> giftCardPurchasedTaskPayloadTransformer,
        GiftCardPurchasedService giftCardPurchasedService) {
        return new GiftCardPurchasedQueueConsumer(
            idempotencyCheck,
            giftCardPurchasedQueueConfig,
            giftCardPurchasedTaskPayloadTransformer,
            giftCardPurchasedService
        );
    }
}
