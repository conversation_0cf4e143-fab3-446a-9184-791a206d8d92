package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.spring.QueueProperties;
import jakarta.validation.Valid;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@ConfigurationProperties(prefix = "database-queue")
public record DatabaseQueueConfigurationProperties(
    @Valid @NestedConfigurationProperty QueueProperties updatePaymentStatusQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderPartsCancelledQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderLineAcknowledgedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderLineDispatchedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderLineExportedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderLineReturnedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderReturnedInStoreQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderPartsRoutedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties giftCardPurchasedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderCancelledProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderFinalizedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderForFulfilmentProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderManualUpdateProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderReturnedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderStatusUpdatedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderStatusUpdateTradebyteProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties refundCreationRequestedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties refundRequestedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties validOrderPlacedQueueProperties
) {
}
