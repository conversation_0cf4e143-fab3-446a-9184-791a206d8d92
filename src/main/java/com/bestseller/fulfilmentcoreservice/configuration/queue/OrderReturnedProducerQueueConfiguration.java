package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderReturnedKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderReturnedProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderReturnedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderReturnedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderReturnedProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderReturnedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderReturned> orderReturnedProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderReturned.class);
    }

    @Bean
    public QueueProducer<OrderReturned> orderReturnedProducerQueueProducer(
        QueueConfig orderReturnedProducerQueueConfig,
        TaskPayloadTransformer<OrderReturned> orderReturnedProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderReturnedProducerQueueConfig,
            orderReturnedProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderReturnedProducerQueueConsumer orderReturnedProducerQueueConsumer(
        QueueConfig orderReturnedProducerQueueConfig,
        TaskPayloadTransformer<OrderReturned> orderReturnedProducerTaskPayloadTransformer,
        OrderReturnedKafkaMessageProducer orderReturnedKafkaMessageProducer) {
        return new OrderReturnedProducerQueueConsumer(
            orderReturnedProducerQueueConfig,
            orderReturnedProducerTaskPayloadTransformer,
            orderReturnedKafkaMessageProducer
        );
    }
}
