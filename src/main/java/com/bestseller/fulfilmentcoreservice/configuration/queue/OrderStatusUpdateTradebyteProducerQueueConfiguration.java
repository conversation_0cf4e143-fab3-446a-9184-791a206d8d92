package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderStatusUpdateTradebyteKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderStatusUpdateTradebyteProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderStatusUpdateTradebyteProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderStatusUpdateTradebyteProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderStatusUpdateTradebyteProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderStatusUpdateTradebyteProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderStatusUpdateTradebyte.class);
    }

    @Bean
    public QueueProducer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerQueueProducer(
        QueueConfig orderStatusUpdateTradebyteProducerQueueConfig,
        TaskPayloadTransformer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderStatusUpdateTradebyteProducerQueueConfig,
            orderStatusUpdateTradebyteProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderStatusUpdateTradebyteProducerQueueConsumer orderStatusUpdateTradebyteProducerQueueConsumer(
        QueueConfig orderStatusUpdateTradebyteProducerQueueConfig,
        TaskPayloadTransformer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerTaskPayloadTransformer,
        OrderStatusUpdateTradebyteKafkaMessageProducer orderStatusUpdateTradebyteKafkaMessageProducer) {
        return new OrderStatusUpdateTradebyteProducerQueueConsumer(
            orderStatusUpdateTradebyteProducerQueueConfig,
            orderStatusUpdateTradebyteProducerTaskPayloadTransformer,
            orderStatusUpdateTradebyteKafkaMessageProducer
        );
    }
}
