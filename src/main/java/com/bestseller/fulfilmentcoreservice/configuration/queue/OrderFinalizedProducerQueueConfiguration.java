package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderFinalizedKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderFinalizedProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderFinalizedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderFinalizedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderFinalizedProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderFinalizedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderFinalized> orderFinalizedProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderFinalized.class);
    }

    @Bean
    public QueueProducer<OrderFinalized> orderFinalizedProducerQueueProducer(
        QueueConfig orderFinalizedProducerQueueConfig,
        TaskPayloadTransformer<OrderFinalized> orderFinalizedProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderFinalizedProducerQueueConfig,
            orderFinalizedProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderFinalizedProducerQueueConsumer orderFinalizedProducerQueueConsumer(
        QueueConfig orderFinalizedProducerQueueConfig,
        TaskPayloadTransformer<OrderFinalized> orderFinalizedProducerTaskPayloadTransformer,
        OrderFinalizedKafkaMessageProducer orderFinalizedKafkaMessageProducer) {
        return new OrderFinalizedProducerQueueConsumer(
            orderFinalizedProducerQueueConfig,
            orderFinalizedProducerTaskPayloadTransformer,
            orderFinalizedKafkaMessageProducer
        );
    }
}
