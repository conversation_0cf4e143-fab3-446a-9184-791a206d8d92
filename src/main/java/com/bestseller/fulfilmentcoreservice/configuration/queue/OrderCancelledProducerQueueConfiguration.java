package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderCancelledKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderCancelledProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderCancelledProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderCancelledProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderCancelledProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderCancelledProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderCancelled> orderCancelledProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderCancelled.class);
    }

    @Bean
    public QueueProducer<OrderCancelled> orderCancelledProducerQueueProducer(
        QueueConfig orderCancelledProducerQueueConfig,
        TaskPayloadTransformer<OrderCancelled> orderCancelledProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderCancelledProducerQueueConfig,
            orderCancelledProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderCancelledProducerQueueConsumer orderCancelledProducerQueueConsumer(
        QueueConfig orderCancelledProducerQueueConfig,
        TaskPayloadTransformer<OrderCancelled> orderCancelledProducerTaskPayloadTransformer,
        OrderCancelledKafkaMessageProducer orderCancelledKafkaMessageProducer) {
        return new OrderCancelledProducerQueueConsumer(
            orderCancelledProducerQueueConfig,
            orderCancelledProducerTaskPayloadTransformer,
            orderCancelledKafkaMessageProducer
        );
    }
}
