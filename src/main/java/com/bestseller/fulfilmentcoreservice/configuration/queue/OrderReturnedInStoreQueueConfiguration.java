package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderReturnedInStoreService;
import com.bestseller.fulfilmentcoreservice.queue.OrderReturnedInStoreQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderReturnedInStoreQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderReturnedInStoreQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderReturnedInStoreQueueProperties();
    }

    @Bean
    public QueueConfig orderReturnedInStoreQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderReturnedInStore> orderReturnedInStoreTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderReturnedInStore.class);
    }

    @Bean
    public QueueProducer<OrderReturnedInStore> orderReturnedInStoreQueueProducer(
        QueueConfig orderReturnedInStoreQueueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> orderReturnedInStoreTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderReturnedInStoreQueueConfig,
            orderReturnedInStoreTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderReturnedInStoreQueueConsumer orderReturnedInStoreQueueConsumer(
        IdempotencyCheckHandler<OrderReturnedInStore> idempotencyCheck,
        QueueConfig orderReturnedInStoreQueueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> orderReturnedInStoreTaskPayloadTransformer,
        OrderReturnedInStoreService orderReturnedInStoreService) {
        return new OrderReturnedInStoreQueueConsumer(
            idempotencyCheck,
            orderReturnedInStoreQueueConfig,
            orderReturnedInStoreTaskPayloadTransformer,
            orderReturnedInStoreService);
    }
}
