package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineReturnedService;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineReturnedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderLineReturnedQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderLineReturnedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderLineReturnedQueueProperties();
    }

    @Bean
    public QueueConfig orderLineReturnedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderLineReturned> orderLineReturnedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderLineReturned.class);
    }

    @Bean
    public QueueProducer<OrderLineReturned> orderLineReturnedQueueProducer(
        QueueConfig orderLineReturnedQueueConfig,
        TaskPayloadTransformer<OrderLineReturned> orderLineReturnedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderLineReturnedQueueConfig,
            orderLineReturnedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderLineReturnedQueueConsumer orderLineReturnedQueueConsumer(
        IdempotencyCheckHandler<OrderLineReturned> idempotencyCheck,
        QueueConfig orderLineReturnedQueueConfig,
        TaskPayloadTransformer<OrderLineReturned> orderLineReturnedTaskPayloadTransformer,
        OrderLineReturnedService orderLineReturnedService) {
        return new OrderLineReturnedQueueConsumer(
            idempotencyCheck,
            orderLineReturnedQueueConfig,
            orderLineReturnedTaskPayloadTransformer,
            orderLineReturnedService);
    }
}
