package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.RefundCreationRequestedKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.RefundCreationRequestedProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RefundCreationRequestedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public RefundCreationRequestedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.refundCreationRequestedProducerQueueProperties();
    }

    @Bean
    public QueueConfig refundCreationRequestedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, RefundCreationRequested.class);
    }

    @Bean
    public QueueProducer<RefundCreationRequested> refundCreationRequestedProducerQueueProducer(
        QueueConfig refundCreationRequestedProducerQueueConfig,
        TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            refundCreationRequestedProducerQueueConfig,
            refundCreationRequestedProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public RefundCreationRequestedProducerQueueConsumer refundCreationRequestedProducerQueueConsumer(
        QueueConfig refundCreationRequestedProducerQueueConfig,
        TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedProducerTaskPayloadTransformer,
        RefundCreationRequestedKafkaMessageProducer refundCreationRequestedKafkaMessageProducer) {
        return new RefundCreationRequestedProducerQueueConsumer(
            refundCreationRequestedProducerQueueConfig,
            refundCreationRequestedProducerTaskPayloadTransformer,
            refundCreationRequestedKafkaMessageProducer
        );
    }
}
