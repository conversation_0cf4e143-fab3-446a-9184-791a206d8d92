package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.OrderManualUpdateKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.OrderManualUpdateProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderManualUpdateProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderManualUpdateProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderManualUpdateProducerQueueProperties();
    }

    @Bean
    public QueueConfig orderManualUpdateProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderManualUpdate> orderManualUpdateProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderManualUpdate.class);
    }

    @Bean
    public QueueProducer<OrderManualUpdate> orderManualUpdateProducerQueueProducer(
        QueueConfig orderManualUpdateProducerQueueConfig,
        TaskPayloadTransformer<OrderManualUpdate> orderManualUpdateProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderManualUpdateProducerQueueConfig,
            orderManualUpdateProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderManualUpdateProducerQueueConsumer orderManualUpdateProducerQueueConsumer(
        QueueConfig orderManualUpdateProducerQueueConfig,
        TaskPayloadTransformer<OrderManualUpdate> orderManualUpdateProducerTaskPayloadTransformer,
        OrderManualUpdateKafkaMessageProducer orderManualUpdateKafkaMessageProducer) {
        return new OrderManualUpdateProducerQueueConsumer(
            orderManualUpdateProducerQueueConfig,
            orderManualUpdateProducerTaskPayloadTransformer,
            orderManualUpdateKafkaMessageProducer
        );
    }
}
