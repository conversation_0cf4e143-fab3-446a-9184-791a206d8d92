package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineDispatchedService;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineDispatchedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderLineDispatchedQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderLineDispatchedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderLineDispatchedQueueProperties();
    }

    @Bean
    public QueueConfig orderLineDispatchedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderLineDispatched> orderLineDispatchedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderLineDispatched.class);
    }

    @Bean
    public QueueProducer<OrderLineDispatched> orderLineDispatchedQueueProducer(
        QueueConfig orderLineDispatchedQueueConfig,
        TaskPayloadTransformer<OrderLineDispatched> orderLineDispatchedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderLineDispatchedQueueConfig,
            orderLineDispatchedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderLineDispatchedQueueConsumer orderLineDispatchedQueueConsumer(
        IdempotencyCheckHandler<OrderLineDispatched> idempotencyCheck,
        QueueConfig orderLineDispatchedQueueConfig,
        TaskPayloadTransformer<OrderLineDispatched> orderLineDispatchedTaskPayloadTransformer,
        OrderLineDispatchedService orderLineDispatchedService) {
        return new OrderLineDispatchedQueueConsumer(
            idempotencyCheck,
            orderLineDispatchedQueueConfig,
            orderLineDispatchedTaskPayloadTransformer,
            orderLineDispatchedService);
    }
}
