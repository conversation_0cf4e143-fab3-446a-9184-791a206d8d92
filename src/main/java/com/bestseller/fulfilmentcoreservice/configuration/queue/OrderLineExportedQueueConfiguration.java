package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineExportedService;
import com.bestseller.fulfilmentcoreservice.queue.OrderLineExportedQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderLineExportedQueueConfiguration {

    private final QueueProperties queueProperties;

    public OrderLineExportedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.orderLineExportedQueueProperties();
    }

    @Bean
    public QueueConfig orderLineExportedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderLineExported> orderLineExportedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderLineExported.class);
    }

    @Bean
    public QueueProducer<OrderLineExported> orderLineExportedQueueProducer(
        QueueConfig orderLineExportedQueueConfig,
        TaskPayloadTransformer<OrderLineExported> orderLineExportedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderLineExportedQueueConfig,
            orderLineExportedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderLineExportedQueueConsumer orderLineExportedQueueConsumer(
        IdempotencyCheckHandler<OrderLineExported> idempotencyCheck,
        QueueConfig orderLineExportedQueueConfig,
        TaskPayloadTransformer<OrderLineExported> orderLineExportedSTaskPayloadTransformer,
        OrderLineExportedService orderLineExportedService) {
        return new OrderLineExportedQueueConsumer(
            idempotencyCheck,
            orderLineExportedQueueConfig,
            orderLineExportedSTaskPayloadTransformer,
            orderLineExportedService);
    }
}
