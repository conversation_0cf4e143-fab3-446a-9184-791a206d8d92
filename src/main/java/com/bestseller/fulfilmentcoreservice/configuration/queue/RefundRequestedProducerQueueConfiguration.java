package com.bestseller.fulfilmentcoreservice.configuration.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.fulfilmentcoreservice.core.messaging.producer.RefundRequestedKafkaMessageProducer;
import com.bestseller.fulfilmentcoreservice.queue.RefundRequestedProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RefundRequestedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public RefundRequestedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.refundRequestedProducerQueueProperties();
    }

    @Bean
    public QueueConfig refundRequestedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<OrderReturnedInStore> refundRequestedProducerTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderReturnedInStore.class);
    }

    @Bean
    public QueueProducer<OrderReturnedInStore> refundRequestedProducerQueueProducer(
        QueueConfig refundRequestedProducerQueueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> refundRequestedProducerTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            refundRequestedProducerQueueConfig,
            refundRequestedProducerTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public RefundRequestedProducerQueueConsumer refundRequestedProducerQueueConsumer(
        QueueConfig refundRequestedProducerQueueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> refundRequestedProducerTaskPayloadTransformer,
        RefundRequestedKafkaMessageProducer refundRequestedKafkaMessageProducer) {
        return new RefundRequestedProducerQueueConsumer(
            refundRequestedProducerQueueConfig,
            refundRequestedProducerTaskPayloadTransformer,
            refundRequestedKafkaMessageProducer
        );
    }
}
