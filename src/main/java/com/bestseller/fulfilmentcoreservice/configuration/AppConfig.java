package com.bestseller.fulfilmentcoreservice.configuration;

import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Clock;

/**
 * Main app configuration.
 */
@Configuration
@EnableScheduling
public class AppConfig {

    /**
     * Clock instance to use across the application.
     *
     * @return clock instance
     */
    @Bean
    public Clock utcClock() {
        return Clock.systemUTC();
    }

    /**
     * Validator bean for Jakarta validation.
     *
     * @return validator instance
     */
    @Bean
    public Validator validator() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        return factory.getValidator();
    }
}
