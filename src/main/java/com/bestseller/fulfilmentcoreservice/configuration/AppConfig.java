package com.bestseller.fulfilmentcoreservice.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Clock;

/**
 * Main app configuration.
 */
@Configuration
@EnableScheduling
public class AppConfig {

    /**
     * Clock instance to use across the application.
     *
     * @return clock instance
     */
    @Bean
    public Clock utcClock() {
        return Clock.systemUTC();
    }
}
