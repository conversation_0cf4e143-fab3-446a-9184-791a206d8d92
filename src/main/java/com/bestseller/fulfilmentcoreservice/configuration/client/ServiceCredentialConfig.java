package com.bestseller.fulfilmentcoreservice.configuration.client;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@Getter
@Setter
@ConfigurationProperties("gateway")
@AllArgsConstructor
public class ServiceCredentialConfig {

    private static final String PAYMENT_SERVICE = "payment-service";

    private Map<String, ServiceCredential> services;

    public ServiceCredential getPaymentServiceCredential() {
        return services.get(PAYMENT_SERVICE);
    }
}
