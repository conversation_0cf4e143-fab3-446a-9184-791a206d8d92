package com.bestseller.fulfilmentcoreservice.configuration.client;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class WebClientConfig {

    @Bean
    public WebClient paymentServiceClient(ServiceCredentialConfig config) {
        return getClient(config.getPaymentServiceCredential());
    }

    private WebClient getClient(ServiceCredential serviceCredential) {
        return WebClient.builder()
            .baseUrl(serviceCredential.getUrl())
            .defaultHeaders(httpHeaders -> {
                if (serviceCredential.isAuth()) {
                    httpHeaders.setBasicAuth(
                        serviceCredential.getUsername(),
                        serviceCredential.getPassword());
                }
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            }).build();
    }

}
