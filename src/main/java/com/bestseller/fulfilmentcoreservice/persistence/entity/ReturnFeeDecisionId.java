package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.EcomCountry;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

import static jakarta.persistence.EnumType.STRING;

@Getter
@Setter
@Builder
@EqualsAndHashCode
@ToString
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class ReturnFeeDecisionId implements Serializable {

    @Column(nullable = false)
    @Enumerated(STRING)
    private CustomerReturnReason customerReturnReason;

    @Column(nullable = false)
    @Enumerated(STRING)
    private EcomCountry ecomCountryCode;
}
