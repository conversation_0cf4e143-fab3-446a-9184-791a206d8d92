package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;


/**
 * This class extends the base entity and brings the use of optimistic locking to the data model, using persistence.
 */
@MappedSuperclass
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class VersionedEntity extends BaseEntity {

    @Version
    private Integer version;

}
