package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Immutable;

/**
 * This class is the main representation of Partner Channels. It's mapped 1:1 to AmosActionCode in the Legacy OMS.
 */
@Entity
@Getter
@Setter
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
@Immutable
@EqualsAndHashCode(of = {"channelId", "actionCode", "description", "language", "carrier", "custCompany", "currency"})
@NoArgsConstructor
@SuperBuilder
public class PartnerChannel extends BaseEntity {

    @Id
    @Column(length = 20, updatable = false)
    private String channelId;

    @Column(nullable = false)
    private Integer actionCode;

    @Column(nullable = false)
    private String description;

    @Column(length = 2)
    private String language;

    @Column(length = 50)
    private String carrier;

    @Column(length = 10)
    private String custCompany;

    @Column(length = 3)
    private String currency;

}
