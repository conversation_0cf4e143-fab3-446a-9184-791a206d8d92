package com.bestseller.fulfilmentcoreservice.persistence.entity;

import lombok.Getter;

@Getter
public final class OrderPlacedConfig {
    public static final int MAX_LENGTH_LANGUAGE = 2;
    public static final int MAX_LENGTH_COUNTRY = 3;
    public static final int ADDRESS_MAX_SIZE = 32;
    public static final int ADDRESS_3_MAX_SIZE = 25;
    public static final int CITY_MAX_SIZE = 24;
    public static final String NL_POST_CODE_REGEX = "^(?i)[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$";

    private OrderPlacedConfig() {
    }
}
