package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Table(name = "OrderFulfillmentPart")
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Getter
@Setter
@ToString(exclude = "order")
public class OrderFulfillmentPart extends VersionedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = 2)
    private Integer partNumber;

    @Column(nullable = false, length = 50)
    private String fulfillmentNode;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "orderFulfillmentPartId")
    private List<OrderLineQtyStatus> orderLineQtyStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "orderId")
    private Order order;

    @Column(nullable = false)
    private boolean holdFromRouting;

    @Column
    private LocalDateTime dispatchDate;

    @Column(length = 50)
    private String trackingNumber;

    @Column(length = 50)
    private String returnTrackingNumber;

    @Column(nullable = false)
    private LocalDate orangePrinted;

    @Column(length = 50)
    private String carrierName;

    @Column
    private Integer storeId;

    @Column
    @Builder.Default
    private boolean active = true;
}
