package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.EntryType;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderColumn;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.JdbcTypeCode;

import java.sql.Types;
import java.util.List;
import java.util.UUID;

/**
 * This class represents the Order Line, a subtype of OrderEntry. The Order Line
 * indicates whether the entry is a bonus product and has OrderLineQtyStatus
 * entries, one per quantity in the line allowing line quantities to occupy
 * different states within the order lifecycle process. It uses persistence
 * annotations to map the object to the database.
 */
@Entity
@Cacheable
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Getter
@Setter
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class OrderLine extends VersionedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JdbcTypeCode(Types.CHAR)
    @Column(nullable = false, length = 36)
    private UUID ecomId;

    @Enumerated(EnumType.STRING)
    private EntryType type;

    private String name;

    @Column(length = 24)
    private String ean;

    @Column(length = 100)
    private String vatClassId;

    private Integer originalQty;

    private Integer openQty;

    @Column(length = 256)
    private String promotionId;

    @Column(length = 256)
    private String campaignId;

    @Column(length = 256)
    private String couponId;

    private boolean bonusProduct;

    private int lineNumber;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderLineId")
    @OrderColumn(name = "indexCol")
    private List<OrderLineQtyStatus> orderLineQtyStatus;

    @Column(length = 63)
    private String brandDescription;

    @Column(nullable = false)
    private boolean virtualProduct;

    @Column(length = 64)
    private String skuId;

    @Column(length = 256)
    private String partnerReference;

    @Transient
    private boolean changed;

    public boolean isRegularProduct() {
        return !virtualProduct && !bonusProduct;
    }
}
