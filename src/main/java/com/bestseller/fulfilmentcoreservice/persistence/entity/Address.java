package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class Address extends VersionedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = 10)
    private String postCode;

    @Column(length = 32)
    private String firstName;

    @Column(length = 26)
    private String lastName;

    @Column(length = 32)
    private String address1;

    @Column(length = 32)
    private String address2;

    @Column(length = 25)
    private String address3;

    @Column(length = 4)
    private String countryCode;

    @Column(length = 24)
    private String city;

    @Column(length = 25)
    private String state;

    private String phone;

    @Column(length = 10)
    private String salutation;

    @Enumerated(EnumType.STRING)
    @Column(length = 25)
    private CarrierVariant carrierVariant;

    @Column(length = 20)
    private String physicalStoreId;

    @Column(length = 100)
    private String deliveryOption;

    @Column(length = 40)
    private String parcelLocker;

    @Column(length = 32)
    private String houseNumber;

    @Column(length = 10)
    private String houseNumberExt;
}
