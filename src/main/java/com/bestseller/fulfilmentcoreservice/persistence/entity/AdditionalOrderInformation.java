package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.EntityType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class AdditionalOrderInformation extends VersionedEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Enumerated(EnumType.STRING)
    private EntityType entityType;

    @Column(name = "`key`", length = 256)
    private String key;

    @Column(name = "`value`", length = 512)
    private String value;

    @ManyToOne
    @JoinColumn(name = "orderId")
    private Order order;
}
