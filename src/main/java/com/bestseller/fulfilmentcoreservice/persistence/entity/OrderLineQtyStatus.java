package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.logistics.statetransition.OrderState;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Transient;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * This class represents the Order Line Quantity Status. Each represents the order state of the quantity in the order
 * and allows line quantities to occupy different states within the order lifecycle process. It uses persistence
 * annotations to map the object to the database.
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor(access = AccessLevel.PRIVATE) // keep it private in order to force usage of the builder.
public class OrderLineQtyStatus extends VersionedEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(value = EnumType.STRING)
    @Builder.Default
    private OrderState orderState = OrderState.PLACED;

    @Enumerated(value = EnumType.STRING)
    @Builder.Default
    private OrderState prevStatus = OrderState.PLACED;

    @ManyToOne
    @JoinColumn(name = "orderLineId", insertable = false, updatable = false)
    private OrderLine orderLine;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "tradebyteOrderLineQtyStatusId")
    private TradebyteOrderLineQtyStatus tradebyteOrderLineQtyStatus;

    private int indexCol;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderFulfillmentPartId")
    private OrderFulfillmentPart orderFulfillmentPart;

    //Refer to oms WMSRef (warehouseMessageReference)
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderStatusUpdateInfoId")
    private OrderStatusUpdateInfo orderStatusUpdateInfo;

    @Transient
    private boolean isChanged;

}
