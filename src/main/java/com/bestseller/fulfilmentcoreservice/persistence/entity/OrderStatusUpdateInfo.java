package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;

import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Getter
@Setter
public class OrderStatusUpdateInfo extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = 50)
    private String returnShipmentId;

    @Column(length = 50)
    private String carrierName;

    @Column(length = 50)
    private String trackingNumber;

    @Column(length = 50)
    @Enumerated(EnumType.STRING)
    private CustomerReturnReason customerReturnReason;

    @Column(length = 50)
    @Enumerated(EnumType.STRING)
    private ReturnType returnType;

    @Column
    private LocalDate returnDate;

    @JdbcTypeCode(Types.CHAR)
    @Column(nullable = false, length = 36)
    private UUID refundRequestId;

    @Column
    private LocalDateTime dispatchDate;

    @ManyToOne
    @JoinColumn(name = "orderId")
    private Order order;

    @Column
    private LocalDateTime actualDispatchDate;

    @Column(length = 50)
    @Enumerated(EnumType.STRING)
    private CancelReason cancelReason;

    @Column
    private LocalDate cancellationDate;

    public void merge(OrderStatusUpdateInfo from) {

        if (from.getOrder() != null) {
            this.setOrder(from.getOrder());
        }

        if (from.getCarrierName() != null) {
            this.setCarrierName(from.getCarrierName());
        }

        if (from.getTrackingNumber() != null) {
            this.setTrackingNumber(from.getTrackingNumber());
        }

        if (from.getCancelReason() != null) {
            this.setCancelReason(from.getCancelReason());
        }

        if (from.getCancellationDate() != null) {
            this.setCancellationDate(from.getCancellationDate());
        }

        if (from.getDispatchDate() != null) {
            this.setDispatchDate(from.getDispatchDate());
        }

        if (from.getActualDispatchDate() != null) {
            this.setActualDispatchDate(from.getActualDispatchDate());
        }

        if (from.getReturnShipmentId() != null) {
            this.setReturnShipmentId(from.getReturnShipmentId());
        }

        if (from.getCustomerReturnReason() != null) {
            this.setCustomerReturnReason(from.getCustomerReturnReason());
        }

        if (from.getReturnType() != null) {
            this.setReturnType(from.getReturnType());
        }

        if (from.getReturnDate() != null) {
            this.setReturnDate(from.getReturnDate());
        }
    }
}
