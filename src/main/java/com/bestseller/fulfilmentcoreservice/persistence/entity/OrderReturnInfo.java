package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;


/**
 * This entity has the main purpose to keep the backward compatibility for the OMS to FCS data migration.
 */
@Table
@Entity
@Builder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE) // keep it private in order to force usage of the builder.
public class OrderReturnInfo extends VersionedEntity {

    @Id
    private String orderId;

    @Column(nullable = false)
    private LocalDate latestReturnDate;
}
