package com.bestseller.fulfilmentcoreservice.persistence.entity;

import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;

import static jakarta.persistence.CascadeType.ALL;

@Table(name = "Orders")
@Entity
@SuperBuilder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE) // keep it private in order to force usage of the builder.
public class Order extends VersionedEntity {

    @Id
    private String orderId;

    @Column(nullable = false)
    private ZonedDateTime orderDate;

    private LocalDate announcedDeliveryDate;

    private String checkoutScopeId;

    @Enumerated(EnumType.STRING)
    private Market market;

    @Enumerated(EnumType.STRING)
    private Platform platform;

    @Enumerated(EnumType.STRING)
    private ChannelType channelType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ShippingMethod shippingMethod;

    @Column(length = 3, nullable = false)
    private String currency;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private OrderState minStatus = OrderState.PLACED;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private OrderState maxStatus = OrderState.PLACED;

    @Column(length = 32)
    private String externalOrderId;

    private boolean test;

    private boolean billToMatchesShipTo;

    @OneToOne(cascade = ALL)
    @JoinColumn(name = "shippingAddressId")
    private Address shippingAddress;

    @OneToOne(cascade = ALL)
    @JoinColumn(name = "billingAddressId")
    private Address billingAddress;

    @OneToOne(cascade = ALL)
    @JoinColumn(name = "customerId")
    private Customer customer;

    @Enumerated(EnumType.STRING)
    private Brand brand;

    @OneToMany(cascade = ALL)
    @JoinColumn(name = "orderId")
    private List<OrderLine> orderLines;

    // PartnerChannel is mapped 1:1 to AmosActionCode
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "partnerChannelId")
    private PartnerChannel partnerChannel;

    @OneToOne(cascade = ALL)
    @JoinColumn(name = "userDeviceInfoId")
    private UserDeviceInfo userDeviceInfo;

    private boolean orderPaymentAuthorised;

    private boolean returnFeeCharged;

    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderId")
    private List<AdditionalOrderInformation> additionalOrderInformation;

    private boolean isFinalized;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "orderId", referencedColumnName = "orderId")
    private List<OrderFulfillmentPart> orderFulfillmentParts;

    @OneToOne(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private OrderBlock orderBlock;

    @Column
    private String isoStoreId;

    private boolean offlinePayment;

    public void setOrderBlock(OrderBlock orderBlock) {
        if (orderBlock == null) {
            this.orderBlock = null;
            return;
        }
        this.orderBlock = orderBlock;
        orderBlock.setOrder(this);
    }
}
