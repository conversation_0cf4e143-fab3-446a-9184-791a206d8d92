package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Table
@Getter
@Setter
@Entity
@Builder
@ToString
@Cacheable
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ReturnFeeDecision {

    @EmbeddedId
    private ReturnFeeDecisionId returnFeeDecisionId;

    @Column(nullable = false)
    private boolean chargeReturnFee;

}
