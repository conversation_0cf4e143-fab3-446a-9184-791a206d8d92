package com.bestseller.fulfilmentcoreservice.persistence.entity;

import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.ZonedDateTime;


/**
 * This class represents provides the audit trail columns to be reused across the data model, using persistence
 * annotations to map the object to the database.
 */
@MappedSuperclass
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class BaseEntity {

    @CreationTimestamp
    private ZonedDateTime createdTS;
    @UpdateTimestamp
    private ZonedDateTime lastModifiedTS;

}
