package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum EcomCountry {

    AD("Andorra"),
    AE("United Arab Emirates"),
    AL("Albania"),
    AM("Armenia"),
    AT("Austria"),
    BA("Bosnia & Herzegovina"),
    BE("Belgium"),
    BG("Bulgaria"),
    CA("Canada"),
    CH("Switzerland"),
    CY("Cyprus"),
    CZ("Czech Republic"),
    DE("Germany"),
    DK("Denmark"),
    DZ("Algeria"),
    EE("Estonia"),
    EG("Egypt"),
    ES("Spain"),
    FI("Finland"),
    FR("France"),
    GB("United Kingdom"),
    GR("Greece"),
    HR("Croatia"),
    HU("Hungary"),
    IE("Ireland"),
    IT("Italy"),
    JO("Jordan"),
    KW("Kuwait"),
    LT("Lithuania"),
    LU("Luxembourg"),
    LV("Latvia"),
    MA("Morocco"),
    MC("Monaco"),
    ME("Montenegro"),
    MK("North Macedonia"),
    MT("Malta"),
    NL("Netherlands"),
    NO("Norway"),
    PL("Poland"),
    PT("Portugal"),
    QA("Qatar"),
    RO("Romania"),
    RS("Serbia"),
    SA("Saudi Arabia"),
    SE("Sweden"),
    SI("Slovenia"),
    SK("Slovakia"),
    TN("Tunisia"),
    UA("Ukraine"),
    US("United States"),
    ROTW("Rest of the world");

    private final String countryName;

    public static EcomCountry findByCountryCode(String countryCode) {
        return Optional.ofNullable(countryCode)
            .map(String::toUpperCase)
            .map(EcomCountry::valueOf)
            .orElse(ROTW);
    }
}
