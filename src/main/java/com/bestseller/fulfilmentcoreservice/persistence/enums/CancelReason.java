package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Cancel reason enumeration.
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum CancelReason {

    /**
     * The cancel reason if item is not available.
     */
    ITEM_NOT_AVAILABLE(1, "ITEM_NOT_AVAILABLE"),
    /**
     * The cancel reason if available last item was damaged.
     */
    ITEM_DAMAGED(2, "ITEM_DAMAGED"),
    /**
     * The cancel reason if carrier refused the shipment.
     */
    REFUSED_BY_CARRIER(3, "REFUSED_BY_CARRIER"),
    /**
     * The cancel reason if shipment cancelled in warehouse by advice of client company.
     */
    CLIENT_COMPANY_CANCELLATION(4, "CLIENT_COMPANY_CANCELLATION"),
    /**
     * The cancel reason if happens cancellation from external warehouse.
     */
    WAREHOUSE_CANCELLATION(5, "WAREHOUSE_CANCELLATION"),
    /**
     * The cancel reason if happens cancellation from external warehouse (3GL).
     */
    WAREHOUSE_3GL_CANCELLATION(6, "WAREHOUSE_3GL_CANCELLATION"),
    /**
     * The cancel reason if happens cancellation of order detail before processing
     * (manually from callcenter; due to missing payments).
     */
    MANUAL_CANCELLATION(20, "MANUAL_CANCELLATION"),
    /**
     * If store has rejected to fulfill order this cancellation status will be used.
     * This rejection could happen if order is eligible for shipping from store but wasn't fulfilled by any store.
     */
    STORE_REJECTION(98, "STORE_REJECTION"),
    /**
     * The cancel reason if  happens auto cancel due to incomplete shipment
     * (only for 'all-or-nothing' agreement: if at least one detail was non deliverable,
     * all other details have to be cancelled as well -> these will be flagged as auto cancel).
     */
    AUTO_CANCELLATION(99, "AUTO_CANCELLATION");

    private final int code;
    private final String reason;
}
