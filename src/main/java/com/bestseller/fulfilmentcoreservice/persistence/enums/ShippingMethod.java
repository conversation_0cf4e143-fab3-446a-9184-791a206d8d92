package com.bestseller.fulfilmentcoreservice.persistence.enums;

public enum ShippingMethod {
    VIRTUAL,
    STANDARD,
    EXPRESS;

    public static ShippingMethod getShippingMethod(String shippingMethod) {
        if (shippingMethod == null) {
            return null;
        }
        try {
            return ShippingMethod.valueOf(shippingMethod.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
