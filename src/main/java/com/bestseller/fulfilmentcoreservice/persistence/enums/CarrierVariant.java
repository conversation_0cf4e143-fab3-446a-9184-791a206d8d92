package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Optional;
import java.util.stream.Stream;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum CarrierVariant {

    HOME(false),
    PICKUP(true),
    CC(false),
    EMPLOYEE(false),
    LOCKER(true),
    POST_OFFICE(true);

    private final boolean parcelShop;

    public static Optional<CarrierVariant> findByName(String carrierVariantName) {
        return Stream.of(values())
            .filter(carrierVariant -> carrierVariant.name()
                .equalsIgnoreCase(carrierVariantName))
            .findFirst();
    }
}
