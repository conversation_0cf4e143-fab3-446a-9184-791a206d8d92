package com.bestseller.fulfilmentcoreservice.persistence.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * Describe the type of the Parcel shop within the OMS.
 */
public enum ParcelShopType {

    NONE("NONE"),

    HERMES("HERMES"),

    KIALA("K<PERSON>LA"),

    BRING("BRING"),

    GLS("GLS"),

    DHL("DHL"),

    UPS("UPS"),

    POST_DK("POST_DK"),

    DHL_PARCEL_SHOP("DHL_SP"),

    POST_NL("POST_NL"),

    B_POST("B_POST"),

    POST_NORD("POST_NORD"),

    BRING<PERSON><PERSON><PERSON>("BRING<PERSON>OLL"),

    CORREOS("CORREOS"),

    COLISSIMO("COLISSIMO"),

    SWISSPOSTPRIO("SWISSPOSTPRIO"),

    BPOSTAPI("BPOSTAPI"),

    INPOST("INPOST");

    private static final Map<String, ParcelShopType> LOOKUP = new HashMap<>();

    private final String typeName;

    /**
     * Parcel shop type.
     *
     * @param typeName Name of the parcel shop type
     */
    ParcelShopType(String typeName) {
        this.typeName = typeName;
    }

    /**
     * Get type name.
     *
     * @return Name of the parcel shop type
     */
    public String getTypeName() {
        return typeName;
    }

    static {
        // Initialize lookup table
        for (ParcelShopType nextShopType : EnumSet.allOf(ParcelShopType.class)) {
            LOOKUP.put(nextShopType.getTypeName(), nextShopType);
        }
    }

    /**
     * Lookup the Parcel shop type enumeration.
     *
     * @param parcelShopType Parcel shop type POJO
     * @return Parcel shop type enumeration
     */
    public static ParcelShopType lookup(ParcelShopType parcelShopType) {
        return LOOKUP.get(parcelShopType.getTypeName());
    }
}
