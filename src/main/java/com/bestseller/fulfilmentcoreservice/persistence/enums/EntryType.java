package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum EntryType {

    INVOICE_PAYMENT_FEE("Fee for PSP services"),
    LINE("A regular line that contains order items"),
    RETURN_FEE("Fee for returning the order"),
    RETURN_GOGW("Refund for return shipping cost"),
    SHIPMENT_FEE("Fee for shipping the order");

    private final String description;

}
