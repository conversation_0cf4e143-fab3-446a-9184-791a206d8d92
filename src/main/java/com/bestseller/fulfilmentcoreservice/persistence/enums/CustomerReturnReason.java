package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CustomerReturnReason {

    MULTIPLE_PURCHASE_1(1, "I ordered multiple sizes to try", false),
    CHANGED_OF_MIND_2(2, "I have changed my mind", false),
    WRONG_OR_FAULTY_3(3, "The item is wrong/faulty", true),
    DOESNT_FIT_4(4, "It doesn’t fit me well", true),
    DIFFERENT_MATERIAL_5(5, "Material differs from what was expected", true),
    TOO_BIG_6(6, "It is too big", true),
    TOO_LONG_7(7, "It is too long", false),
    TOO_SMALL_8(8, "It is too small", false),
    TOO_SHORT_9(9, "It is too short", true),
    DIFFERS_FROM_IMAGE_10(10, "It differs from the photograph", false),
    ITEM_IS_TOO_SMALL_11(11, "The item is too small", false),
    ITEM_IS_TOO_BIG_12(12, "The item is too big", false),
    ITEM_IS_FAULTY_13(13, "The item is faulty, missing button, hole", true),
    ITEM_IS_NOT_ON_ORDER_14(14, "The item is not on my order", true),
    ITEM_WAS_DAMAGED_IN_TRANSPORTATION_15(15, "The item was damaged in transportation", true),
    ITEM_WAS_NOT_IN_THE_PARCEL_16(16, "The item was not in the parcel", true),
    CHANGED_MIND_17(17, "I have changed my mind, I do not want this item", false),
    ITEM_DID_NOT_MEET_EXPECTATIONS_18(18, "The item did not meet my expectations", false),
    OTHER_REASONS_19(19, "Other reasons", true),
    REASON_NOT_STATED_20(20, "No return reason stated", false),
    LOST_ITEM_21(21, "Lost Item  (this is a code that customer services would use, rather than the customer)", true),
    MANUAL_REFUND_RETURN_FEE_22(22, "Manual refund with return fee", false),
    MANUAL_REFUND_NO_RETURN_FEE_23(23, "Manual refund without return fee", true),
    MULTIPLE_ORDERS_IN_ONE_PARCEL_24(24, "Multiple orders in one parcel without return fee.", true),
    REFUSED_TO_ACCEPT_THE_PARCEL_31(31, "Refused to accept the parcel", true),
    UNDELIVERABILITY_32(32, "Undeliverability", true),
    TRANSPORT_DAMAGE_33(33, "Transport damage", true),
    LOST_OUTGOING_SHIPMENT_34(34, "Lost outgoing shipment (or Lost in Transit)", true),
    RETURNED_IN_STORE_35(35, "Item returned in store", false);

    private static final Map<String, CustomerReturnReason> ENUM_MAP =
        Collections.unmodifiableMap(Stream.of(values())
            .collect(Collectors.toMap(CustomerReturnReason::name, Function.identity())));

    private final int value;
    private final String description;
    private final boolean fulfilmentIssueReason;

    public static Optional<CustomerReturnReason> findByName(String name) {
        return Optional.ofNullable(name)
            .map(String::toUpperCase)
            .map(ENUM_MAP::get);
    }

}
