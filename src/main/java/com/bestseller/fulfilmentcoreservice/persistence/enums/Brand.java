package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum Brand {
    MB("general", "MB", "Multibrand"),
    J<PERSON>("jack-jones", "JJ", "Jack & Jones"),
    MM("mama-licious", "MM", "Mamalicious"),
    NI("name-it", "NI", "Name it"),
    OC("object-collectors-item", "OC", "Object"),
    OF("outfitters-nation", "OF", "Outfitters nation"),
    ON("only", "ON", "Only"),
    PC("pieces", "PC", "Pieces"),
    SL("selected", "SL", "Selected"),
    VL("vila", "VL", "Vila"),
    VM("vero-moda", "VM", "Vero Moda"),
    JL("jlindeberg", "J<PERSON>", "<PERSON><PERSON>"),
    <PERSON>("junarose", "<PERSON>", "Junarose"),
    GV(null, null, "All gift-vouchers"),
    BC("bestseller-com", "BC", "Bestseller"),
    LP("little-pieces", "LP", "Little Pieces"),
    YS("yas", "YS", "Yas"),
    NM("noisy-may", "NM", "Noisy"),
    OS("only-and-sons", "OS", "Only & sons"),
    AD("adpt", "AD", "Adapt"),
    PT("produkt", "PT", "Produkt"),
    BI("bianco", "BI", "Bianco"),
    OH("outfit-hustlers", null, "Outfit Hustlers"),
    JX("jjxx", "JX", "JJXX"),
    IQ("iiqual", "IQ", "IIQUAL"),
    TF("thefounded", "TF", "The Founded"),
    LA("lilatelier", "LA", "Lil Atelier"),
    RD("royal-denim-division", "RD", "Royal Denim Division"),
    AP("aprel", "AP", "Aprel"),
    AN("annarr", "AN", "Annarr"),
    TS("twosoon", "TS", "TwoSoon"),
    RE("rougeedit", "RE", "Rouge Edit"),
    TB("tradebyte", "TB", "TradeByte");

    private final String demandwareName;
    private final String brandAbbreviation;
    private final String description;

    public static Brand getDefaultBrand() {
        return MB;
    }

}
