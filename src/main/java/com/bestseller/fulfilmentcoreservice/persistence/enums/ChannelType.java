package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ChannelType {

    MO<PERSON>LE("MobileCustomer"),
    STOREFRONT("DesktopCustomer");

    private final String demandwareOrderType;

    /**
     * Returns the ChannelType for the given demandwareOrderType.
     */
    public static ChannelType fromDemandwareOrderType(String demandwareType) {
        return demandwareType == null
            ? STOREFRONT
            : Stream.of(values())
            .filter(type -> type.getDemandwareOrderType().equalsIgnoreCase(demandwareType)
                || type.name().equalsIgnoreCase(demandwareType))
            .findFirst()
            .orElse(null);
    }
}
