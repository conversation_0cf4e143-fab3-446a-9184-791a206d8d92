package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

/**
 * This enum, represents the code for a geographical Market in which an order can be taken, using persistence
 * annotations to map the object to the database.
 * <p>
 * The geographical market code is represented using a <code>Sting</code>
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum Market {

    BSE_DACH("BSE-DACH", "DACH"),
    BSE_DK("BSE-DK", "Bestseller Ecommerce Denmark"),
    BSE_NL("BSE-NL", "Bestseller Ecommerce The Netherlands"),
    BSE_NORDIC("BSE-Nordic", "Nordic"),
    BSE_SOUTH("BSE-South", "South"),
    BSE_WORLD("BSE-World", "World"),
    CH("CH", "Switzerland"),
    DE("DE", "Germany"),
    DK("DK", "Denmark"),
    NL("NL", "The Netherlands"),
    NO("NO", "Norway"),
    ROE("ROE", "Rest of Europe"),
    SE("SE", "Sweden");

    private final String marketCode;
    private final String description;

    /**
     * Find the correct Market given the Market Code.
     */
    public static Market fromMarketCode(String marketCode) {
        return StringUtils.isBlank(marketCode)
            ? null
            : Stream.of(values())
            .filter(market -> market.getMarketCode().equalsIgnoreCase(marketCode))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("No market found for market code: " + marketCode));
    }

}
