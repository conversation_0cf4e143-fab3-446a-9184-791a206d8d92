package com.bestseller.fulfilmentcoreservice.persistence.dto;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Describe the change of the state of an order line quantity status. EAN refers to a order line of the order.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ToString
public class OrderStatusUpdateStateChange extends StateChange {
    private OrderStatusUpdateInfo orderStatusUpdateInfo;
}
