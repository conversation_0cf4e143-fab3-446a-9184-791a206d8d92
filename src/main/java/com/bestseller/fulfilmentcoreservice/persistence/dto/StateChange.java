package com.bestseller.fulfilmentcoreservice.persistence.dto;

import com.logistics.statetransition.OrderState;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Describe the change of the state of an order line quantity status. EAN refers to a order line of the order.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode
@ToString
public class StateChange {
    private String ean;

    private OrderState orderState;
}
