package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderStateUtils;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.logistics.statetransition.OrderState;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface OrderRepository extends JpaRepository<Order, String>, PagingAndSortingRepository<Order, String> {
    @Query(
        nativeQuery = true,
        value = """
                SELECT DISTINCT o.orderId
                   FROM Orders o
                   JOIN OrderLine ol ON o.orderId = ol.orderId
                   JOIN OrderLineQtyStatus qs ON ol.id = qs.orderLineId
                   WHERE o.orderDate >= NOW() - INTERVAL 365 DAY
                     AND o.offlinePayment = FALSE
                     AND qs.orderState = 'RETURNED'
            """)
    List<String> findAllReturnableOrderIds();

    List<OrderIdOnly> findAllByMinStatusInAndIsFinalized(Set<OrderState> minStatuses,
                                                         boolean isFinalized,
                                                         Pageable pageable);

    default List<OrderIdOnly> findOrdersToBeFinalized(int pageIndex, int pageSize) {
        return findAllByMinStatusInAndIsFinalized(
            OrderStateUtils.getGreaterThanOrEqualsStates(OrderState.DISPATCHED),
            false,
            PageRequest.of(pageIndex, pageSize, Sort.by("orderDate")));
    }

    @Query("SELECT minStatus from Order where orderId = :orderId")
    String getMinOrderStatus(String orderId);

    @Query("SELECT orderPaymentAuthorised from Order where orderId = :orderId")
    Boolean isPaymentAuthorised(String orderId);

    @Query("SELECT o FROM Order o LEFT JOIN FETCH o.orderLines WHERE o.orderId = :orderId")
    Optional<Order> findByIdWithOrderLines(@Param("orderId") String orderId);

    @Query(
        nativeQuery = true,
        value = """
                    SELECT
                        qty.orderState
                    from
                        OrderLine ol
                    INNER JOIN
                            OrderLineQtyStatus qty ON qty.orderLineId = ol.id
                    WHERE
                        ol.orderId = :orderId
                        AND ol.ean = :ean
            """)
    List<String> getOrderStateByOrderId(@Param("orderId") String orderId, @Param("ean") String ean);
}
