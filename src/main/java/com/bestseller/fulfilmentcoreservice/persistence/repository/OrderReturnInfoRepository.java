package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderReturnInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

@Repository
public interface OrderReturnInfoRepository extends JpaRepository<OrderReturnInfo, String> {

    @Modifying
    @Transactional
    @Query(value = """
        INSERT INTO OrderReturnInfo (orderId, latestReturnDate, createdTS, lastModifiedTS, version)
        VALUES (:orderId, :returnDate, NOW(), NOW(), 0)
        ON DUPLICATE KEY UPDATE
            latestReturnDate = GREATEST(latestReturnDate, VALUES(latestReturnDate)),
            lastModifiedTS = NOW(),
            version = version + 1
        """,
        nativeQuery = true
    )
    void upsertLatestReturnDate(String orderId, LocalDate returnDate);

}
