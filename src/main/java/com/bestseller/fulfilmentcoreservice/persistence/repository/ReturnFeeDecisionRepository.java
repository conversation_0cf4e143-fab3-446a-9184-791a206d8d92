package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.persistence.entity.ReturnFeeDecision;
import com.bestseller.fulfilmentcoreservice.persistence.entity.ReturnFeeDecisionId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReturnFeeDecisionRepository extends JpaRepository<ReturnFeeDecision, ReturnFeeDecisionId> {
}
