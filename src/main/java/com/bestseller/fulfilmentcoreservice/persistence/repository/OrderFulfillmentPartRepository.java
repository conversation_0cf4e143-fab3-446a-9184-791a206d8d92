package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderFulfillmentPartRepository extends JpaRepository<OrderFulfillmentPart, Integer> {
    List<OrderFulfillmentPart> findOrderFulfillmentPartByOrderOrderId(String orderId);

    List<OrderFulfillmentPart> findOrderFulfillmentPartByOrderOrderIdAndFulfillmentNode(String orderId,
                                                                                        String fulfillmentNode);
}
