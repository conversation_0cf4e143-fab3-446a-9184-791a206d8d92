package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.UUID;

public interface OrderStatusUpdateInfoRepository extends JpaRepository<OrderStatusUpdateInfo, Integer> {
    OrderStatusUpdateInfo findOrderStatusUpdateInfoByOrder(Order order);

    List<OrderStatusUpdateInfo> findAllByOrder(Order order);

    @Query("SELECT DISTINCT o.customerReturnReason "
        + "FROM OrderStatusUpdateInfo o"
        + " WHERE o.refundRequestId = :refundRequestId")
    List<CustomerReturnReason> findAllCustomerReturnReasonCodesByRefundRequestId(UUID refundRequestId);
}
