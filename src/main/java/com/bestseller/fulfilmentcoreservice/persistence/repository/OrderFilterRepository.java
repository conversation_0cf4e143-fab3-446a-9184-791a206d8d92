package com.bestseller.fulfilmentcoreservice.persistence.repository;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFilter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface OrderFilterRepository extends JpaRepository<OrderFilter, Integer> {

    Optional<OrderFilter> findByOrderId(String orderId);

    boolean existsByOrderId(String orderId);

}
