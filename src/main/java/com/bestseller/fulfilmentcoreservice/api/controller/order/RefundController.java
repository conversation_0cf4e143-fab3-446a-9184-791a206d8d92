package com.bestseller.fulfilmentcoreservice.api.controller.order;

import com.bestseller.fulfilmentcoreservice.api.dto.ReturnReasonCodesResponse;
import com.bestseller.fulfilmentcoreservice.core.service.OrderRefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/refunds")
public class RefundController {
    private final OrderRefundService orderRefundService;

    @GetMapping("{id}/reason-codes")
    public ReturnReasonCodesResponse getReturnReasonCodes(@PathVariable("id") String refundRequestId) {
        log.info("Incoming request to fetch return reason codes for refund requests with id: {}", refundRequestId);
        return orderRefundService.getReturnReasonCodes(refundRequestId);
    }
}
