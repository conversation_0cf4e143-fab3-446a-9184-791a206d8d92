package com.bestseller.fulfilmentcoreservice.api.controller.order;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.api.dto.RefundOptionsResponse;
import com.bestseller.fulfilmentcoreservice.core.service.OrderBlockService;
import com.bestseller.fulfilmentcoreservice.core.service.OrderRefundService;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.service.OrderStatusTradeByteExportService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static org.springframework.http.HttpStatus.NOT_FOUND;

@Slf4j
@RequestMapping("/orders")
@RestController
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;
    private final OrderBlockService orderBlockService;
    private final OrderRefundService orderRefundService;
    private final OrderStatusTradeByteExportService orderStatusTradeByteExportService;

    @GetMapping("/{id}")
    public OrderView getOrder(@PathVariable("id") String orderId) {
        log.info("Incoming request to get order with orderId = {}", orderId);
        return orderService.findOrder(orderId)
            .orElseThrow(() -> new ResponseStatusException(NOT_FOUND, "Requested object not found"));
    }

    @PutMapping("/{id}/block")
    @Operation(summary = "Request an order block")
    public void requestBlockToAnOrder(@PathVariable("id") String orderId) {
        log.info("Incoming request to block the order with id = {}", orderId);
        orderBlockService.block(orderId);
    }

    @PutMapping("/{id}/unblock")
    @Operation(summary = "Request an order unblock")
    public void requestUnblockToAnOrder(@PathVariable("id") String orderId) {
        log.info("Incoming request to unblock the order with id = {}", orderId);
        orderBlockService.unblock(orderId);
    }

    @PutMapping("/{id}/update-email")
    public void updateEmailAddress(@PathVariable("id") String orderId, @RequestParam String emailAddress) {
        log.info("Incoming update email address request for orderId = {}", orderId);
        orderService.updateEmailAddress(orderId, emailAddress);
    }

    @GetMapping("{id}/refund-options")
    public RefundOptionsResponse getRefundOptionsByOrderId(@PathVariable("id") String orderId) {
        return orderRefundService.getInStoreRefundOptions(orderId);
    }

    @PostMapping("export-statuses-to-tradebyte")
    public void exportStatusesToTradebyte(@RequestBody List<String> orderIds) {
        orderStatusTradeByteExportService.export(orderIds);
    }

    @GetMapping("/{id}/validate-cancellation")
    @Operation(summary = "Validate if order can be cancelled")
    public boolean validateIfOrderCanBeCancelled(@PathVariable("id") String orderId) {
        log.info("Validating if order {} can be cancelled", orderId);
        return orderService.validateIfOrderCanBeCancelled(orderId);
    }
}
