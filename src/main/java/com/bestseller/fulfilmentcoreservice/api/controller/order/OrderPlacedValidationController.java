package com.bestseller.fulfilmentcoreservice.api.controller.order;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderPlacedValidationView;
import com.bestseller.fulfilmentcoreservice.core.exception.ValidationException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderPlacedValidationService;
import com.bestseller.fulfilmentcoreservice.core.service.ValidationService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequestMapping("/order-placed")
@RestController
@RequiredArgsConstructor
public class OrderPlacedValidationController {

    private final OrderPlacedValidationService orderPlacedValidationService;
    private final ValidationService validationService;

    /**
     * Request an order placed validation.
     *
     * @param orderPlaced order placed
     */
    @PostMapping("/validate")
    @Operation(summary = "Request an order placed validation")
    public OrderPlacedValidationView validateOrderPlaced(@RequestBody OrderPlaced orderPlaced) {
        log.info("Incoming request to validate order placed message with id = {}", orderPlaced.getOrderId());
        try {
            orderPlacedValidationService.validate(orderPlaced);
        } catch (ConstraintViolationException ex) {
            Map<String, String> errors = new HashMap<>();
            log.warn(
                "Incoming request to validate order placed message faced ConstraintViolationException with id={}, "
                    + "message={}",
                orderPlaced.getOrderId(),
                ex.getMessage()
            );
            var validationErrors = validationService.getValidationErrors(orderPlaced);
            validationErrors.forEach(error ->
                errors.put(error.getPropertyPath().toString(), error.getMessage()));
            return OrderPlacedValidationView
                .builder()
                .isSuccess(Boolean.FALSE)
                .errorMessages(errors.entrySet().stream()
                    .map(error -> error.getKey().concat(" ")
                        .concat(error.getValue()))
                    .toList())
                .build();

        } catch (ValidationException ex) {
            log.warn(
                "Incoming request to validate order placed message faced ValidationException with id={}, message={}",
                orderPlaced.getOrderId(),
                ex.getMessage()
            );
            return OrderPlacedValidationView
                .builder()
                .isSuccess(Boolean.FALSE)
                .errorMessages(List.of(ex.getMessage()))
                .build();
        }
        return OrderPlacedValidationView.builder().isSuccess(Boolean.TRUE).build();
    }
}
