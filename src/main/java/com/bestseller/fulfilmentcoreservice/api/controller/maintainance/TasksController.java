package com.bestseller.fulfilmentcoreservice.api.controller.maintainance;

import com.bestseller.fulfilmentcoreservice.core.task.OrderFinalizerTask;
import com.bestseller.fulfilmentcoreservice.core.task.ReturnProcessorTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/tasks")
@RestController
@RequiredArgsConstructor
public class TasksController {

    private final OrderFinalizerTask orderFinalizerTask;
    private final ReturnProcessorTask returnProcessorTask;

    @PostMapping("/finalize-orders")
    public void finalizeOrders() {
        log.info("Incoming request to finalize orders");
        orderFinalizerTask.run();
    }

    @PostMapping("/process-returns")
    public void processReturns() {
        log.info("Incoming request to process returns");
        returnProcessorTask.processReturn();
    }
}
