package com.bestseller.fulfilmentcoreservice.api.dto;

import lombok.Builder;

import java.time.ZonedDateTime;
import java.util.List;

@Builder
public record OrderView(
    String id,
    ZonedDateTime orderDate,
    ZonedDateTime createdAt,
    ZonedDateTime lastUpdate,
    int version,
    String checkout,
    String market,
    String store,
    String shippingMethod,
    Integer minStatus,
    Integer maxStatus,
    AddressView shippingAddress,
    AddressView billingAddress,
    CustomerView customer,
    String brand,
    String brandAbbreviation,
    String currency,
    List<ShipmentView> shipments,
    List<OrderLineView> orderLines,
    OrderBlockView orderBlock,
    OrderCancelView orderCancel,
    String isoStoreId,
    String channelType,
    PartnerChannelView partnerChannel) {
}
