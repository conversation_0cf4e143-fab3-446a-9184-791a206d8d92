package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;

public class OrderLineReturnedQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderLineReturned> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";

    public OrderLineReturnedQueueConsumer(
        IdempotencyChecker<OrderLineReturned> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderLineReturned> taskPayloadTransformer,
        MessageConsumerService<OrderLineReturned> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderLineReturned message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }
}
