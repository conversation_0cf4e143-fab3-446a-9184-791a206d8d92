package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderForFulfilmentProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<OrderForFulfillment> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderForFulfilmentProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderForFulfilmentProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderForFulfillment> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderForFulfillment> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderForFulfillment message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
