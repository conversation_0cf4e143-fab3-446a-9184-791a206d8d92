package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;

public class OrderPartsCancelledQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderPartsCancelled> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, orderLines=%s";

    public OrderPartsCancelledQueueConsumer(
        IdempotencyChecker<OrderPartsCancelled> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderPartsCancelled> taskPayloadTransformer,
        MessageConsumerService<OrderPartsCancelled> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderPartsCancelled message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getOrderLines());
    }
}
