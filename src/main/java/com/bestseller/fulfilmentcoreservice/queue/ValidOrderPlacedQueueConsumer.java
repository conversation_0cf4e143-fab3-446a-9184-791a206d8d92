package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.AbstractQueueConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ValidOrderPlacedQueueConsumer extends AbstractQueueConsumer<ValidOrderPlaced> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    private final MessageConsumerService<ValidOrderPlaced> messageConsumerService;

    public ValidOrderPlacedQueueConsumer(
        IdempotencyChecker<ValidOrderPlaced> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> taskPayloadTransformer,
        MessageConsumerService<ValidOrderPlaced> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer);
        this.messageConsumerService = messageConsumerService;
    }

    @Override
    protected String getMessageDetails(ValidOrderPlaced message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    protected void consume(ValidOrderPlaced validOrderPlaced) {
        messageConsumerService.process(validOrderPlaced);
    }
}
