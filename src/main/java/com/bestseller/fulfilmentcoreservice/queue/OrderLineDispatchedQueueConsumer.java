package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;

public class OrderLineDispatchedQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderLineDispatched> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";

    public OrderLineDispatchedQueueConsumer(
        IdempotencyChecker<OrderLineDispatched> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderLineDispatched> taskPayloadTransformer,
        MessageConsumerService<OrderLineDispatched> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderLineDispatched message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }
}
