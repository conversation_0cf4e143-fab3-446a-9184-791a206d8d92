package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RefundCreationRequestedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<RefundCreationRequested> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundCreationRequestedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundCreationRequestedId=%s";

    public RefundCreationRequestedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<RefundCreationRequested> taskPayloadTransformer,
        AbstractKafkaMessageProducer<RefundCreationRequested> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(RefundCreationRequested message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getRefundCreationRequestedId());
    }
}
