package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;

public class GiftCardPurchasedQueueConsumer extends AbstractMessageProcessorQueueConsumer<GiftCardPurchased> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, giftCardNumber=%s";

    public GiftCardPurchasedQueueConsumer(
        IdempotencyChecker<GiftCardPurchased> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<GiftCardPurchased> taskPayloadTransformer,
        MessageConsumerService<GiftCardPurchased> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(GiftCardPurchased message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getGiftCardNumber());
    }
}
