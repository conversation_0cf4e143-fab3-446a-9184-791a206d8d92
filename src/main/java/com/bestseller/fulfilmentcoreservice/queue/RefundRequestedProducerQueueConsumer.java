package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RefundRequestedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<OrderReturnedInStore> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundRequestedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s";

    public RefundRequestedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderReturnedInStore> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderReturnedInStore message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getReturnId());
    }
}
