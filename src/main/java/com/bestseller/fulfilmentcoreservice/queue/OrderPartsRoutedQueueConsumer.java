package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;

public class OrderPartsRoutedQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderPartsRouted> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, orderLines=%s, fulfillmentNode=%s";

    public OrderPartsRoutedQueueConsumer(
        IdempotencyChecker<OrderPartsRouted> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderPartsRouted> taskPayloadTransformer,
        MessageConsumerService<OrderPartsRouted> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderPartsRouted message) {
        return MESSAGE_DETAILS_TEMPLATE
            .formatted(message.getOrderId(), message.getOrderLines(), message.getFulfillmentNode());
    }
}
