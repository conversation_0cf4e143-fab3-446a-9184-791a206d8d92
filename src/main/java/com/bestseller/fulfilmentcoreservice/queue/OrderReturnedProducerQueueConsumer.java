package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderReturnedProducerQueueConsumer extends AbstractKafkaMessageProducerQueueConsumer<OrderReturned> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderReturnedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderReturnedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderReturned> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderReturned> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderReturned message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
