package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;

public class OrderReturnedInStoreQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderReturnedInStore> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s";

    public OrderReturnedInStoreQueueConsumer(
        IdempotencyChecker<OrderReturnedInStore> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderReturnedInStore> taskPayloadTransformer,
        MessageConsumerService<OrderReturnedInStore> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderReturnedInStore message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getReturnId());
    }
}
