package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.AbstractQueueConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractMessageProcessorQueueConsumer<T> extends AbstractQueueConsumer<T> {

    private final MessageConsumerService<T> messageConsumerService;

    protected AbstractMessageProcessorQueueConsumer(
        IdempotencyChecker<T> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<T> taskPayloadTransformer,
        MessageConsumerService<T> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer);
        this.messageConsumerService = messageConsumerService;
    }

    @Override
    protected final void consume(T message) {
        messageConsumerService.process(message);
    }
}
