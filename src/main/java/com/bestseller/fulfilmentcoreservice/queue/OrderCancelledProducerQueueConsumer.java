package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderCancelledProducerQueueConsumer extends AbstractKafkaMessageProducerQueueConsumer<OrderCancelled> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCancelledProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderCancelledProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderCancelled> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderCancelled> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderCancelled message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
