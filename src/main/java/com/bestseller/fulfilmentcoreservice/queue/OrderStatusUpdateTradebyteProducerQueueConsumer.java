package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderStatusUpdateTradebyteProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<OrderStatusUpdateTradebyte> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatusUpdateTradebyteProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderStatusUpdateTradebyteProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderStatusUpdateTradebyte> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderStatusUpdateTradebyte> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderStatusUpdateTradebyte message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
