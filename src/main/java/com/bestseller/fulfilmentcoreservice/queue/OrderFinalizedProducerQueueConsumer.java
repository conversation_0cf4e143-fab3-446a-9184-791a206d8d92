package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderFinalizedProducerQueueConsumer extends AbstractKafkaMessageProducerQueueConsumer<OrderFinalized> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderFinalizedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderFinalizedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderFinalized> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderFinalized> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderFinalized message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
