package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderStatusUpdatedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<OrderStatusUpdated> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatusUpdatedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, type=%s";

    public OrderStatusUpdatedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderStatusUpdated> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderStatusUpdated> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getType());
    }
}
