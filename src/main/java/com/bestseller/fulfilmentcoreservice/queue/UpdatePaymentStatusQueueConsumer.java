package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;

public class UpdatePaymentStatusQueueConsumer extends AbstractMessageProcessorQueueConsumer<PaymentStatusUpdated> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, paymentState=%s";

    public UpdatePaymentStatusQueueConsumer(
        IdempotencyChecker<PaymentStatusUpdated> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> taskPayloadTransformer,
        MessageConsumerService<PaymentStatusUpdated> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(PaymentStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getPaymentState());
    }
}
