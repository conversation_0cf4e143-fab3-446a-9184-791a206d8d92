package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;

public class OrderLineExportedQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderLineExported> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";

    public OrderLineExportedQueueConsumer(
        IdempotencyChecker<OrderLineExported> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderLineExported> taskPayloadTransformer,
        MessageConsumerService<OrderLineExported> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderLineExported message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }
}
