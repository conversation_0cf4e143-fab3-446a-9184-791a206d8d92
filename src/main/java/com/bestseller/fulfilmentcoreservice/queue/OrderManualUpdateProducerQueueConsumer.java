package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderManualUpdateProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<OrderManualUpdate> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderManualUpdateProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    public OrderManualUpdateProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderManualUpdate> taskPayloadTransformer,
        AbstractKafkaMessageProducer<OrderManualUpdate> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(OrderManualUpdate message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }
}
