package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;

public class OrderLineAcknowledgedQueueConsumer extends AbstractMessageProcessorQueueConsumer<OrderLineAcknowledged> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, ean=%s";

    public OrderLineAcknowledgedQueueConsumer(
        IdempotencyChecker<OrderLineAcknowledged> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderLineAcknowledged> taskPayloadTransformer,
        MessageConsumerService<OrderLineAcknowledged> messageConsumerService) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer, messageConsumerService);
    }

    @Override
    protected String getMessageDetails(OrderLineAcknowledged message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getEan());
    }
}
