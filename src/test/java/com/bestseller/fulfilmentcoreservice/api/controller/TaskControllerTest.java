package com.bestseller.fulfilmentcoreservice.api.controller;

import com.bestseller.fulfilmentcoreservice.api.controller.maintainance.TasksController;
import com.bestseller.fulfilmentcoreservice.core.task.OrderFinalizerTask;
import com.bestseller.fulfilmentcoreservice.core.task.ReturnProcessorTask;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class TaskControllerTest {

    @InjectMocks
    private TasksController taskController;

    @Mock
    private OrderFinalizerTask orderFinalizerTask;

    @Mock
    private ReturnProcessorTask returnProcessorTask;

    @Test
    void finalizeOrders() {
        // arrange

        // act
        taskController.finalizeOrders();

        // assert
        verify(orderFinalizerTask).run();
    }

    @Test
    void processReturns() {
        // arrange

        // act
        taskController.processReturns();

        // assert
        verify(returnProcessorTask).processReturn();
    }
}
