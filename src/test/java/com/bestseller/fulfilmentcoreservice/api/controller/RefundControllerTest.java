package com.bestseller.fulfilmentcoreservice.api.controller;

import com.bestseller.fulfilmentcoreservice.api.controller.order.RefundController;
import com.bestseller.fulfilmentcoreservice.core.service.OrderRefundService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundControllerTest {
    @Mock
    private OrderRefundService orderRefundService;

    @InjectMocks
    private RefundController refundController;

    @Test
    void getReturnReasonCodes_givenValidOrder_requiredServiceIsCalled() {
        // arrange
        String refundRequestId = "TB612678123";
        when(orderRefundService.getReturnReasonCodes(refundRequestId)).thenReturn(null);

        //act
        refundController.getReturnReasonCodes(refundRequestId);
        // assert
        verify(orderRefundService).getReturnReasonCodes(refundRequestId);
    }
}
