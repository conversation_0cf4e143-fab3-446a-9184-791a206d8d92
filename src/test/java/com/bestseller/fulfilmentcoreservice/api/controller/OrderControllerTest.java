package com.bestseller.fulfilmentcoreservice.api.controller;

import com.bestseller.fulfilmentcoreservice.api.controller.order.OrderController;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.api.dto.RefundOptionsResponse;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.OrderRefundService;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.service.OrderStatusTradeByteExportService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderControllerTest {

    @InjectMocks
    private OrderController orderController;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderRefundService orderRefundService;

    @Mock
    private OrderStatusTradeByteExportService orderStatusTradeByteExportService;

    @Test
    void produce_givenOrderStatusUpdated_producesMessage() {
        // arrange
        var tbOrderId = "TB".concat("612678123");
        var orderView = OrderView.builder().id(tbOrderId).build();
        when(orderService.findOrder(anyString())).thenReturn(Optional.of(orderView));
        // act
        // no exception thrown
        assertDoesNotThrow(() -> orderController.getOrder(tbOrderId));
    }

    @Test
    void produce_givenOrderStatusUpdated_thrownException() {
        // arrange
        var tbOrderId = "TB".concat("612678123");
        when(orderService.findOrder(anyString())).thenReturn(Optional.empty());
        // act
        // no exception thrown
        assertThrows(ResponseStatusException.class,
            () -> orderController.getOrder(tbOrderId));
    }

    @Test
    void refundOptions_givenValidOrder_requiredServiceIsCalled() {
        // arrange
        String orderId = "TB612678123";
        when(orderRefundService.getInStoreRefundOptions(orderId))
            .thenReturn(RefundOptionsResponse.builder()
            .refundShippingFee(true)
            .chargeReturnFee(false)
                .build());
        // act
        orderController.getRefundOptionsByOrderId(orderId);

        // assert
        verify(orderRefundService).getInStoreRefundOptions(orderId);
    }

    @Test
    void refundOptions_givenInvalidOrder_throwsException() {
        // arrange
        String orderId = "TB612678";
        when(orderRefundService.getInStoreRefundOptions(orderId)).thenThrow(new OrderNotFoundException(orderId));
        // act and assert
        assertThrows(OrderNotFoundException.class,
            () -> orderController.getRefundOptionsByOrderId(orderId));
    }

    @Test
    void exportStatusesToTradebyte_givenValidOrder_requiredServiceIsCalled() {
        // arrange
        List<String> orderIds = List.of("TB612678123", "TB612678124");

        // act
        orderController.exportStatusesToTradebyte(orderIds);

        // assert
        verify(orderStatusTradeByteExportService).export(orderIds);
    }

    @Test
    void validateIfOrderCanBeCancelled_givenValidOrderId_returnsTrue() {
        // arrange
        String orderId = "TB612678123";
        when(orderService.validateIfOrderCanBeCancelled(orderId)).thenReturn(true);

        // act
        boolean canBeCancelled = orderController.validateIfOrderCanBeCancelled(orderId);

        // assert
        assertThat(canBeCancelled).isTrue();
    }
}
