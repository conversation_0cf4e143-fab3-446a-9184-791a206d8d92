package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import com.bestseller.fulfilmentcoreservice.core.model.CustomerModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.fulfilmentcoreservice.core.model.ValidOrderPlacedOrderLineModel;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import com.logistics.statetransition.Platform;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Random;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.STANDARD;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderModelGenerator {

    public static final LocalDate ANNOUNCED_DELIVERY_DATE = LocalDate.parse("2024-02-17");

    public static OrderModel generateOrderModel(String orderId) {
        return OrderModel.builder()
            .orderId(orderId)
            .orderDate(ZonedDateTime.now())
            .checkoutScopeId("C687BD65-EFE0-4ADB-AEBB-85D202C4BF50")
            .market("BSE-DACH")
            .platform(Platform.DEMANDWARE.name())
            .channel(ChannelType.MOBILE.getDemandwareOrderType())
            .shippingMethod(STANDARD.name())
            .externalOrderId("EX%s".formatted(orderId))
            .test(new Random().nextBoolean())
            .billToMatchesShipTo(new Random().nextBoolean())
            .shippingAddress(new AddressModel())
            .billingAddress(new AddressModel())
            .customer(new CustomerModel())
            .brandedShipping("JJ")
            .orderLines(List.of(ValidOrderPlacedOrderLineModel.builder().build(),
                ValidOrderPlacedOrderLineModel.builder().build()))
            .announcedDeliveryDate(ANNOUNCED_DELIVERY_DATE)
            .offlinePayment(true)
            .build();
    }

}
