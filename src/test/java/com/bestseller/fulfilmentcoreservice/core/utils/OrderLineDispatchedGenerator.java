package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@SuppressWarnings("all")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderLineDispatchedGenerator {

    public static final String EAN = "1234567891023";
    public static final int QUANTITY = 2;
    public static final String WAREHOUSE = "WAREHOUSE";
    public static final ZonedDateTime ORANGE_PRINTED_DATE = ZonedDateTime.now();
    public static final ZonedDateTime DISPATCHED_DATE = ZonedDateTime.now();


    public static OrderLineDispatched generate(String orderId) {
        return new OrderLineDispatched()
            .withOrderId(orderId)
            .withOrangePrinted(ORANGE_PRINTED_DATE)
            .withDispatchDate(DISPATCHED_DATE)
            .withWarehouse(WAREHOUSE)
            .withQuantity(QUANTITY)
            .withEan(EAN);
    }
}
