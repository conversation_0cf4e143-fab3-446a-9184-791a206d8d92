package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.ReturnItem;

import java.math.BigDecimal;
import java.util.List;

public class OrderReturnedInStoreGenerator {
    public static final String ORDER_LINE_EAN_1 = "ean1";
    public static final String ORDER_LINE_EAN_2 = "ean2";

    public static final String ORDER_ID = "OL1001628107";

    private static final long RETURN_ID = 147273L;

    private static final BigDecimal TOTAL_UPSELL_DISCOUNT_AMOUNT = BigDecimal.valueOf(0.0);

    private static final BigDecimal GROSS_PRICE = BigDecimal.valueOf(249.95);

    private static final BigDecimal DISCOUNT_AMOUNT = BigDecimal.valueOf(0.0);

    private static final int QUANTITY_1 = 1;
    private static final int QUANTITY_2 = 2;

    public static OrderReturnedInStore generate() {
        return new OrderReturnedInStore()
            .withOrderId(ORDER_ID)
            .withReturnId(RETURN_ID)
            .withTotalUpsellDiscountAmount(TOTAL_UPSELL_DISCOUNT_AMOUNT)
            .withReturnItems(List.of(new ReturnItem()
                .withGrossPrice(GROSS_PRICE)
                .withDiscountAmount(DISCOUNT_AMOUNT)
                .withOrderLine(new OrderLine()
                    .withEan(ORDER_LINE_EAN_1)
                    .withQuantity(QUANTITY_1))));
    }

    public static OrderReturnedInStore generateMultiReturnItems() {
        return new OrderReturnedInStore()
            .withOrderId(ORDER_ID)
            .withReturnId(RETURN_ID)
            .withTotalUpsellDiscountAmount(TOTAL_UPSELL_DISCOUNT_AMOUNT)
            .withReturnItems(List.of(new ReturnItem()
                .withGrossPrice(GROSS_PRICE)
                .withDiscountAmount(DISCOUNT_AMOUNT)
                .withOrderLine(new OrderLine()
                    .withEan(ORDER_LINE_EAN_1)
                    .withQuantity(QUANTITY_1)),
                new ReturnItem()
                    .withGrossPrice(GROSS_PRICE)
                    .withDiscountAmount(DISCOUNT_AMOUNT)
                    .withOrderLine(new OrderLine()
                        .withEan(ORDER_LINE_EAN_2)
                        .withQuantity(QUANTITY_2))));
    }
}
