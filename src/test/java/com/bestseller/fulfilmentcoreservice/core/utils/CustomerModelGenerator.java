package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.CustomerModel;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CustomerModelGenerator {

    public static CustomerModel generateCustomer() {
        return CustomerModel.builder()
            .customerId("CUSTOMER_ID")
            .name("NAME")
            .email("<EMAIL>")
            .employeeId("EMPLOYEE_ID")
            .type(CustomerType.EMPLOYEE.name())
            .isLoggedIn(true)
            .externalCustomerId("EXTERNAL_CUSTOMER_ID")
            .customerLocale("en-dk")
            .appVersion("version")
            .originalScopeId("originalScopeId")
            .platform("mobile")
            .build();
    }
}
