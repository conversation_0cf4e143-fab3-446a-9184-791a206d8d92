package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AddressModelGenerator {

    public static final String ADDRESS_LINE_1 = "Frögatan";

    public static final String ADDRESS_LINE_2 = "Address Line 2";

    public static final String ADDRESS_LINE_3 = "Address Line 3";
    public static final String CITY = "Solna";
    public static final String COUNTRY = "NL";
    public static final String FIRST_NAME = "First Name";
    public static final String HOUSE_NUMBER = "1";
    public static final String LAST_NAME = "Last Name";
    public static final String PHONE_NUMBER = "+31712474580";
    public static final String ZIPCODE = "3061 GG";

    public static final String CARRIER_VARIANT = "HOME";

    public static final String SALUTATION = "Mr";
    public static final String HOUSE_NUMBER_EXT = "HOME NUMBER EXTENSION";
    public static final String PHYSICAL_STORE_ID = "12";
    public static final String STATE = "BR";
    public static final String DELIVERY_OPTION = "DHL";
    public static final String PARCEL_LOCKER = "PARCEL LOCKER";

    public static AddressModel generateAddress() {
        return AddressModel.builder()
            .address1(ADDRESS_LINE_1)
            .address2(ADDRESS_LINE_2)
            .address3(ADDRESS_LINE_3)
            .city(CITY)
            .countryCode(COUNTRY)
            .firstName(FIRST_NAME)
            .houseNumber(HOUSE_NUMBER)
            .lastName(LAST_NAME)
            .phone(PHONE_NUMBER)
            .postCode(ZIPCODE)
            .salutation(SALUTATION)
            .houseNumberExt(HOUSE_NUMBER_EXT)
            .carrierVariant(CARRIER_VARIANT)
            .deliveryOption(DELIVERY_OPTION)
            .state(STATE)
            .physicalStoreId(PHYSICAL_STORE_ID)
            .parcelLocker(PARCEL_LOCKER)
            .build();
    }

}
