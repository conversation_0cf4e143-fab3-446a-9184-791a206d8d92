package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.UUID;

@SuppressWarnings("all")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderLineReturnedGenerator {

    public static final UUID ECOM_ID = UUID.fromString("8453E2B2-900D-4F47-ABAF-833E850D879A");
    public static final String OL_12233132 = "OL12233132";
    public static final String EAN = "1234567891023";
    public static final int RETURNED_QUANTITY = 10;
    public static final String WAREHOUSE = "WAREHOUSE";
    public static final ZonedDateTime EFFECTIVE_DATE = ZonedDateTime.now();


    public static OrderLineReturned generate() {
        return new OrderLineReturned()
            .withOrderId(OL_12233132)
            .withLineNumber(1)
            .withReturnType(ReturnType.CUSTOMER_RETURN.name())
            .withReturnReason(CustomerReturnReason.REASON_NOT_STATED_20.name())
            .withEan(EAN)
            .withQuantity(RETURNED_QUANTITY)
            .withPrepaidLabel("Y")
            .withEffectiveDate(EFFECTIVE_DATE)
            .withWarehouse(WAREHOUSE);
    }
}
