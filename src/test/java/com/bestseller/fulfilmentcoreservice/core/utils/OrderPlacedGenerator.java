package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.FulfillmentAdvice;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingCharge;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingInformation;
import com.logistics.statetransition.Platform;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@SuppressWarnings("magicnumber")
public final class OrderPlacedGenerator {
    private static final Integer RANDOM_NUM = 123;

    private OrderPlacedGenerator() {
    }

    /**
     * Address properties.
     */
    public static class AddressProperties {
        public static final String ADDRESS_LINE_1 = "Street 1";
        public static final String VERY_LONG_ADDRESS_LINE = "Very long line 1 of the address longer than 32 symbols.";
        public static final String ADDRESS_LINE_2 = "line2";
        public static final String ADDRESS_LINE_3 = "line3";
        public static final String CITY = "DUBLIN";
        public static final String COUNTRY = "NL";
        public static final String FIRST_NAME = "firstName";
        public static final String LAST_NAME = "lastName";
        public static final String HOUSE_NUMBER = "66";
        public static final String HOUSE_NUMBER_EXTENDED = "A";
        public static final String PHONE_NUMBER = "+3456000000";
        public static final String ZIPCODE = "1232 KL";
    }

    /**
     * CustomerInformation properties.
     */
    public static class CustomerInformationProperties {
        public static final String EMAIL_ADDRESS = "<EMAIL>";
        public static final String CUSTOMER_ID = "12344321";
        public static final String EXTERNAL_CUSTOMER_ID = "1234554321";
    }

    /**
     * OrderDetails properties.
     */
    public static class OrderDetailsProperties {
        public static final String EXTERNAL_ORDER_NUMBER = "99999999000000";
        public static final BigDecimal ORDER_VALUE = BigDecimal.valueOf(413.50);
        public static final String ORDER_TYPE = "MARKETPLACE";
        public static final String SHIPPING_METHOD = "EXPRESS";
        public static final String MARKET = "BSE-South";
        public static final String CHANNEL = "otde";
        public static final String CARRIER = "DHL";
        public static final String CARRIER_VARIANT = "HOME";
    }

    private static List<OrderLine> getOrderLines() {
        final List<OrderLine> orderLines = new ArrayList<>();
        orderLines.add(createFullOrderLine());

        final OrderLine secondOrderLine = new OrderLine()
            .withEan(SecondOrderLineProperties.EAN)
            .withExternalItemId(SecondOrderLineProperties.EXTERNAL_ITEM_ID)
            .withProductName(SecondOrderLineProperties.PRODUCT_NAME)
            .withLineNumber(SecondOrderLineProperties.LINE_NUMBER)
            .withQuantity(SecondOrderLineProperties.QUANTITY)
            .withRetailPrice(SecondOrderLineProperties.RETAIL_PRICE)
            .withDiscountValue(SecondOrderLineProperties.DISCOUNT_VALUE)
            .withPartnerReference(SecondOrderLineProperties.PARTNER_REFERENCE)
            .withVat(SecondOrderLineProperties.VAT)
            .withTax(SecondOrderLineProperties.TAX);
        orderLines.add(secondOrderLine);
        return orderLines;
    }

    /**
     * Valid Order Placed Message.
     *
     * @return Order Placed
     */
    public static OrderPlaced createBasicOrderPlacedMessage() {
        final ZonedDateTime now = dateToZoneDateTime(new Date());
        final Address billingAddress = new Address();
        billingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
            .withCity(AddressProperties.CITY)
            .withLastName(AddressProperties.LAST_NAME)
            .withFirstName(AddressProperties.FIRST_NAME)
            .withCountry(AddressProperties.COUNTRY)
            .withZipcode(AddressProperties.ZIPCODE);

        final Address shippingAddress = new Address();
        shippingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
            .withCity(AddressProperties.CITY)
            .withLastName(AddressProperties.LAST_NAME)
            .withFirstName(AddressProperties.FIRST_NAME)
            .withCountry(AddressProperties.COUNTRY)
            .withZipcode(AddressProperties.ZIPCODE);

        final CustomerInformation customerInformation = new CustomerInformation();
        customerInformation.withCustomerId(UUID.randomUUID().toString())
            .withBillingAddress(billingAddress);

        final ShippingInformation shippingInformation = new ShippingInformation();
        shippingInformation.withShippingAddress(shippingAddress);

        final OrderDetails orderDetails = new OrderDetails()
            .withExternalOrderNumber(OrderDetailsProperties.EXTERNAL_ORDER_NUMBER)
            .withOrderCreationDate(ZonedDateTime.now())
            .withOrderValue(OrderDetailsProperties.ORDER_VALUE);

        final List<OrderLine> orderLines = new ArrayList<>();
        final OrderLine orderLine = new OrderLine();
        orderLine.withEan(FirstOrderLineProperties.EAN)
            .withExternalItemId(FirstOrderLineProperties.EXTERNAL_ITEM_ID)
            .withQuantity(FirstOrderLineProperties.QUANTITY)
            .withRetailPrice(FirstOrderLineProperties.RETAIL_PRICE)
            .withVat(FirstOrderLineProperties.VAT);
        orderLines.add(orderLine);

        final OrderPlaced message = new OrderPlaced();
        message.withOrderId(OrderProperties.ORDER_ID)
            .withPlacedDate(now)
            .withStore(Platform.TRADEBYTE.name())
            .withIsTest(false)
            .withCustomerInformation(customerInformation)
            .withShippingInformation(shippingInformation)
            .withOrderDetails(orderDetails)
            .withOrderLines(orderLines);
        return message;
    }

    /**
     * converts {@link Date} to {@link ZonedDateTime} using the OS zone.
     * As OMS Tomcat is started using CEST and we do not want to create issues we get the Zone from the system.
     * if date is null returns null.
     *
     * @param date date
     * @return zone date time
     */
    public static ZonedDateTime dateToZoneDateTime(Date date) {
        ZonedDateTime zonedDateTime = null;
        if (date != null) {
            zonedDateTime = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        }
        return zonedDateTime;
    }

    /**
     * Order properties.
     */
    public static class OrderProperties {
        public static final String ORDER_ID = "orderId";
        public static final boolean IS_TEST = false;
    }

    /**
     * Fulfillment advice.
     */
    public static class FulfillmentAdviceProperties {
        public static final String FULFILLMENT_NODE = "HERMES";
    }

    public static OrderPlaced createTradeByteOrderPlacedMessage() {
        OrderPlaced orderPlacedMessage = createOrderPlacedMessage();
        orderPlacedMessage.setStore(Platform.TRADEBYTE.name());
        orderPlacedMessage.getOrderDetails().setChannel(OrderDetailsProperties.CHANNEL);
        orderPlacedMessage.getOrderDetails().setExternalOrderNumber(OrderDetailsProperties.EXTERNAL_ORDER_NUMBER);
        orderPlacedMessage.getCustomerInformation().getBillingAddress()
            .setAddressLine1(AddressProperties.VERY_LONG_ADDRESS_LINE);
        orderPlacedMessage.getCustomerInformation()
            .setExternalCustomerId(CustomerInformationProperties.EXTERNAL_CUSTOMER_ID);
        orderPlacedMessage.getShippingInformation().getShippingAddress()
            .setAddressLine1(AddressProperties.VERY_LONG_ADDRESS_LINE);
        return orderPlacedMessage;
    }

    public static OrderPlaced createPhoenixOrderPlacedMessage() {
        OrderPlaced orderPlacedMessage = createOrderPlacedMessage();
        orderPlacedMessage.setStore(Platform.DEMANDWARE.name());
        BigDecimal price = BigDecimal.valueOf(4);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0.7);
        ShippingCharge shippingCharge =
            new ShippingCharge(
                shippingName, "ean", shippingTaxRate, shippingVatAmount, price, price, price, null, null, null
            );

        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String pspReference = "**********";
        String additionalReference = null;
        String provider = "ADYEN";
        BigDecimal amount = BigDecimal.valueOf(24.95);
        String currency = "EUR";
        String state = "AUTH";
        Payment payment = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, currency, state, null, null, null);
        ArrayList<Payment> payments = new ArrayList<>();
        payments.add(payment);

        orderPlacedMessage.getShippingInformation().setShippingCharges(Collections.singletonList(shippingCharge));
        orderPlacedMessage.setPayments(payments);
        return orderPlacedMessage;
    }

    public static OrderPlaced createValidOrderPlaced() {
        String orderId = "IT_0453219";

        OrderLine orderLine = new OrderLine();
        orderLine.setEan("ean");
        orderLine.setQuantity(RANDOM_NUM);
        orderLine.setBrand("brand");
        orderLine.setRetailPrice(BigDecimal.TEN);
        orderLine.setVat(BigDecimal.ONE);

        OrderDetails orderDetails = new OrderDetails();
        orderDetails.setOrderValue(BigDecimal.ZERO);
        orderDetails.setExternalOrderNumber("345");

        OrderPlaced orderPlaced = new OrderPlaced();
        orderPlaced.setOrderId(orderId);
        orderPlaced.setIpAddress("1363");
        orderPlaced.setStore("TRADEBYTE");
        orderPlaced.setOrderLines(List.of(new OrderLine()));
        orderPlaced.setOrderLines(List.of(orderLine));
        orderPlaced.setOrderDetails(orderDetails);
        orderPlaced.setPlacedDate(ZonedDateTime.now());
        orderPlaced.setIsTest(Boolean.TRUE);
        orderPlaced.setShippingInformation(new ShippingInformation().withShippingAddress(
            new Address().withLastName("bestseller")
                .withFirstName("mina").withCity("AMS").withAddressLine1("add1")
                .withAddressLine3("add3")
                .withCountry("NL")
                .withZipcode("1363AC")));
        orderPlaced.setCustomerInformation(
            new CustomerInformation()
                .withCustomerId("43")
                .withBillingAddress(
                    new Address()
                        .withLastName("bestseller")
                        .withFirstName("mina")
                        .withCity("AMS")
                        .withAddressLine1("add1")
                        .withAddressLine3("add3")
                        .withCountry("NL")
                        .withZipcode("1363AC")
                )
                .withCustomerLocale("en_NL")
        );
        return orderPlaced;
    }

    /**
     * Create order placed message.
     *
     * @return Order Placed Message
     */
    private static OrderPlaced createOrderPlacedMessage() {
        final ZonedDateTime now = ZonedDateTime.now();
        final Address billingAddress = new Address();
        billingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
            .withAddressLine2(AddressProperties.ADDRESS_LINE_2)
            .withAddressLine3(AddressProperties.ADDRESS_LINE_3)
            .withCity(AddressProperties.CITY)
            .withCountry(AddressProperties.COUNTRY)
            .withFirstName(AddressProperties.FIRST_NAME)
            .withLastName(AddressProperties.LAST_NAME)
            .withHouseNumber(AddressProperties.HOUSE_NUMBER)
            .withHouseNumberExtended(AddressProperties.HOUSE_NUMBER_EXTENDED)
            .withPhoneNumber(AddressProperties.PHONE_NUMBER)
            .withZipcode(AddressProperties.ZIPCODE)
            .withCountry(AddressProperties.COUNTRY);

        final Address shippingAddress = new Address();
        shippingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
            .withAddressLine2(AddressProperties.ADDRESS_LINE_2)
            .withAddressLine3(AddressProperties.ADDRESS_LINE_3)
            .withCity(AddressProperties.CITY)
            .withCountry(AddressProperties.COUNTRY)
            .withFirstName(AddressProperties.FIRST_NAME)
            .withLastName(AddressProperties.LAST_NAME)
            .withHouseNumber(AddressProperties.HOUSE_NUMBER)
            .withHouseNumberExtended(AddressProperties.HOUSE_NUMBER_EXTENDED)
            .withPhoneNumber(AddressProperties.PHONE_NUMBER)
            .withZipcode(AddressProperties.ZIPCODE)
            .withCountry(AddressProperties.COUNTRY);

        final CustomerInformation customerInformation = new CustomerInformation()
            .withEmail(CustomerInformationProperties.EMAIL_ADDRESS)
            .withBillingAddress(billingAddress)
            .withCustomerId(CustomerInformationProperties.CUSTOMER_ID);

        final OrderDetails orderDetails = new OrderDetails()
            .withCarrier(OrderDetailsProperties.CARRIER)
            .withCarrierVariant(OrderDetailsProperties.CARRIER_VARIANT)
            .withOrderType(OrderDetailsProperties.ORDER_TYPE)
            .withOrderValue(OrderDetailsProperties.ORDER_VALUE)
            .withShippingMethod(OrderDetailsProperties.SHIPPING_METHOD)
            .withOrderCreationDate(now.plusHours(1))
            .withMarket(OrderDetailsProperties.MARKET);

        final ShippingInformation shippingInformation = new ShippingInformation();
        shippingInformation.withShippingAddress(shippingAddress);

        final List<OrderLine> orderLines = getOrderLines();

        List<Payment> payments = Arrays.asList(
            new Payment(),
            new Payment()
        );

        final FulfillmentAdvice fulfillmentAdvice = new FulfillmentAdvice()
            .withFulfillmentNode(FulfillmentAdviceProperties.FULFILLMENT_NODE);

        return new OrderPlaced()
            .withOrderId(OrderProperties.ORDER_ID)
            .withPlacedDate(now)
            .withIsTest(OrderProperties.IS_TEST)
            .withCustomerInformation(customerInformation)
            .withOrderDetails(orderDetails)
            .withShippingInformation(shippingInformation)
            .withPayments(payments)
            .withOrderLines(orderLines)
            .withFulfillmentAdvice(fulfillmentAdvice);
    }

    public static OrderLine createFullOrderLine() {
        return new OrderLine()
            .withEan(FirstOrderLineProperties.EAN)
            .withExternalItemId(FirstOrderLineProperties.EXTERNAL_ITEM_ID)
            .withProductName(FirstOrderLineProperties.PRODUCT_NAME)
            .withLineNumber(FirstOrderLineProperties.LINE_NUMBER)
            .withQuantity(FirstOrderLineProperties.QUANTITY)
            .withRetailPrice(FirstOrderLineProperties.RETAIL_PRICE)
            .withDiscountValue(FirstOrderLineProperties.DISCOUNT_VALUE)
            .withPartnerReference(FirstOrderLineProperties.PARTNER_REFERENCE)
            .withVat(FirstOrderLineProperties.VAT)
            .withTax(FirstOrderLineProperties.TAX);
    }

    public static OrderLine createVirtualProductOrderLine() {
        OrderLine orderLine = createFullOrderLine();
        orderLine.setVirtualProduct(true);
        return orderLine;
    }

    public static OrderLine createPhysicalProductOrderLine() {
        OrderLine orderLine = createFullOrderLine();
        orderLine.setVirtualProduct(false);
        return orderLine;
    }

    /**
     * First OrderLine properties.
     */
    public static class FirstOrderLineProperties {
        public static final String EAN = "**********";
        public static final Integer EXTERNAL_ITEM_ID = 15846171;
        public static final String PRODUCT_NAME = "productName1";
        public static final int LINE_NUMBER = 1;
        public static final int QUANTITY = 10;
        public static final BigDecimal RETAIL_PRICE = BigDecimal.valueOf(20.00);
        public static final BigDecimal DISCOUNT_VALUE = BigDecimal.valueOf(2.00);
        public static final String PARTNER_REFERENCE = "partnerReference";
        public static final BigDecimal VAT = BigDecimal.valueOf(10.00);
        public static final BigDecimal TAX = BigDecimal.valueOf(2.0);
    }

    /**
     * Second OrderLine properties.
     */
    public static class SecondOrderLineProperties {
        public static final String EAN = "0987654321";
        public static final Integer EXTERNAL_ITEM_ID = 15846172;
        public static final String PRODUCT_NAME = "productName2";
        public static final int LINE_NUMBER = 2;
        public static final int QUANTITY = 11;
        public static final BigDecimal RETAIL_PRICE = BigDecimal.valueOf(25.00);
        public static final BigDecimal DISCOUNT_VALUE = BigDecimal.valueOf(5.00);
        public static final BigDecimal VAT = BigDecimal.valueOf(10.00);
        public static final BigDecimal TAX = BigDecimal.valueOf(2.50);
        public static final String PARTNER_REFERENCE = "partnerReference";
    }
}
