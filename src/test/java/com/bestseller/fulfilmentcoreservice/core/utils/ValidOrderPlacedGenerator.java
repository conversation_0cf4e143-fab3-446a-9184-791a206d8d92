package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.logistics.statetransition.Platform;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidOrderPlacedGenerator {

    // Shipping Information constants
    public static final String ADDRESS_LINE_1 = "Frögatan";
    public static final String CITY = "Solna";
    public static final String COUNTRY = "NL";
    public static final String FIRST_NAME = "First Name";
    public static final String HOUSE_NUMBER = "1";
    public static final String LAST_NAME = "Last Name";
    public static final String PHONE_NUMBER = "+31712474580";
    public static final String ZIPCODE = "3061 GG";

    // Customer Information constants
    public static final String EMAIL = "<EMAIL>";
    public static final String CUSTOMER_ID = "00186577";
    public static final boolean IS_EMPLOYEE = false;

    // OrderLines constants
    public static final String EAN_1 = "5715423786800";
    public static final int LINE_NUMBER_1 = 1;
    public static final String PRODUCT_NAME_1 = "YASFLAIR SHIRT DRESS";
    public static final int QUANTITY_1 = 1;
    public static final BigDecimal LIST_PRICE_1 = new BigDecimal("89.99");
    public static final BigDecimal DISCOUNTED_UNIT_PRICE_1 = new BigDecimal("89.99");
    public static final BigDecimal VAT = new BigDecimal("0.2100");
    public static final String VAT_CLASS = "adult-clothing";
    public static final BigDecimal TAX_UNIT_PRICE_1 = new BigDecimal("15.62");
    public static final BigDecimal DISCOUNTED_TOTAL_PRICE_1 = new BigDecimal("89.99");
    public static final String BRAND_1 = "yas";

    public static final String EAN_2 = "5715209167540";
    public static final int LINE_NUMBER_2 = 2;
    public static final String PRODUCT_NAME_2 = "YASVIGGI SHORTS";
    public static final int QUANTITY_2 = 1;
    public static final BigDecimal RETAIL_PRICE_2 = new BigDecimal("49.99");
    public static final BigDecimal DISCOUNTED_UNIT_PRICE_2 = new BigDecimal("49.99");
    public static final BigDecimal TAX_UNIT_PRICE_2 = new BigDecimal("8.68");
    public static final BigDecimal DISCOUNTED_TOTAL_PRICE_2 = new BigDecimal("49.99");
    public static final String BRAND_2 = "yas";

    // Order Details constants
    public static final String SHIPPING_METHOD = "STANDARD";
    public static final ZonedDateTime ORDER_CREATION_DATE = ZonedDateTime.parse("2023-10-11T07:57:12.000Z");
    public static final String CARRIER = "DHL";
    public static final String CARRIER_VARIANT = "HOME";
    public static final BigDecimal ORDER_VALUE = new BigDecimal("139.98");
    public static final String ORDER_TYPE = "STANDARD";
    public static final String CHECKOUT = "ys";
    public static final String BRANDED_SHIPPING = "YS";
    public static final String MARKET = "BSE-NL";
    public static final String CHANNEL_STOREFRONT = "storefront";
    public static final String CHANNEL_TRADEBYTE = "amco";

    // Payments constants
    public static final String PAYMENT_METHOD = "ADYEN_IDEAL";
    public static final String PAYMENT_SUB_METHOD = "ideal";
    public static final String PSP_REFERENCE = "FKD7LRLPF6ZW8N82";
    public static final String PAYMENT_PROVIDER = "ADYEN";
    public static final BigDecimal PAYMENT_AMOUNT = new BigDecimal("139.98");
    public static final String PAYMENT_CURRENCY = "EUR";
    public static final String PAYMENT_STATE = "AUTHORISED";

    // Other constants
    public static final boolean IS_TEST = false;
    public static final ZonedDateTime PLACED_DATE = ZonedDateTime.parse("2023-10-11T07:57:12.000Z");
    public static final LocalDate ANNOUNCED_DELIVERY_DATE = LocalDate.parse("2023-10-15");
    public static final int EXTERNAL_ITEM_ID_1 = 7709;
    public static final int EXTERNAL_ITEM_ID_2 = 9077;
    public static final String IP_ADDRESS = "*************";
    public static final String ISO_STORE_ID = "ISO Store ID";
    public static final UUID ECOM_ORDERLINE_ID_1 = UUID.fromString("715C1A60-E026-427E-817A-D75C1DFBF235");
    public static final UUID ECOM_ORDERLINE_ID_2 = UUID.fromString("83EDF24A-A315-4222-A080-2FA1E9C3576A");

    public static ValidOrderPlaced generateDemandwareOrder(String orderId) {
        ValidOrderPlaced message = generateBasicMessage(orderId);

        message.getOrderDetails().setChannel(CHANNEL_STOREFRONT);
        message.setStore(Platform.DEMANDWARE.name());
        message.setPayments(
            List.of(
                new Payment()
                    .withMethod(PAYMENT_METHOD)
                    .withSubMethod(PAYMENT_SUB_METHOD)
                    .withPspReference(PSP_REFERENCE)
                    .withProvider(PAYMENT_PROVIDER)
                    .withAmount(PAYMENT_AMOUNT)
                    .withCurrency(PAYMENT_CURRENCY)
                    .withState(PAYMENT_STATE))
        );
        return message;
    }

    public static ValidOrderPlaced generateTradebyteOrder(String orderId) {
        ValidOrderPlaced message = generateBasicMessage(orderId);

        message.getOrderDetails().setChannel(CHANNEL_TRADEBYTE);
        message.setStore(Platform.TRADEBYTE.name());
        message.getOrderLines().get(0).setExternalItemId(EXTERNAL_ITEM_ID_1);
        message.getOrderLines().get(1).setExternalItemId(EXTERNAL_ITEM_ID_2);
        return message;
    }

    private static ValidOrderPlaced generateBasicMessage(String orderId) {
        return new ValidOrderPlaced()
            .withShippingInformation(new ShippingInformation()
                .withShippingAddress(new Address()
                    .withAddressLine1(ADDRESS_LINE_1)
                    .withCity(CITY)
                    .withCountry(COUNTRY)
                    .withFirstName(FIRST_NAME)
                    .withHouseNumber(HOUSE_NUMBER)
                    .withLastName(LAST_NAME)
                    .withPhoneNumber(PHONE_NUMBER)
                    .withZipcode(ZIPCODE))
                .withShippingCharges(List.of()))
            .withCustomerInformation(new CustomerInformation()
                .withBillingAddress(new Address()
                    .withAddressLine1(ADDRESS_LINE_1)
                    .withCity(CITY)
                    .withCountry(COUNTRY)
                    .withFirstName(FIRST_NAME)
                    .withHouseNumber(HOUSE_NUMBER)
                    .withLastName(LAST_NAME)
                    .withPhoneNumber(PHONE_NUMBER)
                    .withZipcode(ZIPCODE))
                .withEmail(EMAIL)
                .withCustomerId(CUSTOMER_ID)
                .withIsEmployee(IS_EMPLOYEE))
            .withOrderLines(Arrays.asList(
                new OrderLine()
                    .withId(ECOM_ORDERLINE_ID_1)
                    .withEan(EAN_1)
                    .withLineNumber(LINE_NUMBER_1)
                    .withProductName(PRODUCT_NAME_1)
                    .withQuantity(QUANTITY_1)
                    .withRetailPrice(LIST_PRICE_1)
                    .withDiscountedUnitPrice(DISCOUNTED_UNIT_PRICE_1)
                    .withVat(VAT)
                    .withVatClass(VAT_CLASS)
                    .withTaxUnitPrice(TAX_UNIT_PRICE_1)
                    .withVirtualProduct(false)
                    .withDiscountedTotalPrice(DISCOUNTED_TOTAL_PRICE_1)
                    .withBrand(BRAND_1),
                new OrderLine()
                    .withId(ECOM_ORDERLINE_ID_2)
                    .withEan(EAN_2)
                    .withLineNumber(LINE_NUMBER_2)
                    .withProductName(PRODUCT_NAME_2)
                    .withQuantity(QUANTITY_2)
                    .withRetailPrice(RETAIL_PRICE_2)
                    .withDiscountedUnitPrice(DISCOUNTED_UNIT_PRICE_2)
                    .withVat(VAT)
                    .withTaxUnitPrice(TAX_UNIT_PRICE_2)
                    .withVirtualProduct(false)
                    .withDiscountedTotalPrice(DISCOUNTED_TOTAL_PRICE_2)
                    .withBrand(BRAND_2)))
            .withOrderDetails(new OrderDetails()
                .withShippingMethod(SHIPPING_METHOD)
                .withOrderCreationDate(ORDER_CREATION_DATE)
                .withCarrier(CARRIER)
                .withCarrierVariant(CARRIER_VARIANT)
                .withOrderValue(ORDER_VALUE)
                .withOrderType(ORDER_TYPE)
                .withCheckout(CHECKOUT)
                .withBrandedShipping(BRANDED_SHIPPING)
                .withMarket(MARKET)
                .withExternalOrderNumber(orderId.replace("TB", ""))
                .withIsoStoreId(ISO_STORE_ID)
            )
            .withOrderId(orderId)
            .withIpAddress(IP_ADDRESS)
            .withIsTest(IS_TEST)
            .withPlacedDate(PLACED_DATE)
            .withAnnouncedDeliveryDate(ANNOUNCED_DELIVERY_DATE)
            .withOrderPromotions(List.of())
            .withOfflinePayment(true);
    }

}
