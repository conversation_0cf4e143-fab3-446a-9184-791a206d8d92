package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;

public class PartnerChannelGenerator {

    public static final int ACTION_CODE = 1234;
    public static final String DESCRIPTION = "description";
    public static final String CHANNEL_ID = "3061";

    public static PartnerChannel createPartnerChannelGenerator() {
        return PartnerChannel
            .builder()
            .channelId(CHANNEL_ID)
            .actionCode(ACTION_CODE)
            .description(DESCRIPTION)
            .build();
    }
}
