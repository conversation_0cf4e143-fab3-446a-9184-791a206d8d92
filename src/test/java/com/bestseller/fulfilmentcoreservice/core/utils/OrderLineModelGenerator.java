package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.OrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.model.TradebyteOrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.model.ValidOrderPlacedOrderLineModel;
import com.logistics.statetransition.OrderState;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.EntryType.INVOICE_PAYMENT_FEE;

@SuppressWarnings("all")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderLineModelGenerator {

    public static final UUID ECOM_ID = UUID.fromString("8453E2B2-900D-4F47-ABAF-833E850D879A");

    public static ValidOrderPlacedOrderLineModel generateOrderLine() {
        final int originalQty = 4;
        final int openQty = 3;
        return ValidOrderPlacedOrderLineModel.builder()
            .ecomId(ECOM_ID)
            .bonusProduct(true)
            .lineNumber(1)
            .brandDescription("JJ")
            .type(INVOICE_PAYMENT_FEE.name())
            .name("NAME")
            .skuId("1234413123123")
            .ean("9873819263891")
            .taxRate(new BigDecimal("0.25"))
            .vatClassId("VAT_CLASS_ID")
            .originalQty(originalQty)
            .openQty(openQty)
            .promotionId("PROMOTION_ID")
            .campaignId("CAMPAIGN_ID")
            .couponId("COUPON_ID")
            .standardRetailPrice(new BigDecimal("67.00"))
            .partnerReference("PARTNER_REFERENCE")
            .virtualProduct(true)
            .orderLineQtyStatus(List.of(OrderLineQtyStatusModel.builder()
                .tradebyteOrderLineQtyStatus(TradebyteOrderLineQtyStatusModel.builder()
                    .originalLineNumber(1)
                    .lastTradebyteStatus(OrderState.EXPORTED.name())
                    .build())
                .build()))
            .build();
    }
}
