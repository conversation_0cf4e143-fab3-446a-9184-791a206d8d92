package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.OrderDetailsModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderLineModel;
import com.bestseller.fulfilmentcoreservice.core.model.PaymentModel;
import com.bestseller.fulfilmentcoreservice.core.model.PaymentStatusUpdatedModel;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.Payment;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PaymentStatusUpdatedGenerator {

    private static final String PAYMENT_SUB_METHOD = "ADYEN_CREDIT_CARD";
    private static final String PAYMENT_METHOD_NAME = "ADYEN_CARD";
    private static final Double ORDER_VALUE = 399.95;
    private static final Double SHIPPING_FEES = 0.0;
    private static final Double SHIPPING_FEES_TAX_PERCENTAGE = 0.25;

    private static final String EAN = "1234567890123";

    public static PaymentStatusUpdatedModel toPaymentStatusUpdatedModel(String orderId) {
        return PaymentStatusUpdatedModel.builder()
            .orderId(orderId)
            .payments(List.of(PaymentModel.builder()
                .name(PAYMENT_METHOD_NAME)
                .subMethod(PAYMENT_SUB_METHOD)
                .amount(BigDecimal.valueOf(ORDER_VALUE)).build()))
            .orderDetails(OrderDetailsModel.builder()
                .orderValue(BigDecimal.valueOf(ORDER_VALUE))
                .shippingFees(BigDecimal.valueOf(SHIPPING_FEES))
                .shippingFeesTaxPercentage(BigDecimal.valueOf(SHIPPING_FEES_TAX_PERCENTAGE))
                .shippingFeesCancelled(Boolean.FALSE)
                .build())
            .orderLines(List.of(
                OrderLineModel.builder()
                    .ean(EAN)
                    .discountValue(BigDecimal.valueOf(SHIPPING_FEES))
                    .taxPercentage(BigDecimal.valueOf(SHIPPING_FEES_TAX_PERCENTAGE))
                    .retailPrice(BigDecimal.valueOf(ORDER_VALUE))
                    .build()
            )).build();
    }

    public static PaymentStatusUpdated toPaymentStatusUpdated(String orderId,
                                                              PaymentStatusUpdated.PaymentState paymentState) {
        return new PaymentStatusUpdated()
            .withOrderId(orderId)
            .withPaymentState(paymentState)
            .withPayload(AuthorizedPayload.builder()
                .payments(List.of(Payment.builder()
                    .name(PAYMENT_METHOD_NAME)
                    .subMethod(PAYMENT_SUB_METHOD)
                    .amount(BigDecimal.valueOf(ORDER_VALUE))
                    .build()))
                .build());
    }
}
