package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderPartsCancelledGenerator {

    // OrderLines constants
    public static final String EAN_1 = "5715423786800";
    public static final int LINE_NUMBER_1 = 1;
    public static final int QUANTITY_1 = 1;
    public static final String EAN_2 = "5715209167540";
    public static final int LINE_NUMBER_2 = 2;
    public static final int QUANTITY_2 = 1;
    public static final String WAREHOUSE = "warehouse";

    public static OrderPartsCancelled generateOrderPartsCancelled(String orderId) {
        return new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse(WAREHOUSE)
            .withCancellationDate(ZonedDateTime.now())
            .withOrderLines(List.of(
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                    .withEan(EAN_1)
                    .withLineNumber(LINE_NUMBER_1)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(QUANTITY_1),
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                    .withEan(EAN_2)
                    .withLineNumber(LINE_NUMBER_2)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(QUANTITY_2)));
    }
}
