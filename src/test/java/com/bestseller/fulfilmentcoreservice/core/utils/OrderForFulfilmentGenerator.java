package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.core.model.AddressModel;
import com.bestseller.fulfilmentcoreservice.core.model.CustomerInformationModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderDetailsModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderForFulfilmentModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderLineModel;
import com.bestseller.fulfilmentcoreservice.core.model.ShippingInformationModel;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderForFulfilmentGenerator {

    public static final String CHANNEL_TRADEBYTE = "tradebyte";
    public static final String CHANNEL_STOREFRONT = "TRADEBYTE";
    public static final String MARKET = "BSE-NL";

    // Shipping Information constants
    public static final String ADDRESS_LINE_1 = "Frögatan";
    public static final String CITY = "Solna";
    public static final String COUNTRY = "NL";
    public static final String FIRST_NAME = "First Name";
    public static final String HOUSE_NUMBER = "1";
    public static final String LAST_NAME = "Last Name";
    public static final String PHONE_NUMBER = "+31712474580";
    public static final String ZIPCODE = "3061 GG";

    // Customer Information constants
    public static final String EMAIL = "<EMAIL>";
    public static final String CUSTOMER_ID = "00186577";
    public static final String CUSTOMER_LOCALE = "customerLocale";

    // OrderLines constants
    public static final String EAN_1 = "5715423786800";
    public static final int LINE_NUMBER_1 = 1;
    public static final String PRODUCT_NAME_1 = "YASFLAIR SHIRT DRESS";
    public static final int QUANTITY_1 = 1;
    public static final BigDecimal LIST_PRICE_1 = new BigDecimal("89.99");
    public static final String BRAND_1 = "yas";

    public static final String EAN_2 = "5715209167540";
    public static final int LINE_NUMBER_2 = 2;
    public static final String PRODUCT_NAME_2 = "YASVIGGI SHORTS";
    public static final int QUANTITY_2 = 1;
    public static final BigDecimal RETAIL_PRICE_2 = new BigDecimal("49.99");
    public static final String BRAND_2 = "yas";

    // Order Details constants
    public static final String SHIPPING_METHOD = "STANDARD";
    public static final ZonedDateTime ORDER_CREATION_DATE = ZonedDateTime.parse("2023-10-11T07:57:12.000Z");
    public static final String CARRIER = "DHL";
    public static final String CARRIER_VARIANT = "HOME";
    public static final BigDecimal ORDER_VALUE = new BigDecimal("139.98");
    public static final String ORDER_TYPE = "STANDARD";
    public static final BigDecimal SHIPPING_FEES = BigDecimal.ZERO;
    public static final String CHECKOUT = "ys";

    public static OrderForFulfillment generateOrderForFulfilment(String orderId) {
        return new OrderForFulfillment()
            .withOrderId(orderId)
            .withBrand(BRAND_1)
            .withChannel(CHANNEL_TRADEBYTE)
            .withCustomerInformation(new CustomerInformation()
                .withBillingAddress(new Address()
                    .withAddressLine1(ADDRESS_LINE_1)
                    .withCity(CITY)
                    .withCountry(COUNTRY)
                    .withFirstName(FIRST_NAME)
                    .withHouseNumber(HOUSE_NUMBER)
                    .withLastName(LAST_NAME)
                    .withPhoneNumber(PHONE_NUMBER)
                    .withZipcode(ZIPCODE))
                .withEmail(EMAIL)
                .withCustomerId(CUSTOMER_ID)
                .withCustomerLocale(CUSTOMER_LOCALE))
            .withIsTest(Boolean.TRUE)
            .withOrderDetails(new OrderDetails()
                .withShippingMethod(SHIPPING_METHOD)
                .withOrderCreationDate(ORDER_CREATION_DATE)
                .withCarrier(CARRIER)
                .withCarrierVariant(CARRIER_VARIANT)
                .withOrderValue(ORDER_VALUE)
                .withOrderType(ORDER_TYPE)
                .withShippingFees(SHIPPING_FEES)
                .withCheckout(CHECKOUT)
                .withExternalOrderNumber(orderId.replace("TB", "")))
            .withOrderLines(List.of(
                new OrderLine()
                    .withEan(EAN_1)
                    .withLineNumber(LINE_NUMBER_1)
                    .withProductName(PRODUCT_NAME_1)
                    .withQuantity(QUANTITY_1)
                    .withRetailPrice(LIST_PRICE_1)
                    .withBrand(BRAND_1),
                new OrderLine()
                    .withEan(EAN_2)
                    .withLineNumber(LINE_NUMBER_2)
                    .withProductName(PRODUCT_NAME_2)
                    .withQuantity(QUANTITY_2)
                    .withRetailPrice(RETAIL_PRICE_2)
                    .withBrand(BRAND_2)))
            .withStore(CHANNEL_STOREFRONT)
            .withMarketPlace(MARKET)
            .withShippingInformation(new ShippingInformation()
                .withShippingAddress(new Address()
                    .withAddressLine1(ADDRESS_LINE_1)
                    .withCity(CITY)
                    .withCountry(COUNTRY)
                    .withFirstName(FIRST_NAME)
                    .withHouseNumber(HOUSE_NUMBER)
                    .withLastName(LAST_NAME)
                    .withPhoneNumber(PHONE_NUMBER)
                    .withZipcode(ZIPCODE)));
    }

    public static OrderForFulfilmentModel generateOrderForFulfilmentModel(String orderId) {
        return OrderForFulfilmentModel.builder()
            .orderId(orderId)
            .brand(BRAND_1)
            .channel(CHANNEL_TRADEBYTE)
            .customerInformation(CustomerInformationModel.builder()
                .billingAddress(AddressModel.builder()
                    .address1(ADDRESS_LINE_1)
                    .city(CITY)
                    .countryCode(COUNTRY)
                    .firstName(FIRST_NAME)
                    .houseNumber(HOUSE_NUMBER)
                    .lastName(LAST_NAME)
                    .phone(PHONE_NUMBER)
                    .postCode(ZIPCODE).build())
                .email(EMAIL)
                .customerId(CUSTOMER_ID)
                .customerLocale(CUSTOMER_LOCALE).build())
            .isTest(Boolean.TRUE)
            .orderDetails(OrderDetailsModel.builder()
                .shippingMethod(SHIPPING_METHOD)
                .orderCreationDate(ORDER_CREATION_DATE)
                .carrier(CARRIER)
                .carrierVariant(CARRIER_VARIANT)
                .orderValue(ORDER_VALUE)
                .orderType(ORDER_TYPE)
                .shippingFees(SHIPPING_FEES)
                .checkout(CHECKOUT)
                .externalOrderNumber(orderId.replace("TB", ""))
                .build())
            .marketPlace(CHANNEL_STOREFRONT)
            .shippingInformation(ShippingInformationModel.builder()
                .shippingAddress(AddressModel.builder()
                    .address1(ADDRESS_LINE_1)
                    .city(CITY)
                    .countryCode(COUNTRY)
                    .firstName(FIRST_NAME)
                    .houseNumber(HOUSE_NUMBER)
                    .lastName(LAST_NAME)
                    .phone(PHONE_NUMBER)
                    .postCode(ZIPCODE).build())
                .build())
            .orderLines(List.of(
                OrderLineModel.builder()
                    .virtualProduct(Boolean.FALSE)
                    .ean(EAN_1)
                    .lineNumber(LINE_NUMBER_1)
                    .productName(PRODUCT_NAME_1)
                    .quantity(QUANTITY_1)
                    .retailPrice(LIST_PRICE_1)
                    .brand(BRAND_1).build(),
                OrderLineModel.builder()
                    .ean(EAN_2)
                    .lineNumber(LINE_NUMBER_2)
                    .productName(PRODUCT_NAME_2)
                    .quantity(QUANTITY_2)
                    .retailPrice(RETAIL_PRICE_2)
                    .brand(BRAND_2).build()))
            .build();
    }
}
