package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.fulfilmentcoreservice.persistence.entity.AdditionalOrderInformation;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;
import com.bestseller.fulfilmentcoreservice.persistence.entity.UserDeviceInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.EntityType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

public class OrderGenerator {
    public static final ZonedDateTime TEST_DATE = ZonedDateTime.now();
    public static final int VERSION = 10;
    public static final int ACTION_CODE = 1234;
    public static final int LINE_NUMBER = 1;
    public static final String DESCRIPTION = "description";
    public static final String EAN_1 = "5715423786800";
    public static final String CURRENCY = "EUR";
    public static final String CHECKOUT_SCOPE_ID = "ys";
    public static final String EXTERNAL_ORDER_NUMBER = "612678123";

    // Address Information constants
    public static final String CARRIER = "DHL";
    public static final String ADDRESS_LINE_1 = "Frögatan";
    public static final String CITY = "Solna";
    public static final String COUNTRY = "NL";
    public static final String FIRST_NAME = "First Name";
    public static final String HOUSE_NUMBER = "1";
    public static final String LAST_NAME = "Last Name";
    public static final String PHONE_NUMBER = "+31712474580";
    public static final String POSTCODE = "3061 GG";
    public static final String CHANNEL_ID = "3061";
    public static final Integer QUANTITY = 2;
    public static final String FULFILMENT_NODE = "fulfilmentNode";
    public static final String PLATFORM = "platform";
    public static final String ISO_STORE_ID = "ISO Store ID";

    public static final String KEY = "KEY";
    public static final String VALUE = "VALUE";

    public static Order createOrder(String testOrderId) {
        var shippingAddress = Address.builder()
            .address1(ADDRESS_LINE_1)
            .city(CITY)
            .countryCode(COUNTRY)
            .firstName(FIRST_NAME)
            .houseNumber(HOUSE_NUMBER)
            .lastName(LAST_NAME)
            .deliveryOption(CARRIER)
            .carrierVariant(CarrierVariant.HOME)
            .phone(PHONE_NUMBER)
            .postCode(POSTCODE).build();
        var order = Order.builder()
            .orderId(testOrderId)
            .orderDate(TEST_DATE)
            .lastModifiedTS(TEST_DATE)
            .createdTS(TEST_DATE)
            .version(VERSION)
            .shippingMethod(ShippingMethod.STANDARD)
            .currency(CURRENCY)
            .checkoutScopeId(CHECKOUT_SCOPE_ID)
            .externalOrderId(EXTERNAL_ORDER_NUMBER)
            .platform(Platform.TRADEBYTE)
            .userDeviceInfo(UserDeviceInfo
                .builder()
                .platform(PLATFORM)
                .build())
            .billingAddress(Address.builder()
                .address1(ADDRESS_LINE_1)
                .city(CITY)
                .countryCode(COUNTRY)
                .firstName(FIRST_NAME)
                .houseNumber(HOUSE_NUMBER)
                .lastName(LAST_NAME)
                .phone(PHONE_NUMBER)
                .postCode(POSTCODE).build())
            .shippingAddress(shippingAddress)
            .channelType(ChannelType.STOREFRONT)
            .customer(Customer.builder()
                .email("<EMAIL>")
                .customerId("00186577")
                .build())
            .brand(Brand.ON)
            .partnerChannel(
                PartnerChannel
                    .builder()
                    .channelId(CHANNEL_ID)
                    .actionCode(ACTION_CODE)
                    .description(DESCRIPTION)
                    .build())
            .market(Market.BSE_NL)
            .orderLines(List.of(
                OrderLine.builder()
                    .originalQty(QUANTITY)
                    .ean(EAN_1)
                    .lineNumber(LINE_NUMBER)
                    .ecomId(UUID.randomUUID())
                    .openQty(QUANTITY)
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderFulfillmentPart(OrderFulfillmentPart
                                .builder()
                                .fulfillmentNode(FULFILMENT_NODE)
                                .holdFromRouting(true).build())
                            .orderState(OrderState.PLACED).build()))
                    .build()))
            .isoStoreId(ISO_STORE_ID)
            .build();
        order.setAdditionalOrderInformation(getAdditionalInformation(order));

        order.getOrderLines().forEach(orderLine -> {
            orderLine.getOrderLineQtyStatus().forEach(orderLineQtyStatus -> {
                orderLineQtyStatus.setOrderLine(orderLine);
                orderLineQtyStatus.getOrderFulfillmentPart().setOrderLineQtyStatus(List.of(orderLineQtyStatus));
            });
        });

        return order;
    }

    private static List<AdditionalOrderInformation> getAdditionalInformation(Order order) {
        AdditionalOrderInformation shippingInfo = new AdditionalOrderInformation(
            1, EntityType.SHIPPING_INFORMATION, KEY, VALUE, order
        );
        shippingInfo.setVersion(VERSION + 1);

        AdditionalOrderInformation orderDetail = new AdditionalOrderInformation(
            2, EntityType.ORDER_DETAILS, KEY, VALUE, order
        );
        orderDetail.setVersion(VERSION + 1);

        return List.of(shippingInfo, orderDetail);
    }
}
