package com.bestseller.fulfilmentcoreservice.core.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GiftCardPurchasedGenerator {

    private static final String URL = "fc.giftcard.com";
    private static final String CARD_NUMBER = "123456";
    private static final int INDEX = 1;
    private static final int ORDER_LINE_NUMBER = 1;

    public static GiftCardPurchased generateGiftCardPurchased(String orderId) {
        return new GiftCardPurchased()
            .withOrderId(orderId)
            .withGiftCardUrl(URL)
            .withGiftCardNumber(CARD_NUMBER)
            .withIndex(INDEX)
            .withOrderLineNumber(ORDER_LINE_NUMBER);

    }
}
