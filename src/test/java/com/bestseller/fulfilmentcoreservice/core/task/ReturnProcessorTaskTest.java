package com.bestseller.fulfilmentcoreservice.core.task;

import com.bestseller.fulfilmentcoreservice.core.service.ReturnProcessorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ReturnProcessorTaskTest {

    @InjectMocks
    private ReturnProcessorTask returnProcessorTask;

    @Mock
    private ReturnProcessorService returnProcessorService;

    @Test
    void processReturn_processMethodIsCalled() {
        // arrange

        // act
        returnProcessorTask.processReturn();

        // assert
        Mockito.verify(returnProcessorService).processReturns();
    }

}
