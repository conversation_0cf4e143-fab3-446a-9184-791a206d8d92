package com.bestseller.fulfilmentcoreservice.core.task;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OrderFinalizerTaskTest {

    @InjectMocks
    private OrderFinalizerTask orderFinalizerTask;

    @Mock
    private OrderService orderService;

    @Test
    void run_finalizeOrdersMethodIsCalled() {
        // arrange

        // act
        orderFinalizerTask.run();

        // assert
        Mockito.verify(orderService).finalizeOrders();
    }

}
