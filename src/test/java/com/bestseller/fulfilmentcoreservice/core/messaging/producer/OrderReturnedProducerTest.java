package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReturnedProducerTest {

    @InjectMocks
    private OrderReturnedKafkaMessageProducer orderReturnedProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenMessage_producesMessage() {
        // arrange
        var orderReturned = new OrderReturned();
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(orderReturned))).thenReturn(true);

        // act & assert
        assertDoesNotThrow(() -> orderReturnedProducer.produce(orderReturned));
    }

    @Test
    void produce_givenMessageAndStreamBridgeCannotProduce_messageNotProduced() {
        // arrange
        var orderReturned = new OrderReturned();
        orderReturned.setOrderId("TB123456");
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(orderReturned))).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderReturnedProducer.produce(orderReturned));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderReturned: orderId=TB123456");
    }

}
