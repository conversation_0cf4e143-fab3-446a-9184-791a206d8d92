package com.bestseller.fulfilmentcoreservice.core.messaging.idempotency;

import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.IdempotencyCheckHandler;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IdempotencyCheckHandlerTest {

    private static final String ORDER_ID = "12345";
    private static final String EAN = "EAN123";

    @Mock
    private OrderService orderService;

    private IdempotencyCheckHandler<Object> idempotencyCheck;

    @BeforeEach
    void setUp() {
        idempotencyCheck = new IdempotencyCheckHandler<>(orderService);
    }

    @Test
    void testGetEan_MethodPresent() throws Exception {
        var message = new OrderLineExported();
        message.setOrderId(ORDER_ID);
        message.setEan(EAN);
        Method getEanMethod = IdempotencyCheckHandler.class.getDeclaredMethod("getEan", Object.class);
        getEanMethod.setAccessible(true);

        String ean = (String) getEanMethod.invoke(idempotencyCheck, message);
        assertEquals(EAN, ean, "Expected getEan to return the correct EAN value");
    }

    @Test
    void testGetEan_MethodNotPresent() throws Exception {
        var message = new OrderLineExported();
        message.setOrderId(ORDER_ID);
        message.setEan(null);
        Method getEanMethod = IdempotencyCheckHandler.class.getDeclaredMethod("getEan", Object.class);
        getEanMethod.setAccessible(true);

        String ean = (String) getEanMethod.invoke(idempotencyCheck, message);
        assertNull(ean, "Expected getEan to return null when method is missing");
    }

    @Test
    void testIsDuplicate_WithValidEan() {
        var message = new OrderLineExported();
        message.setOrderId(ORDER_ID);
        message.setEan(EAN);
        when(orderService.isDuplicateRequest(ORDER_ID, EAN, OrderState.EXPORTED)).thenReturn(true);

        boolean result = idempotencyCheck.isDuplicate(message);

        assertTrue(result);
        verify(orderService, times(1))
            .isDuplicateRequest(ORDER_ID, EAN, OrderState.EXPORTED);
    }

    @Test
    void testIsDuplicate_WithoutEan() {
        var message = new OrderLineExported();
        message.setOrderId(ORDER_ID);
        when(orderService.isDuplicateRequest(ORDER_ID, OrderState.EXPORTED)).thenReturn(false);

        boolean result = idempotencyCheck.isDuplicate(message);

        assertFalse(result);
        verify(orderService, times(1)).isDuplicateRequest(ORDER_ID, OrderState.EXPORTED);
    }

    @Test
    void testGetOrderId_MethodNotPresent() throws Exception {
        var getOrderIdMethod = IdempotencyCheckHandler.class.getDeclaredMethod("getOrderId", Object.class);
        getOrderIdMethod.setAccessible(true);

        String orderId = (String) getOrderIdMethod.invoke(idempotencyCheck, new Object());
        assertNull(orderId, "Expected getOrderIdMethod to return null when method is missing");
    }
}
