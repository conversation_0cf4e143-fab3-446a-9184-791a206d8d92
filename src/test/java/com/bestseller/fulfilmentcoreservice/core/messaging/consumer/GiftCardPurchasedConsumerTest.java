package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.fulfilmentcoreservice.core.service.GiftCardPurchasedService;
import com.bestseller.fulfilmentcoreservice.core.utils.GiftCardPurchasedGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class GiftCardPurchasedConsumerTest {
    @Mock
    private GiftCardPurchasedService giftCardPurchasedService;

    @InjectMocks
    private GiftCardPurchasedConsumer consumer;

    @Test
    void consume_givenValidMessage_success() {
        // arrange
        var message = GiftCardPurchasedGenerator.generateGiftCardPurchased("orderId");

        // act
        consumer.consume(message);

        // assert
        verify(giftCardPurchasedService, times(1)).process(any());
    }

    @Test
    void getMessageKey_givenValidMessage_orderIdIsReturned() {
        // arrange
        var message = GiftCardPurchasedGenerator.generateGiftCardPurchased("orderId");

        // act
        var messageKey = consumer.getMessageKey(message);

        // assert
        assertThat(messageKey).isEqualTo(message.getOrderId());
    }
}
