package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.UUID;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundCreationRequestedProducerTest {

    @InjectMocks
    private RefundCreationRequestedKafkaMessageProducer refundCreationRequestedProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenMessage_producesMessage() {
        // arrange
        var refundCreationRequested = new RefundCreationRequested();
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(refundCreationRequested))).thenReturn(true);

        // act & assert
        assertDoesNotThrow(() -> refundCreationRequestedProducer.produce(refundCreationRequested));
    }

    @Test
    void produce_givenMessageAndStreamBridgeCannotProduce_messageNotProduced() {
        // arrange
        var refundCreationRequested = new RefundCreationRequested();
        refundCreationRequested.setOrderId("TB123456");
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(refundCreationRequested))).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> refundCreationRequestedProducer.produce(refundCreationRequested));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic RefundCreationRequested: "
                + "orderId=TB123456, refundCreationRequestedId=%s"
                .formatted(refundCreationRequested.getRefundCreationRequestedId())
            );
    }

}
