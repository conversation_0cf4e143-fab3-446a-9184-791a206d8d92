package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderStatusUpdatedProducerTest {

    private static final String ORDER_STATUS_UPDATED_BIND_NAME = "orderStatusUpdated-out-0";

    @InjectMocks
    private OrderStatusUpdatedKafkaMessageProducer orderStatusUpdateProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenOrderStatusUpdated_producesMessage() {
        // arrange
        var orderStatusUpdated = new OrderStatusUpdated();
        when(streamBridge.send(ORDER_STATUS_UPDATED_BIND_NAME, orderStatusUpdated)).thenReturn(true);

        // act
        orderStatusUpdateProducer.produce(orderStatusUpdated);

        // assert
        // no exception thrown
    }

    @Test
    void produce_givenOrderStatusUpdated_messageNotProduced() {
        // arrange
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId("OL123456");
        orderStatusUpdated.setType(OrderStatusUpdated.Type.ROUTED);
        when(streamBridge.send(ORDER_STATUS_UPDATED_BIND_NAME, orderStatusUpdated)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderStatusUpdateProducer.produce(orderStatusUpdated));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage(
                "Kafka message couldn't be produced for topic OrderStatusUpdated: orderId=OL123456, type=ROUTED"
            );
    }

}
