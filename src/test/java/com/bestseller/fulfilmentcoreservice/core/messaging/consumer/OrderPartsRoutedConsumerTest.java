package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.fulfilmentcoreservice.core.service.OrderPartsRoutedService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsRoutedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderPartsRoutedConsumerTest {

    @Mock
    private OrderPartsRoutedService orderPartsRoutedService;

    @InjectMocks
    private OrderPartsRoutedConsumer consumer;

    @Test
    void consume_givenValidMessage_success() {
        // arrange
        var message = OrderPartsRoutedGenerator.generateOrderPartsRouted("orderId");

        // act
        consumer.consume(message);

        // assert
        verify(orderPartsRoutedService, times(1)).process(any());
    }

    @Test
    void getMessageKey_givenValidMessage_orderIdIsReturned() {
        // arrange
        var message = new OrderPartsRouted().withOrderId("12345");

        // act
        var messageKey = consumer.getMessageKey(message);

        // assert
        assertThat(messageKey).isEqualTo(message.getOrderId());
    }
}
