package com.bestseller.fulfilmentcoreservice.core.messaging.idempotency;

import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.ValidOrderPlacedIdempotencyCheck;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedIdempotencyCheckTest {

    @InjectMocks
    private ValidOrderPlacedIdempotencyCheck idempotencyCheck;

    @Mock
    private OrderService orderService;

    @Test
    void isDuplicate_returnsTrue() {
        // arrange
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");
        when(orderService.exists(validOrderPlaced.getOrderId())).thenReturn(true);

        // act
        var result = idempotencyCheck.isDuplicate(validOrderPlaced);

        // assert
        assertTrue(result);
        verify(orderService).exists(validOrderPlaced.getOrderId());
    }

    @Test
    void isDuplicate_returnsFalse() {
        // arrange
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");
        when(orderService.exists(validOrderPlaced.getOrderId())).thenReturn(false);

        // act
        var result = idempotencyCheck.isDuplicate(validOrderPlaced);

        // assert
        assertFalse(result);
        verify(orderService).exists(validOrderPlaced.getOrderId());
    }
}
