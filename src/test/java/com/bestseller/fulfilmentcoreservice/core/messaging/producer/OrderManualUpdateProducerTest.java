package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderManualUpdateProducerTest {

    private static final String ORDER_MANUAL_UPDATE_BIND_NAME = "orderManualUpdate-out-0";

    @InjectMocks
    private OrderManualUpdateKafkaMessageProducer orderManualUpdateProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenValidOrderManualUpdate_producesMessage() {
        // arrange
        var orderManualUpdate = new OrderManualUpdate();
        when(streamBridge.send(ORDER_MANUAL_UPDATE_BIND_NAME, orderManualUpdate)).thenReturn(true);

        // act
        orderManualUpdateProducer.produce(orderManualUpdate);

        // assert
        // no exception thrown
    }

    @Test
    void produce_givenValidOrderManualUpdate_messageNotProduced() {
        // arrange
        var orderManualUpdate = new OrderManualUpdate();
        orderManualUpdate.setOrderId("OL123456");
        when(streamBridge.send(ORDER_MANUAL_UPDATE_BIND_NAME, orderManualUpdate)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderManualUpdateProducer.produce(orderManualUpdate));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderManualUpdate: orderId=OL123456");
    }

}
