package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderForFulfilmentProducerTest {
    private static final String ORDER_STATUS_UPDATED_BIND_NAME = "orderForFulfilment-out-0";

    @InjectMocks
    private OrderForFulfilmentKafkaMessageProducer orderForFulfilmentProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenOrderForFulfilment_producesMessage() {
        // arrange
        var orderForFulfillment = new OrderForFulfillment();
        when(streamBridge.send(ORDER_STATUS_UPDATED_BIND_NAME, orderForFulfillment)).thenReturn(true);

        // act
        orderForFulfilmentProducer.produce(orderForFulfillment);

        // assert
        // no exception thrown
    }

    @Test
    void produce_givenOrderForFulfilment_messageNotProduced() {
        // arrange
        var orderForFulfillment = new OrderForFulfillment();
        orderForFulfillment.setOrderId("OL123456");
        when(streamBridge.send(ORDER_STATUS_UPDATED_BIND_NAME, orderForFulfillment)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderForFulfilmentProducer.produce(orderForFulfillment));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderForFulfillment: orderId=OL123456");
    }

}
