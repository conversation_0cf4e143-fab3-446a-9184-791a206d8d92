package com.bestseller.fulfilmentcoreservice.core.messaging.idempotency;

import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.PaymentStatusUpdatedIdempotencyCheck;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.PaymentStatusUpdatedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentStatusUpdatedIdempotencyCheckTest {

    @InjectMocks
    private PaymentStatusUpdatedIdempotencyCheck idempotencyCheck;

    @Mock
    private OrderService orderService;

    @Test
    void isDuplicate_true() {
        // arrange
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator.toPaymentStatusUpdated(
            "orderId", PaymentStatusUpdated.PaymentState.AUTHORISED);
        when(orderService.isPaymentAuthorised(paymentStatusUpdated.getOrderId())).thenReturn(true);

        // act
        var result = idempotencyCheck.isDuplicate(paymentStatusUpdated);

        // assert
        assertTrue(result);
        verify(orderService).isPaymentAuthorised(paymentStatusUpdated.getOrderId());
    }

    @Test
    void isDuplicate_false() {
        // arrange
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator.toPaymentStatusUpdated(
            "orderId", PaymentStatusUpdated.PaymentState.AUTHORISED);
        when(orderService.isPaymentAuthorised(paymentStatusUpdated.getOrderId())).thenReturn(false);

        // act
        var result = idempotencyCheck.isDuplicate(paymentStatusUpdated);

        // assert
        assertFalse(result);
        verify(orderService).isPaymentAuthorised(paymentStatusUpdated.getOrderId());
    }
}
