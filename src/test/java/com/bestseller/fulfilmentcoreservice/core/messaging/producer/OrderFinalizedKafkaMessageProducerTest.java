package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderFinalizedKafkaMessageProducerTest {
    private static final String FINALIZED_BIND_NAME = "orderFinalized-out-0";

    @InjectMocks
    private OrderFinalizedKafkaMessageProducer orderFinalizedKafkaMessageProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenOrderCancelled_producesMessage() {
        // arrange
        var orderFinalized = new OrderFinalized();
        when(streamBridge.send(FINALIZED_BIND_NAME, orderFinalized)).thenReturn(true);

        // act & assert
        // no exception thrown
        assertDoesNotThrow(() -> orderFinalizedKafkaMessageProducer.produce(orderFinalized));
    }

    @Test
    void produce_givenOrderCancelled_messageNotProduced() {
        // arrange
        var orderFinalized = new OrderFinalized();
        orderFinalized.setOrderId("OL123456");
        when(streamBridge.send(FINALIZED_BIND_NAME, orderFinalized)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderFinalizedKafkaMessageProducer.produce(orderFinalized));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderFinalized: orderId=OL123456");
    }
}
