package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundRequestedProducerTest {

    @InjectMocks
    private RefundRequestedKafkaMessageProducer refundRequestedProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenMessage_producesMessage() {
        // arrange
        var orderReturnedInStore = new OrderReturnedInStore();
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(orderReturnedInStore))).thenReturn(true);

        // act & assert
        assertDoesNotThrow(() -> refundRequestedProducer.produce(orderReturnedInStore));
    }

    @Test
    void produce_givenMessageAndStreamBridgeCannotProduce_messageNotProduced() {
        // arrange
        var orderReturnedInStore = new OrderReturnedInStore();
        orderReturnedInStore.setOrderId("TB123456");
        orderReturnedInStore.setReturnId(1L);
        when(streamBridge.send(Mockito.anyString(), Mockito.eq(orderReturnedInStore))).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> refundRequestedProducer.produce(orderReturnedInStore));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderReturnedInStore: "
                + "orderId=TB123456, returnId=1");
    }
}
