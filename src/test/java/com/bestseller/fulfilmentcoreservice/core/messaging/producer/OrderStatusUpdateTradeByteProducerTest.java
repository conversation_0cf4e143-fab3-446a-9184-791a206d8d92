package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderStatusUpdateTradeByteProducerTest {
    private static final String ORDER_STATUS_UPDATE_TRADE_BYTE_BIND_NAME = "orderStatusUpdateTradeByte-out-0";

    @InjectMocks
    private OrderStatusUpdateTradebyteKafkaMessageProducer orderStatusUpdateTradeByteProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenOrderStatusUpdateTradeByteProducer_producesMessage() {
        // arrange
        var orderStatusUpdateTradebyte = new OrderStatusUpdateTradebyte();
        when(streamBridge.send(ORDER_STATUS_UPDATE_TRADE_BYTE_BIND_NAME, orderStatusUpdateTradebyte)).thenReturn(true);

        // act
        orderStatusUpdateTradeByteProducer.produce(orderStatusUpdateTradebyte);

        // assert
        // no exception thrown
    }

    @Test
    void produce_givenOrderStatusUpdateTradeByteProducer_messageNotProduced() {
        // arrange
        var orderStatusUpdateTradebyte = new OrderStatusUpdateTradebyte();
        orderStatusUpdateTradebyte.setOrderId("TB123456");
        when(streamBridge.send(ORDER_STATUS_UPDATE_TRADE_BYTE_BIND_NAME, orderStatusUpdateTradebyte)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderStatusUpdateTradeByteProducer.produce(orderStatusUpdateTradebyte));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderStatusUpdateTradebyte: orderId=TB123456");
    }

}
