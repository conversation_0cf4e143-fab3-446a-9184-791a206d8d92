package com.bestseller.fulfilmentcoreservice.core.messaging.idempotency;

import com.bestseller.fulfilmentcoreservice.core.messaging.validator.idempotency.OrderPartsRoutedIdempotencyCheck;
import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsRoutedGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPartsRoutedIdempotencyCheckTest {

    @InjectMocks
    private OrderPartsRoutedIdempotencyCheck idempotencyCheck;

    @Mock
    private OrderService orderService;

    @Test
    void isDuplicate_givenDuplicateOrder_returnsTrue() {
        // arrange
        var orderPartsRouted = OrderPartsRoutedGenerator.generateOrderPartsRouted("orderId");

        when(orderService.isDuplicateRequest(orderPartsRouted)).thenReturn(true);

        // act
        var result = idempotencyCheck.isDuplicate(orderPartsRouted);

        // assert
        assertTrue(result);
    }

    @Test
    void isDuplicate_givenNonDuplicateOrder_returnsFalse() {
        // arrange
        var orderPartsRouted = OrderPartsRoutedGenerator.generateOrderPartsRouted("orderId");

        when(orderService.isDuplicateRequest(orderPartsRouted)).thenReturn(false);

        // act
        var result = idempotencyCheck.isDuplicate(orderPartsRouted);

        // assert
        assertFalse(result);
    }

}
