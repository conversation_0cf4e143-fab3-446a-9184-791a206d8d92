package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.fulfilmentcoreservice.core.service.ValidOrderPlacedService;
import com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedConsumerTest {

    @Mock
    private ValidOrderPlacedService validOrderPlacedService;

    @InjectMocks
    private ValidOrderPlacedConsumer consumer;

    @Test
    void consume_givenValidOrder_success() {
        // arrange
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");

        // act
        consumer.consume(message);

        // assert
        verify(validOrderPlacedService, times(1)).process(any());
    }

    @Test
    void consume_givenBlankBrand_success() {
        // arrange
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");
        message.getOrderDetails().setBrandedShipping("");

        // act
        consumer.consume(message);

        // assert
        verify(validOrderPlacedService, times(1)).process(any());
    }

    @Test
    void getMessageKey_givenValidOrderPlaced_orderIdIsReturned() {
        // arrange
        var message = new ValidOrderPlaced().withOrderId("12345");

        // act
        var messageKey = consumer.getMessageKey(message);

        // assert
        assertThat(messageKey).isEqualTo(message.getOrderId());
    }
}
