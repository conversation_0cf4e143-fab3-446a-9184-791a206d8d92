package com.bestseller.fulfilmentcoreservice.core.messaging.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderCancelledKafkaMessageProducerTest {
    private static final String ORDER_CANCELLED_BIND_NAME = "orderCancelled-out-0";

    @InjectMocks
    private OrderCancelledKafkaMessageProducer orderCancelledKafkaMessageProducer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenOrderCancelled_producesMessage() {
        // arrange
        var orderCancelled = new OrderCancelled();
        when(streamBridge.send(ORDER_CANCELLED_BIND_NAME, orderCancelled)).thenReturn(true);

        // act & assert
        // no exception thrown
        assertDoesNotThrow(() -> orderCancelledKafkaMessageProducer.produce(orderCancelled));
    }

    @Test
    void produce_givenOrderCancelled_messageNotProduced() {
        // arrange
        var orderCancelled = new OrderCancelled();
        orderCancelled.setOrderId("OL123456");
        when(streamBridge.send(ORDER_CANCELLED_BIND_NAME, orderCancelled)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class,
            () -> orderCancelledKafkaMessageProducer.produce(orderCancelled));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic OrderCancelled: orderId=OL123456");
    }
}
