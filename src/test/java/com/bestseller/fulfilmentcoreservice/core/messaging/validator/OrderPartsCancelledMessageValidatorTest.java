package com.bestseller.fulfilmentcoreservice.core.messaging.validator;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsCancelledGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import jakarta.validation.Validator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPartsCancelledMessageValidatorTest {

    @InjectMocks
    private OrderPartsCancelledMessageValidator orderPartsCancelledMessageValidator;

    @Mock
    private OrderService orderService;

    @Mock
    private Validator validator;

    @Test
    void passesCustomValidation_givenValidMessage_returnsTrue() {
        // arrange
        var orderPartsCancelled = OrderPartsCancelledGenerator.generateOrderPartsCancelled("orderId");
        when(orderService.findByIdWithOrderLines(orderPartsCancelled.getOrderId())).thenReturn(OrderGenerator.createOrder("orderId"));
        when(validator.validate(any(), any(Class[].class))).thenReturn(Set.of());

        // act
        var result = orderPartsCancelledMessageValidator.passesCustomValidation(orderPartsCancelled);

        // assert
        assertTrue(result);
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void passesCustomValidation_givenMessageWithMoreCancelledQuantityThanAvailableQuantity_throwIllegalException() {
        // arrange
        var orderPartsCancelled = OrderPartsCancelledGenerator.generateOrderPartsCancelled("orderId");
        orderPartsCancelled.getOrderLines().stream().findFirst().get().setQuantity(5);
        when(orderService.findByIdWithOrderLines(orderPartsCancelled.getOrderId())).thenReturn(OrderGenerator.createOrder("orderId"));
        when(validator.validate(any(), any(Class[].class))).thenReturn(Set.of());

        // act and assert
        assertThrows(IllegalArgumentException.class,
            () -> orderPartsCancelledMessageValidator.passesCustomValidation(orderPartsCancelled));
    }

    @Test
    void isValid_givenMissingCancellationDateMessage_returnsFalse() {
        // arrange
        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId("orderId")
            .withWarehouse("INGRAM_MICRO")
            .withOrderLines(List.of(
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                    .withEan("EAN_1")
                    .withLineNumber(1)
                    .withCancelReason(CancelReason.MANUAL_CANCELLATION.name())
                    .withQuantity(1)));

        // act
        var result = orderPartsCancelledMessageValidator.isValid(orderPartsCancelled);

        // assert
        assertFalse(result);
        verifyNoInteractions(orderService);
    }

}
