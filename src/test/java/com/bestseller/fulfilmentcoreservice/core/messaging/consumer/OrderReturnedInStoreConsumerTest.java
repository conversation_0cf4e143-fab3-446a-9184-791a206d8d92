package com.bestseller.fulfilmentcoreservice.core.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReturnedInStoreConsumerTest {

    @InjectMocks
    private OrderReturnedInStoreConsumer consumer;

    @Mock
    private OrderReturnedInStore orderReturnedInStore;

    @Test
    void testGetMessageKey() {
        // Arrange
        String expectedOrderId = "ORDER123";
        when(orderReturnedInStore.getOrderId()).thenReturn(expectedOrderId);

        // Act
        String messageKey = consumer.getMessageKey(orderReturnedInStore);

        // Assert
        assertEquals(expectedOrderId, messageKey);
    }
}
