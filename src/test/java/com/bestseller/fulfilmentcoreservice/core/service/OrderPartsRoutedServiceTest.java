package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderPartsRoutedStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsRoutedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPartsRoutedServiceTest {

    @InjectMocks
    private OrderPartsRoutedServiceImpl orderPartsRoutedService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderFulfilmentPartService orderFulfillmentPartService;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private OrderPartsRoutedStrategy orderPartsRoutedStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    @BeforeEach
    public void setUp() {
        when(orderPartsRoutedStrategy.getMessageType()).thenReturn(OrderPartsRouted.class);
        context = new OrderStateChangeContext(List.of(orderPartsRoutedStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void updateOrderStatusAndGenerateOrderFulfillmentPart() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsRouted = OrderPartsRoutedGenerator.generateOrderPartsRouted(orderId);
        order.setOrderLines(List.of(
            OrderLine.builder().ecomId(UUID.randomUUID()).orderLineQtyStatus(
                List.of(OrderLineQtyStatus.builder()
                    .orderState(OrderState.PLACED)
                    .build())).build()));

        var routedPayload = DefaultOrderStatusUpdatedPayload
            .builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(UUID.randomUUID())
                        .states(List.of(OrderStatusUpdated.Type.ROUTED.name()))
                        .build()
                )
            )
            .build();

        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .build();
        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(orderId)
            .withPayload(routedPayload)
            .withType(OrderStatusUpdated.Type.ROUTED);

        when(orderService.findById(orderId)).thenReturn(order);
        when(orderFulfillmentPartConverter.toOrderFulfillmentPartModel(any(), any(OrderPartsRouted.class)))
            .thenReturn(orderFulfillmentPartModel);
        when(orderStatusUpdatedConverter.convertTo(any(), any(), any())).thenReturn(orderStatusUpdated);
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(routedPayload);

        // act
        orderPartsRoutedService.process(orderPartsRouted);

        // assert
        verify(orderStateService).apply(any(), any());
        verify(orderFulfillmentPartService, times(1)).handleOrderFulfilmentParts(
            any(), any(OrderFulfilmentPartModel.class));
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
    }

    @Test
    void updateOrderStatusAndGenerateOrderFulfillmentPart_whenApplyThrowEx() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsRouted = OrderPartsRoutedGenerator.generateOrderPartsRouted(orderId);
        when(orderService.findById(orderId)).thenReturn(order);
        doThrow(StateTransitionException.class).when(orderStateService).apply(any(), any());

        // act & assert
        assertThrows(StateTransitionException.class,
            () -> orderPartsRoutedService.process(orderPartsRouted));
    }

    @Test
    void updateOrderStatusAndGenerateOrderFulfillmentPart_whenThrowEx() {
        // arrange
        var orderId = "OL12212";
        var orderPartsRouted = OrderPartsRoutedGenerator.generateOrderPartsRouted(orderId);
        when(orderService.findById(orderId)).thenThrow(OrderNotFoundException.class);

        // act & assert
        Assert.assertThrows(OrderNotFoundException.class, () ->
            orderPartsRoutedService.process(orderPartsRouted));
    }
}
