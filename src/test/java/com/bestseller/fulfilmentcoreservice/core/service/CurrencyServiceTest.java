package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@ExtendWith(MockitoExtension.class)
class CurrencyServiceTest {

    @InjectMocks
    private CurrencyServiceImpl currencyService;

    @Test
    void getCurrency_givenTradebyteOrder_willReturnCurrencyFromPartnerChannel() {
        // arrange
        String currency = "DKK";
        var order = Order.builder()
            .platform(Platform.TRADEBYTE)
            .partnerChannel(PartnerChannel.builder()
                .currency(currency)
                .build())
            .build();
        // act & assert
        assertThat(currencyService.getCurrency(order, null))
            .as("Currency should be returned from PartnerChannel")
            .isEqualTo(currency);
    }

    @Test
    void getCurrency_givenDemandwareOrder_willReturnCurrencyFromPayment() {
        // arrange
        String currency = "DKK";
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .build();
        // act & assert
        assertThat(currencyService.getCurrency(order, currency))
            .as("Currency should be returned from payment")
            .isEqualTo(currency);
    }

    @Test
    void getCurrency_givenDemandwareOrderWithoutCurrency_willReturnDefaultCurrency() {
        // arrange
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .build();
        // act & assert
        assertThat(currencyService.getCurrency(order, null))
            .as("Default currency should be returned")
            .isEqualTo("EUR");
    }

}
