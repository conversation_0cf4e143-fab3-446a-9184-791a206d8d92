package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.logistics.statetransition.OrderState;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.UUID;

import static com.logistics.statetransition.OrderState.DISPATCHED;
import static com.logistics.statetransition.OrderState.RETURNED;
import static com.logistics.statetransition.OrderState.RETURN_PROCESSED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderRefundServiceTest {

    @InjectMocks
    private OrderRefundServiceImpl orderRefundService;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private QueueProducer<RefundCreationRequested> refundCreationRequestedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private RefundShipmentFeeService refundShipmentFeeService;

    @Mock
    private ChargeReturnFeeService chargeReturnFeeService;

    @Mock
    private QueueProducer<OrderReturned> orderReturnedProducerQueueProducer;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private OrderStatusUpdateInfoRepository orderStatusUpdateInfoRepository;

    @Mock
    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService = new StateChangeServiceImpl(context);

    @Captor
    private ArgumentCaptor<List<StateChange>> stateChangeCaptor;

    @Captor
    private ArgumentCaptor<EnqueueParams<RefundCreationRequested>> refundCreationRequestedEnqueueParamsCaptor;

    @Captor
    private ArgumentCaptor<EnqueueParams<OrderReturned>> orderReturnedEnqueueParamsCaptor;

    @Captor
    private ArgumentCaptor<Order> orderArgumentCaptor;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means QueueProducer&lt;RefundCreationRequested&gt;, QueueProducer&lt;OrderStatusUpdated&gt;
     * and QueueProducer&lt;OrderReturned&gt; are seen as the same QueueProducer class at runtime.
     * To fix it distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(
            orderRefundService,
            "orderReturnedProducerQueueProducer",
            orderReturnedProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            orderRefundService,
            "refundCreationRequestedProducerQueueProducer",
            refundCreationRequestedProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            orderRefundService,
            "orderStatusUpdatedProducerQueueProducer",
            orderStatusUpdatedProducerQueueProducer
        );
    }

    @Test
    void processRefund_givenValidOrder_refundMessageIsProduced() {
        // arrange
        String orderId = "123";
        String ean1 = "12234134123123";
        String ean2 = "73816278361278";
        OrderLineQtyStatus quantityStatus = OrderLineQtyStatus.builder()
            .orderState(RETURNED)
            .orderStatusUpdateInfo(new OrderStatusUpdateInfo())
            .build();
        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(
                OrderLine.builder()
                    .virtualProduct(true)
                    .build(),
                OrderLine.builder()
                    .bonusProduct(true)
                    .build(),
                OrderLine.builder()
                    .bonusProduct(true)
                    .virtualProduct(true)
                    .build(),
                OrderLine.builder()
                    .ean(ean1)
                    .orderLineQtyStatus(List.of(
                        quantityStatus,
                        quantityStatus,
                        OrderLineQtyStatus.builder()
                            .orderState(DISPATCHED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(RETURN_PROCESSED)
                            .build()
                    ))
                    .build(),
                OrderLine.builder()
                    .ean(ean2)
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(DISPATCHED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(DISPATCHED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(RETURN_PROCESSED)
                            .build()
                    ))
                    .build()
            ))
            .build();

        when(orderRepository.getReferenceById(orderId)).thenReturn(order);

        int returnedItems = 2;
        when(stateChangeService.createCopiesOfStateChange(ean1, returnedItems, OrderState.RETURN_PROCESSED))
            .thenCallRealMethod();
        doNothing().when(orderStateService).apply(eq(order), stateChangeCaptor.capture());
        when(orderRepository.save(orderArgumentCaptor.capture())).thenReturn(null);
        when(orderStatusUpdatedConverter.convertTo(eq(order), any(), any())).thenReturn(new OrderStatusUpdated());

        // act
        orderRefundService.processRefund(orderId);

        // assert
        assertThat(stateChangeCaptor.getValue())
            .hasSize(2)
            .extracting("orderState", "ean")
            .containsExactly(
                Tuple.tuple(OrderState.RETURN_PROCESSED, ean1),
                Tuple.tuple(OrderState.RETURN_PROCESSED, ean1)
            );

        verify(refundCreationRequestedProducerQueueProducer)
            .enqueue(refundCreationRequestedEnqueueParamsCaptor.capture());
        var refundCreationRequested = refundCreationRequestedEnqueueParamsCaptor.getValue().getPayload();
        assertThat(refundCreationRequested)
            .usingRecursiveComparison()
            .ignoringFields("refundCreationRequestedId")
            .isEqualTo(new RefundCreationRequested()
                .withOrderId(orderId)
                .withChargeReturnFee(false)
                .withRefundShippingFee(false)
                .withItemsToRefund(List.of(new ItemsToRefund()
                    .withEan(ean1)
                    .withQuantity(returnedItems)
                ))
            );

        assertThat(refundCreationRequested.getRefundCreationRequestedId())
            .isNotNull();

        verify(orderReturnedProducerQueueProducer).enqueue(orderReturnedEnqueueParamsCaptor.capture());
        assertThat(orderReturnedEnqueueParamsCaptor.getValue().getPayload())
            .usingRecursiveComparison()
            .isEqualTo(
                new OrderReturned()
                    .withOrderId(orderId)
                    .withOrderLines(List.of(
                        new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturned.OrderLine()
                            .withEan(ean1)
                            .withQuantity(returnedItems + 1)
                    ))
            );

        quantityStatus.getOrderStatusUpdateInfo().setRefundRequestId(
            refundCreationRequested.getRefundCreationRequestedId()
        );

        assertThat(orderArgumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(order);
    }

    /**
     * Complementary scenario.
     * When order has the return fee already charged, the Order.returnFeeCharged should not change.
     */
    @Test
    void processRefund_givenValidOrderAlreadyReturnFeeCharged_refundMessageIsProduced() {
        // arrange
        String orderId = "123";
        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of())
            .returnFeeCharged(true)
            .build();

        when(orderRepository.getReferenceById(orderId)).thenReturn(order);

        when(orderRepository.save(orderArgumentCaptor.capture())).thenReturn(null);
        when(orderStatusUpdatedConverter.convertTo(eq(order), any(), any())).thenReturn(new OrderStatusUpdated());

        // act
        orderRefundService.processRefund(orderId);

        // assert
        assertThat(orderArgumentCaptor.getValue().isReturnFeeCharged()).isTrue();
    }

    /**
     * Complementary scenario.
     * When order has the return fee not charged, the Order.returnFeeCharged should change due to charge service.
     */
    @Test
    void processRefund_givenValidOrderReturnFeeNotCharged_refundMessageIsProduced() {
        // arrange
        String orderId = "123";
        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of())
            .build();

        when(orderRepository.getReferenceById(orderId)).thenReturn(order);

        when(orderRepository.save(orderArgumentCaptor.capture())).thenReturn(null);
        when(chargeReturnFeeService.shouldChargeReturnFee(order)).thenReturn(true);
        when(orderStatusUpdatedConverter.convertTo(eq(order), any(), any())).thenReturn(new OrderStatusUpdated());

        // act
        orderRefundService.processRefund(orderId);

        // assert
        assertThat(orderArgumentCaptor.getValue().isReturnFeeCharged()).isTrue();
    }

    @Test
    void getReturnReasonCodes_givenRefundRequestIds_returnEmptyListReasonCodes() {
        // arrange
        String refundRequestId = UUID.randomUUID().toString();

        when(orderStatusUpdateInfoRepository
            .findAllCustomerReturnReasonCodesByRefundRequestId(any())).thenReturn(List.of());

        // act
        var result = orderRefundService.getReturnReasonCodes(refundRequestId);

        // assert
        verify(orderStatusUpdateInfoRepository, times(1))
            .findAllCustomerReturnReasonCodesByRefundRequestId(any());

    }

}
