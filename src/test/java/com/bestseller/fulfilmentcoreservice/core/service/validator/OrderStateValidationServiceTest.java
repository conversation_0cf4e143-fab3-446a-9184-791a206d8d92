package com.bestseller.fulfilmentcoreservice.core.service.validator;

import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.logistics.statetransition.OrderState.PLACED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@SpringBootTest
@ContextConfiguration(classes = {OrderStateValidationServiceImpl.class, ValidationAutoConfiguration.class})
class OrderStateValidationServiceTest {

    private static final String EAN_3 = "EAN3";
    private static final String EAN_4 = "EAN4";
    private static final String EAN_5 = "EAN5";
    private static final String EAN_6 = "EAN6";
    private static final String EAN_1 = "EAN1";
    private static final String EAN_2 = "EAN2";

    @Autowired
    private OrderStateValidationService orderStateValidationService;

    @Test
    void validate_givenNullOrder_exceptionShouldBeThrown() {
        // arrange

        // act & assert
        var exception = assertThrows(
            ConstraintViolationException.class,
            () -> orderStateValidationService.validate(null, null));
        assertThat(exception)
            .as("Error message is correct")
            .hasMessageContaining("validate.order: Order must not be null")
            .hasMessageContaining("validate.changes: Order Changes must not be empty")
            .hasMessageContaining("validate.changes: Order Changes must not be null");
    }

    @Test
    void validate_givenNullChange_exceptionShouldBeThrown() {
        // arrange
        var orderId = "OL1234";
        var order = Order.builder()
            .orderId(orderId)
            .build();

        // act & assert
        var exception = assertThrows(
            ConstraintViolationException.class,
            () -> orderStateValidationService.validate(order, null));
        assertThat(exception)
            .as("Error message is correct")
            .hasMessageContaining("validate.changes: Order Changes must not be empty")
            .hasMessageContaining("validate.changes: Order Changes must not be null");
    }

    @Test
    void validate_givenEmptyChange_exceptionShouldBeThrown() {
        // arrange
        var orderId = "OL12345";
        var order = Order.builder()
            .orderId(orderId)
            .build();

        // act & assert
        var exception = assertThrows(
            ConstraintViolationException.class,
            () -> orderStateValidationService.validate(order, Map.of()));
        assertThat(exception)
            .as("Error message is correct")
            .hasMessageContaining("validate.changes: Order Changes must not be empty");
    }

    @Test
    void validate_givenEmptyOrderLines_exceptionShouldBeThrown() {
        // arrange
        var orderId = "OL123456";
        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of())
            .build();

        // act & assert
        var exception = assertThrows(
            StateTransitionException.class,
            () -> orderStateValidationService.validate(order, Map.of("EAN", List.of())));
        assertThat(exception)
            .as("Error message is correct")
            .hasMessageContaining("Order: OL123456 | Order line list is empty!");
    }

    @Test
    void validate_givenSomeOrderLines_noExceptionShouldBeThrown() {
        // arrange
        var orderId = "OL1234567";
        var changes = Stream.of(StateChange.builder()
                .ean(EAN_3)
                .orderState(PLACED)
                .build(),
            StateChange.builder()
                .ean(EAN_4)
                .orderState(PLACED)
                .build(),
            StateChange.builder()
                .ean(EAN_5)
                .orderState(PLACED)
                .build(),
            StateChange.builder()
                .ean(EAN_6)
                .orderState(PLACED)
                .build()
        ).collect(Collectors.groupingBy(StateChange::getEan));
        var orderLine1 = OrderLine.builder()
            .ean(EAN_1)
            .build();
        var orderLine2 = OrderLine.builder()
            .ean(EAN_2)
            .originalQty(1)
            .build();
        var orderLine3 = OrderLine.builder()
            .ean(EAN_3)
            .build();
        var orderLine4 = OrderLine.builder()
            .ean(EAN_4)
            .build();
        var orderLine5 = OrderLine.builder()
            .ean(EAN_5)
            .originalQty(1)
            .build();

        final int originalQtyForOrderLine6 = 10;
        var orderLine6 = OrderLine.builder()
            .ean(EAN_6)
            .originalQty(originalQtyForOrderLine6)
            .build();
        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine1,
                orderLine2,
                orderLine3,
                orderLine4,
                orderLine5,
                orderLine6))
            .build();

        // act & assert
        orderStateValidationService.validate(order, changes);
    }

    @Test
    void validate_givenSomeOrderLines_exceptionShouldBeThrown() {
        // arrange
        var orderId = "OL1234568";
        var changes = Stream.of(StateChange.builder()
                .ean(EAN_1)
                .orderState(PLACED)
                .build(),
            StateChange.builder()
                .ean(EAN_2)
                .orderState(PLACED)
                .build()
        ).collect(Collectors.groupingBy(StateChange::getEan));
        var orderLine1 = OrderLine.builder()
            .ean(EAN_1)
            .build();

        var order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine1))
            .build();

        // act & assert
        var exception = assertThrows(StateTransitionException.class,
            () -> orderStateValidationService.validate(order, changes));
        assertThat(exception)
            .as("Error message is correct")
            .hasMessageContaining(
                "Order: OL1234568, invalid EANs: [EAN2] | The applied changes contain EANs which "
                    + "do not belong to this order's lines.");
    }

}
