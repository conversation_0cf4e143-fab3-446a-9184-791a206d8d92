package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderReturnedInStoreGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.logistics.statetransition.OrderState;
import edu.emory.mathcs.backport.java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class OrderReturnedInStoreStrategyTest {

    @InjectMocks
    private OrderReturnedInStoreStrategy orderReturnedInStoreStrategy;

    @Mock
    private OrderService orderService;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();
        var returnItem = orderReturnedInStore.getReturnItems().getFirst().getOrderLine();
        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(OrderReturnedInStoreGenerator.ORDER_LINE_EAN_1)
                    .lineNumber(1)
                    .openQty(returnItem.getQuantity())
                    .orderLineQtyStatus(Collections.nCopies(returnItem.getQuantity(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()
            ))
            .build();

        // act
        List<OrderStatusUpdateStateChange> result =
            orderReturnedInStoreStrategy.defineOrderStateChange(orderReturnedInStore, order);

        // assert
        assertThat(result).isNotEmpty();
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order returned in store.
     * <p>
     * It will return the same amount of open quantity.
     */
    @Test
    void defineStateChangeFor_givenOrderReturnedInStore_shouldReturnAllStatesInReturned() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();
        var returnItem = orderReturnedInStore.getReturnItems().getFirst().getOrderLine();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(returnItem.getEan())
                    .lineNumber(1)
                    .openQty(returnItem.getQuantity())
                    .orderLineQtyStatus(Collections.nCopies(returnItem.getQuantity(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()
            ))
            .build();

        // act
        var result = orderReturnedInStoreStrategy.defineOrderStateChange(orderReturnedInStore, order);

        // assert
        assertThat(result)
            .hasSize(returnItem.getQuantity());

        result.forEach(item ->
            assertThat(item)
                .extracting("orderState", "ean",
                    "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                .containsExactly(
                    OrderState.POS_RETURNED_IN_STORE,
                    OrderReturnedInStoreGenerator.ORDER_LINE_EAN_1,
                    ReturnType.CUSTOMER_RETURN,
                    CustomerReturnReason.RETURNED_IN_STORE_35)
        );
    }

    /**
     * This test is verifying if the state change with multiple return items is correctly defined
     *   for a given order returned in store.
     * <p>
     * It will return the same amount of open quantity.
     */
    @Test
    void defineStateChangeFor_givenOrderReturnedInStore_withMultipleReturnItems_shouldReturnAllStatesInReturned() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generateMultiReturnItems();

        var returnedOrderLine1 = orderReturnedInStore.getReturnItems().getFirst().getOrderLine();
        var returnedOrderLine2 = orderReturnedInStore.getReturnItems().get(1).getOrderLine();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(returnedOrderLine1.getEan())
                    .lineNumber(1)
                    .openQty(returnedOrderLine1.getQuantity())
                    .orderLineQtyStatus(Collections.nCopies(returnedOrderLine1.getQuantity(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build(),
                OrderLine.builder()
                    .ean(returnedOrderLine2.getEan())
                    .lineNumber(2)
                    .openQty(returnedOrderLine2.getQuantity())
                    .orderLineQtyStatus(Collections.nCopies(returnedOrderLine2.getQuantity(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()
            ))
            .build();

        // act
        var result = orderReturnedInStoreStrategy.defineOrderStateChange(orderReturnedInStore, order);

        // assert
        assertThat(result)
            .hasSize(returnedOrderLine1.getQuantity() + returnedOrderLine2.getQuantity());

        result.forEach(item ->
            assertThat(item)
                .extracting("orderState", "ean",
                    "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                .containsExactly(OrderState.POS_RETURNED_IN_STORE, item.getEan(), ReturnType.CUSTOMER_RETURN,
                    CustomerReturnReason.RETURNED_IN_STORE_35)
        );
    }

    /**
     * This test is verifying if the ean not found it throws exception for a given order returned in store.
     * <p>
     * It will throw exception because the item (EAN) couldn't be found.
     */
    @Test
    void defineStateChangeFor_givenInvalidOrderReturnedInStore_exceptionIsThrown() {
        final String invalidEan = "invalidEan";

        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();
        var returnItem = orderReturnedInStore.getReturnItems().getFirst().getOrderLine();
        returnItem.setEan(invalidEan);

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("EAN1")
                    .lineNumber(1)
                    .openQty(returnItem.getQuantity())
                    .orderLineQtyStatus(Collections.nCopies(returnItem.getQuantity(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()
            ))
            .build();

        // act & assert
        assertThatThrownBy(() -> {
            orderReturnedInStoreStrategy.defineOrderStateChange(orderReturnedInStore, order);
        })
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("Order line not found for ean: %s".formatted(invalidEan));
    }
}
