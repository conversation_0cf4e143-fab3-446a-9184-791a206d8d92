package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ChannelTypeServiceTest {

    @InjectMocks
    private ChannelTypeServiceImpl channelTypeService;

    @Test
    void getChannelType_givenTradebyteOrder_willReturnDefault() {
        // arrange
        var order = Order.builder()
            .platform(Platform.TRADEBYTE)
            .build();

        // act & assert
        assertThat(channelTypeService.getChannelType(order, null))
            .as("Default STOREFRONT should be returned")
            .isEqualTo(ChannelType.STOREFRONT);
    }

    @Test
    void getChannelType_givenDemandwareOrder_willReturnValidValue() {
        // arrange
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(channelTypeService.getChannelType(order, ChannelType.MOBILE.getDemandwareOrderType()))
            .as("MOBILE should be returned")
            .isEqualTo(ChannelType.MOBILE);
    }

}
