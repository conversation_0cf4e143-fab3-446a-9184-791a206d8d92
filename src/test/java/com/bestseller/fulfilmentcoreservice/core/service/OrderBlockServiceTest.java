package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderBlock;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderBlockServiceTest {

    @InjectMocks
    private OrderBlockServiceImpl orderBlockService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderFilterService orderFilterService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private ManualOrderForFulfillmentService manualOrderForFulfillmentService;

    @Captor
    private ArgumentCaptor<Order> orderCaptor;

    @Test
    void block_givenValidIdForResubmittedOrder_orderIsBlockedWithResubmittedStatus() {
        // arrange
        var orderId = "OL12212";
        var resubmitOrderState = OrderState.RESUBMIT;
        var order = Order.builder()
            .minStatus(resubmitOrderState)
            .build();
        var orderStatusUpdated = new OrderStatusUpdated();
        var orderStatusUpdatedPayload = new DefaultOrderStatusUpdatedPayload();

        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(orderStatusUpdatedPayload);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderStatusUpdatedPayload, OrderStatusUpdated.Type.BLOCKED)).thenReturn(orderStatusUpdated);

        when(orderService.save(orderCaptor.capture())).thenReturn(order);

        // act
        orderBlockService.block(orderId);

        // assert
        var orderPersisted = orderCaptor.getValue();
        assertThat(orderPersisted.getOrderBlock().getResumeState()).isEqualTo(resubmitOrderState);
        verifyNoInteractions(orderFilterService);
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
    }

    @Test
    void block_givenValidIdForAnyOrderStatuses_orderIsBlockedWithPlacedStatus() {
        // arrange
        var orderId = "OL12222";
        var dispatchedOrderState = OrderState.DISPATCHED;
        var orderBlockedState = OrderState.BLOCKED;

        var order = Order.builder()
            .minStatus(dispatchedOrderState)
            .build();
        var orderStatusUpdated = new OrderStatusUpdated();
        var orderStatusUpdatedPayload = new DefaultOrderStatusUpdatedPayload();

        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(orderStatusUpdatedPayload);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderStatusUpdatedPayload, OrderStatusUpdated.Type.BLOCKED)).thenReturn(orderStatusUpdated);

        when(orderService.save(orderCaptor.capture())).thenReturn(order);
        doNothing().when(orderStateService).applyOrderState(order, orderBlockedState);

        // act
        orderBlockService.block(orderId);

        // assert
        var orderPersisted = orderCaptor.getValue();
        assertThat(orderPersisted.getOrderBlock().getResumeState()).isEqualTo(OrderState.PLACED);
        verifyNoInteractions(orderFilterService);
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
    }

    @Test
    void block_givenNotFoundOrder_orderFilterIsCreated() {
        // arrange
        var orderId = "OL12222";

        when(orderService.findOrderById(orderId)).thenReturn(Optional.empty());

        // act
        orderBlockService.block(orderId);

        // assert
        verify(orderFilterService).createFilter(orderId);
    }

    @Test
    void unblock_givenValidIdButOrderWasNotBlockedBefore_nothingHappens() {
        // arrange
        var orderId = "OL12223";
        var order = Order.builder()
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService, never()).save(any());
        verify(orderStateService, never()).applyOrderState(any(), any());
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void unblock_givenValidIdAndMinStateIsNotBlocked_orderBlockBecomesEmpty() {
        // arrange
        var orderId = "OL12223";
        var order = Order.builder()
            .minStatus(OrderState.BEGIN)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(OrderState.BEGIN)
                    .build()
            )
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService).save(order);
        verify(orderStateService, never()).applyOrderState(any(), any());
        assertThat(order.getOrderBlock()).isNull();
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void unblock_givenValidIdAndMinStateIsBlockedAndOrderBlockStateIsNull_orderBlockBecomesEmpty() {
        // arrange
        var orderId = "OL12223";
        var order = Order.builder()
            .minStatus(OrderState.BLOCKED)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(OrderState.PLACED)
                    .build()
            )
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService).save(order);
        verify(orderStateService).applyOrderState(order, OrderState.PLACED);
        assertThat(order.getOrderBlock()).isNull();
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void unblock_givenValidIdAndMinStateIsBlockedAndOrderBlockStateIsNotNullButNotResubmit_orderBlockBecomesEmpty() {
        // arrange
        var orderId = "OL12223";
        var order = Order.builder()
            .minStatus(OrderState.BLOCKED)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(OrderState.BEGIN)
                    .build()
            )
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService).save(order);
        verify(orderStateService).applyOrderState(order, OrderState.PLACED);
        assertThat(order.getOrderBlock()).isNull();
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void unblock_givenValidIdAndMinStateIsBlockedAndOrderBlockStateIsNotNullAndResubmit_orderBlockBecomesEmpty() {
        // arrange
        var orderId = "OL12223";
        var resubmitState = OrderState.RESUBMIT;
        var order = Order.builder()
            .minStatus(OrderState.BLOCKED)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(resubmitState)
                    .build()
            )
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService).save(order);
        verify(orderStateService).applyOrderState(order, resubmitState);
        assertThat(order.getOrderBlock()).isNull();
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void unblock_givenOrderWithPaymentAuthorised_orderBlockBecomesEmpty() {
        // arrange
        var orderId = "OL12223";
        var order = Order.builder()
            .minStatus(OrderState.BLOCKED)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(OrderState.PLACED)
                    .build()
            )
            .orderPaymentAuthorised(true)
            .build();
        when(orderService.findOrderById(orderId)).thenReturn(Optional.of(order));

        // act
        orderBlockService.unblock(orderId);

        // assert
        verify(orderService).save(order);
        verify(orderStateService).applyOrderState(order, OrderState.PLACED);
        assertThat(order.getOrderBlock()).isNull();
        verify(manualOrderForFulfillmentService).fulfillOrder(order);
        verify(orderFilterService).removeFilter(orderId);
    }

    @Test
    void block_givenOrderIsFiltered_orderIsBlockedWithPlacedStatus() {
        // arrange
        var orderId = "OL12222";

        var order = Order.builder()
            .orderId(orderId)
            .build();

        when(orderFilterService.isFilterExist(orderId)).thenReturn(true);

        // act
        orderBlockService.applyOrderBlock(order);

        // assert
        assertThat(order.getOrderBlock().getResumeState()).isEqualTo(OrderState.PLACED);
        verify(orderStateService).applyOrderState(order, OrderState.BLOCKED);
    }

    @Test
    void block_givenOrderIsFiltered_orderIsBlockedResubmitStatus() {
        // arrange
        var orderId = "OL12222";

        var order = Order.builder()
            .minStatus(OrderState.RESUBMIT)
            .orderId(orderId)
            .build();

        when(orderFilterService.isFilterExist(orderId)).thenReturn(true);

        // act
        orderBlockService.applyOrderBlock(order);

        // assert
        assertThat(order.getOrderBlock().getResumeState()).isEqualTo(OrderState.RESUBMIT);
        verify(orderStateService).applyOrderState(order, OrderState.BLOCKED);
    }

    @Test
    void block_givenOrderIsFiltered_andOrderIsVirtual_shouldNotBlock() {
        // arrange
        var orderId = "OL12222";

        var order = Order.builder()
            .orderId(orderId)
            .shippingMethod(ShippingMethod.VIRTUAL)
            .build();

        when(orderFilterService.isFilterExist(orderId)).thenReturn(true);

        // act
        orderBlockService.applyOrderBlock(order);

        // assert
        assertThat(order.getOrderBlock()).isNull();
        verify(orderStateService, never()).applyOrderState(order, OrderState.BLOCKED);
    }

    @Test
    void block_givenOrderIsNotFiltered_andOrderIsStandard_shouldNotBlock() {
        // arrange
        var orderId = "OL12222";

        var order = Order.builder()
            .orderId(orderId)
            .shippingMethod(ShippingMethod.STANDARD)
            .build();

        when(orderFilterService.isFilterExist(orderId)).thenReturn(false);

        // act
        orderBlockService.applyOrderBlock(order);

        // assert
        assertThat(order.getOrderBlock()).isNull();
        verify(orderStateService, never()).applyOrderState(order, OrderState.BLOCKED);
    }
}
