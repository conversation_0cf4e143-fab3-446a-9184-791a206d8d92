package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(MockitoExtension.class)
class OrderLineAcknowledgedStrategyTest {

    @InjectMocks
    private OrderLineAcknowledgedStrategy orderLineAcknowledgedStrategy;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderLine = OrderLine.builder()
            .ean("5715423786800")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();

        var orderLineAcknowledged = new OrderLineAcknowledged();
        orderLineAcknowledged.setOrderId(order.getOrderId());
        orderLineAcknowledged.setEan(orderLine.getEan());
        orderLineAcknowledged.setQuantity(orderLine.getOriginalQty());
        orderLineAcknowledged.setAcknowledgementDate(ZonedDateTime.now());

        // act
        List<OrderStatusUpdateStateChange> result =
            orderLineAcknowledgedStrategy.defineOrderStateChange(orderLineAcknowledged, order);

        // assert
        assertThat(result).isNotEmpty();
    }

    @Test
    public void testExecuteStrategy_whenEan_doesNotExists() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);

        var orderLineAcknowledged = new OrderLineAcknowledged();
        orderLineAcknowledged.setOrderId(order.getOrderId());
        orderLineAcknowledged.setEan("wrongEan");
        orderLineAcknowledged.setQuantity(1);
        orderLineAcknowledged.setAcknowledgementDate(ZonedDateTime.now());

        //  act & assert
        var exception = assertThrows(
            IllegalArgumentException.class,
            () -> orderLineAcknowledgedStrategy.defineOrderStateChange(orderLineAcknowledged, order));
        assertThat(exception)
            .hasMessage("EAN wrongEan not found in order lines for orderId OL12212");
    }
}
