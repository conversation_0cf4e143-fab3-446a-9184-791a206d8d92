package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderStatusUpdateInfoRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderPartsRoutedStrategyTest {

    @InjectMocks
    private OrderPartsRoutedStrategy orderPartsCancelledStrategy;

    @Mock
    private OrderStatusUpdateInfoRepository repository;

    @Mock
    private OrderService orderService;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsRouted = new OrderPartsRouted()
            .withOrderLines(List.of(
                new OrderLine()
                    .withEan("5715423786800")
                    .withLineNumber(1)
                    .withQuantity(2)));

        // act
        List<OrderStatusUpdateStateChange> result =
            orderPartsCancelledStrategy.defineOrderStateChange(orderPartsRouted, order);

        // assert
        assertThat(result).isNotEmpty();
    }
}
