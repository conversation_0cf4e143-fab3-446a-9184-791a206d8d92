package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderReturnInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.EcomCountry;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundShipmentFeeServiceTest {

    @InjectMocks
    private RefundShipmentFeeServiceImpl refundShipmentFeeService;

    @Mock
    private OrderReturnInfoService orderReturnInfoService;

    @BeforeEach
    void setup() {
        final int freeReturnPolicy = 28;
        ReflectionTestUtils.setField(refundShipmentFeeService, "daysForFreeReturn", freeReturnPolicy);
        ReflectionTestUtils.setField(refundShipmentFeeService, "countriesExcludedFromRefundShipmentFee",
            Set.of(EcomCountry.US.name()));
    }

    @Test
    void shouldRefundShipmentFee_givenUsOrder_shouldNotRefund() {
        // arrange
        var order = Order.builder()
            .shippingAddress(Address.builder()
                .countryCode("US")
                .build())
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(OrderLineQtyStatus.builder()
                            .orderState(OrderState.ROUTING)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()))
            .build();

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isFalse();
    }

    @Test
    void shouldRefundShipmentFee_givenNotAllItemReturned_shouldNotRefund() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(OrderLineQtyStatus.builder()
                            .orderState(OrderState.ROUTING)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.POS_RETURNED_IN_STORE)
                            .build()))
                    .build()))
            .build();

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isFalse();
    }

    /**
     * Test case for the following scenario:
     * All items are returned and no return date is found in order status update
     * info. The fallback check also returns null. In this case, the shipment fee should be refunded.
     */
    @Test
    void shouldRefundShipmentFee_givenReturnDateNotPresentAtAll_shouldRefund() {
        // arrange
        var order = Order.builder()
            .orderId("orderId")
            .orderDate(ZonedDateTime.parse("2024-04-05T14:27:03.880724+02:00"))
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build()))
                    .build()))
            .build();

        when(orderReturnInfoService.getOrderReturnInfo(order.getOrderId())).thenReturn(Optional.empty());

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isTrue();
    }

    /**
     * Test case for the following scenario:
     * All items are returned and no return date is found in order status update
     * info. The fallback check returns valid date. In this case, the shipment fee should be refunded.
     */
    @Test
    void shouldRefundShipmentFee_givenReturnDateNotFoundInOrderStatusUpdateInfoFallbackIsCalled_shouldRefund() {
        // arrange
        LocalDate returnDate = LocalDate.parse("2024-04-08");
        var order = Order.builder()
            .orderId("orderId")
            .orderDate(ZonedDateTime.parse("2024-04-05T14:27:03.880724+02:00"))
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build()))
                    .build()))
            .build();

        when(orderReturnInfoService.getOrderReturnInfo(order.getOrderId()))
            .thenReturn(Optional.of(OrderReturnInfo.builder().latestReturnDate(returnDate).build()));

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isTrue();
    }


    /**
     * Test case for the following scenario:
     * All items are returned and return date is found in order status update
     * info and no dispatch date is found, for some reason, it should refund.
     */
    @Test
    void shouldRefundShipmentFee_givenReturnDateIsPresentInOrderStatusUpdateInfoAndDispatchDateNotFound_shouldRefund() {
        // arrange
        LocalDate returnDate = LocalDate.parse("2024-04-08");
        var order = Order.builder()
            .orderDate(ZonedDateTime.parse("2024-04-05T14:27:03.880724+02:00"))
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate)
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate.plusDays(1))
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build()))
                    .build()))
            .build();

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isTrue();
    }

    /**
     * Test case for the following scenario:
     * All items are returned and return date is found in order status update
     * info and dispatch date is found but after the free return policy, so should not be refunded.
     */
    @Test
    void shouldRefundShipmentFee_givenReturnDateIsLotAfterReturnPolicy_shouldNotRefund() {
        // arrange
        LocalDate returnDate = LocalDate.parse("2024-04-08");
        LocalDateTime dispatchDate = LocalDateTime.parse("2024-03-07T00:00:00");
        var order = Order.builder()
            .orderDate(ZonedDateTime.parse("2024-03-05T14:27:03.880724+02:00"))
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate)
                                .build())
                            .orderFulfillmentPart(OrderFulfillmentPart.builder()
                                .dispatchDate(dispatchDate)
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate.plusDays(1))
                                .build())
                            .orderFulfillmentPart(OrderFulfillmentPart.builder()
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build()))
                    .build(),
                OrderLine.builder()
                    .virtualProduct(true)
                    .bonusProduct(true)
                    .orderLineQtyStatus(List.of())
                    .build()))
            .build();

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isFalse();
    }

    /**
     * Test case for the following scenario:
     * All items are returned and return date is found in order status update
     * info and dispatch date is found but before the free return policy, so should be refunded.
     */
    @Test
    void shouldRefundShipmentFee_givenReturnDateIsNotAfterReturnPolicy_shouldRefund() {
        // arrange
        LocalDate returnDate = LocalDate.parse("2024-03-15");
        LocalDateTime dispatchDate = LocalDateTime.parse("2024-03-07T00:00:00");
        var order = Order.builder()
            .orderDate(ZonedDateTime.parse("2024-03-05T14:27:03.880724+02:00"))
            .orderLines(List.of(
                OrderLine.builder()
                    .orderLineQtyStatus(List.of(
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate)
                                .build())
                            .orderFulfillmentPart(OrderFulfillmentPart.builder()
                                .dispatchDate(dispatchDate)
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                                .returnDate(returnDate.plusDays(1))
                                .build())
                            .orderFulfillmentPart(OrderFulfillmentPart.builder()
                                .build())
                            .build(),
                        OrderLineQtyStatus.builder()
                            .orderState(OrderState.RETURNED)
                            .build()))
                    .build()))
            .build();

        // act & assert
        assertThat(refundShipmentFeeService.shouldRefundShipmentFee(order)).isTrue();
    }

}
