package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.TradebyteOrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.support.SimpleTransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Clock;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderStatusTradeByteExportServiceTest {
    private OrderStatusTradeByteExportServiceImpl orderStatusTradeByteExportService;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerQueueProducer;

    @Mock
    private TransactionTemplate transactionTemplate;

    @BeforeEach
    void setUp() {
        orderStatusTradeByteExportService = spy(
            new OrderStatusTradeByteExportServiceImpl(Clock.systemUTC(),
                orderService,
                orderStatusUpdateTradebyteProducerQueueProducer,
                transactionTemplate));
    }

    @SuppressWarnings("unchecked")
    @Test
    void export_givenListOfOrderIds_shouldCallExportForEachOrderId() {
        // arrange
        List<String> orderIds = List.of("TB12345", "TB67890");
        doNothing().when(orderStatusTradeByteExportService).export(anyString());


        doAnswer(invocation -> {
            var callback = invocation.getArgument(0, Consumer.class);
            callback.accept(new SimpleTransactionStatus());
            return null;
        }).when(transactionTemplate).executeWithoutResult(any());

        // act
        orderStatusTradeByteExportService.export(orderIds);

        // assert
        verify(orderStatusTradeByteExportService, times(1)).export("TB12345");
        verify(orderStatusTradeByteExportService, times(1)).export("TB67890");
    }

    @Test
    void export_givenOrderIdNotStartingWithTB_shouldNotProduceMessages() {
        // arrange
        String orderId = "OL12212";

        // act
        orderStatusTradeByteExportService.export(orderId);

        // assert
        verify(orderService, never()).findById(any());
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void export_givenOrderNotFound_shouldThrowOrderNotFoundException() {
        // arrange
        String orderId = "TB12345";
        when(orderService.findById(orderId)).thenThrow(OrderNotFoundException.class);

        // act & assert
        assertThrows(OrderNotFoundException.class, () -> orderStatusTradeByteExportService.export(orderId));
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void export_givenOrderWithMaxStatusLessThanDispatched_shouldNotProduceMessages() {
        // arrange
        String orderId = "TB12345";
        var order = OrderGenerator.createOrder("TB12345");
        setOrderStatus(order, OrderState.PROCESSING);
        when(orderService.findById("TB12345")).thenReturn(order);

        // act
        orderStatusTradeByteExportService.export(orderId);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void export_givenOrderWithDispatchedState_shouldProduceOrderStatusUpdateTradebyte() {
        // arrange
        String orderId = "TB12345";
        var order = OrderGenerator.createOrder("TB12345");
        setOrderStatus(order, OrderState.DISPATCHED);
        when(orderService.findById(orderId)).thenReturn(order);

        // act
        orderStatusTradeByteExportService.export(orderId);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(1))
            .enqueue(any(EnqueueParams.class));
    }

    @Test
    void export_givenOrderWithCancelledState_shouldProduceOrderStatusUpdateTradebyte() {
        // arrange
        String orderId = "TB12345";
        var order = OrderGenerator.createOrder("TB12345");
        setOrderStatus(order, OrderState.CANCELLED);
        when(orderService.findById(orderId)).thenReturn(order);

        // act
        orderStatusTradeByteExportService.export(orderId);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(1))
            .enqueue(any(EnqueueParams.class));
    }

    @Test
    void export_givenOrderWithReturnedState_shouldProduceTwoOrderStatusUpdateTradebyteMessages() {
        // arrange
        String orderId = "TB12345";
        var order = OrderGenerator.createOrder("TB12345");
        setOrderStatus(order, OrderState.RETURNED);
        when(orderService.findById(orderId)).thenReturn(order);

        // act
        orderStatusTradeByteExportService.export(orderId);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(2))
            .enqueue(any(EnqueueParams.class));
    }

    @Test
    void getTbOrderItemId_givenTradebyteOrderLineQtyStatus_shouldReturnTradebyteOrderLineQtyStatus() {
        // arrange
        var tradebyteOrderLineQtyStatus = TradebyteOrderLineQtyStatus.builder()
            .originalLineNumber(2)
            .build();
        var lineNumber = 1;

        // act
        var result = orderStatusTradeByteExportService.getTbOrderItemId(tradebyteOrderLineQtyStatus, lineNumber);

        // assert
        assertEquals(tradebyteOrderLineQtyStatus.getOriginalLineNumber(), result);
    }

    @Test
    void getTbOrderItemId_givenEmptyTradebyteOrderLineQtyStatus_shouldReturnLineNumber() {
        // arrange
        TradebyteOrderLineQtyStatus tradebyteOrderLineQtyStatus = null;
        var lineNumber = 1;

        // act
        var result = orderStatusTradeByteExportService.getTbOrderItemId(tradebyteOrderLineQtyStatus, lineNumber);

        // assert
        assertEquals(lineNumber, result);
    }

    @Test
    void getTbOrderItemId_givenTradebyteOrderLineQtyStatusWithNullOriginalLineNumber_shouldReturnLineNumber() {
        // arrange
        var tradebyteOrderLineQtyStatus = TradebyteOrderLineQtyStatus.builder()
            .originalLineNumber(null)
            .build();
        var lineNumber = 1;

        // act
        var result = orderStatusTradeByteExportService.getTbOrderItemId(tradebyteOrderLineQtyStatus, lineNumber);

        // assert
        assertEquals(lineNumber, result);
    }

    @Test
    void getTbOrderItemId_givenTradebyteOrderLineQtyStatusWithZeroOriginalLineNumber_shouldReturnLineNumber() {
        // arrange
        var tradebyteOrderLineQtyStatus = TradebyteOrderLineQtyStatus.builder()
            .originalLineNumber(0)
            .build();
        var lineNumber = 1;

        // act
        var result = orderStatusTradeByteExportService.getTbOrderItemId(tradebyteOrderLineQtyStatus, lineNumber);

        // assert
        assertEquals(lineNumber, result);
    }

    private static void setOrderStatus(Order order, OrderState orderState) {
        var orderLineQuantityStatus = order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst();
        orderLineQuantityStatus.setOrderState(orderState);
        orderLineQuantityStatus.setOrderStatusUpdateInfo(OrderStatusUpdateInfo.builder().build());
        order.setMaxStatus(orderState);
    }
}
