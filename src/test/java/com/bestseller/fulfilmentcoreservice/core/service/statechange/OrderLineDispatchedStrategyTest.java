package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderLineDispatchedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderLineDispatchedGenerator.WAREHOUSE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(MockitoExtension.class)
class OrderLineDispatchedStrategyTest {

    @InjectMocks
    private OrderLineDispatchedStrategy orderLineDispatchedStrategy;

    @Mock
    private OrderService orderService;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        OrderLineDispatched orderLineDispatched = OrderLineDispatchedGenerator.generate(orderId);

        String ean1 = orderLineDispatched.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .openQty(orderLineDispatched.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineDispatched.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()))
            .build();
        // act
        List<OrderStatusUpdateStateChange> result =
            orderLineDispatchedStrategy.defineOrderStateChange(orderLineDispatched, order);

        // assert
        assertThat(result).isNotEmpty();
    }

    @Test
    void defineStateChangeFor_givenOrderLineDispatchedOnlyOneWarehouse_shouldDispatchAllStates() {
        // arrange
        var orderId = "OL12212";
        var orderLineDispatched = OrderLineDispatchedGenerator.generate(orderId);

        final int openQtySecondEAN = 2;

        String ean1 = orderLineDispatched.getEan();
        String ean2 = orderLineDispatched.getEan() + "1";

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .openQty(orderLineDispatched.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineDispatched.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build(),
                OrderLine.builder()
                    .ean(ean2)
                    .openQty(openQtySecondEAN)
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineDispatched.getQuantity(),
                        OrderState.DISPATCHED,
                        "WAREHOUSE2"))
                    .build()
            ))
            .build();

        // act
        var result = orderLineDispatchedStrategy.defineOrderStateChange(orderLineDispatched, order)
            .stream()
            .collect(Collectors.groupingBy(OrderStatusUpdateStateChange::getEan, Collectors.toList()));

        // assert
        assertThat(result.values().stream().flatMap(List::stream))
            .hasSize(orderLineDispatched.getQuantity());
        assertThat(result.get(ean1))
            .hasSize(orderLineDispatched.getQuantity());

        result.get(ean1)
            .forEach(item ->
                assertThat(item)
                    .extracting("orderState", "ean").containsExactly(OrderState.DISPATCHED, ean1)
            );
    }

    @Test
    public void testExecuteStrategy_whenEan_doesNotExists() {
        // arrange
        var orderId = "OL12212";
        OrderLineDispatched orderLineDispatched = OrderLineDispatchedGenerator.generate(orderId);
        orderLineDispatched.setEan("wrongEan");
        String ean1 = orderLineDispatched.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .openQty(orderLineDispatched.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineDispatched.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()))
            .build();

        //  act & assert
        var exception = assertThrows(
            IllegalArgumentException.class,
            () -> orderLineDispatchedStrategy.defineOrderStateChange(orderLineDispatched, order));
        assertThat(exception)
            .hasMessage("EAN wrongEan not found in order lines for orderId OL12212");
    }

    @Test
    void defineOrderStateChange_givenOrderLineQtyStatusesWithoutOrderFulfillmentPartReference_nothingReturn() {
        // arrange
        var orderLineDispatched = OrderLineDispatchedGenerator.generate(UUID.randomUUID().toString());
        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(orderLineDispatched.getEan())
                    .openQty(orderLineDispatched.getQuantity())
                    .orderLineQtyStatus(
                        Collections.nCopies(
                            orderLineDispatched.getQuantity(),
                            OrderLineQtyStatus.builder()
                                .orderState(OrderState.DISPATCHED)
                                .orderFulfillmentPart(null)
                                .build())
                    )
                    .build()))
            .build();

        // act
        var result = orderLineDispatchedStrategy.defineOrderStateChange(orderLineDispatched, order);

        // assert
        assertThat(result).isEmpty();
    }

    private List<OrderLineQtyStatus> generateOrderLineQtyStatus(int quantity,
                                                                OrderState orderState,
                                                                String warehouse) {
        return Collections.nCopies(quantity,
            OrderLineQtyStatus.builder()
                .orderState(orderState)
                .orderFulfillmentPart(OrderFulfillmentPart.builder()
                    .fulfillmentNode(warehouse)
                    .build())
                .build());
    }
}
