package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.converter.entity.OrderEntityConverter;
import com.bestseller.fulfilmentcoreservice.converter.entity.UserDeviceInfoEntityConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.ValidOrderPlacedConverter;
import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.UserDeviceInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedServiceTest {

    @InjectMocks
    private ValidOrderPlacedServiceImpl submitOrderService;

    @Mock
    private OrderEntityConverter orderEntityConverter;

    @Mock
    private PartnerChannelService partnerChannelService;

    @Mock
    private CurrencyService currencyService;

    @Mock
    private OrderService orderService;

    @Mock
    private ChannelTypeService channelTypeService;

    @Mock
    private UserDeviceInfoEntityConverter userDeviceInfoEntityConverter;

    @Mock
    private ValidOrderPlacedConverter validOrderPlacedConverter;

    @Mock
    private OrderBlockService orderBlockService;

    @Test
    void process_givenOrderModel_orderIsSaved() {
        // arrange
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");
        var channel = "channel";
        var currency = "BRL";

        var orderModel = OrderModel.builder()
            .channel(channel)
            .currency(currency)
            .build();
        var order = Mockito.mock(Order.class);

        when(validOrderPlacedConverter.toOrderModel(validOrderPlaced)).thenReturn(orderModel);
        when(orderEntityConverter.convert(orderModel)).thenReturn(order);
        when(userDeviceInfoEntityConverter.toUserDeviceInfo(orderModel.getCustomer()))
            .thenReturn(UserDeviceInfo.builder().build());

        // act
        submitOrderService.process(validOrderPlaced);

        // assert
        verify(orderService).save(order);
        verify(partnerChannelService).getPartnerChannel(order, channel);
        verify(currencyService).getCurrency(order, currency);
        verify(channelTypeService).getChannelType(order, channel);
        verify(userDeviceInfoEntityConverter).toUserDeviceInfo(orderModel.getCustomer());
        verify(order).getAdditionalOrderInformation();
        verify(orderBlockService).applyOrderBlock(order);
    }

    @Test
    void process_givenOrderModelWithAdditionalInformation_orderIsSaved() {
        // arrange
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder("orderId");
        String channel = "channel";
        String currency = "BRL";
        var orderModel = OrderModel.builder()
            .channel(channel)
            .currency(currency)
            .build();
        var order = Mockito.mock(Order.class);

        when(validOrderPlacedConverter.toOrderModel(validOrderPlaced)).thenReturn(orderModel);
        when(orderEntityConverter.convert(orderModel)).thenReturn(order);
        when(userDeviceInfoEntityConverter.toUserDeviceInfo(orderModel.getCustomer()))
            .thenReturn(UserDeviceInfo.builder().build());

        // act
        submitOrderService.process(validOrderPlaced);

        order.getAdditionalOrderInformation()
            .forEach(additionalInformation -> {
                verify(additionalInformation).setOrder(order);
            });
        verify(orderBlockService).applyOrderBlock(order);
    }
}
