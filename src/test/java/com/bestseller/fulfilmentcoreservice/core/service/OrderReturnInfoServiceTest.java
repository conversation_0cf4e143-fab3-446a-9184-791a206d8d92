package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderReturnInfo;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderReturnInfoRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReturnInfoServiceTest {

    @InjectMocks
    private OrderReturnInfoServiceImpl orderReturnInfoService;

    @Mock
    private OrderReturnInfoRepository orderReturnInfoRepository;

    @Test
    void getLatestReturnDate_givenOrderReturnInfoPresent_returnDateIsReturned() {
        // arrange
        var returnDate = LocalDate.now();
        var orderId = "1";
        OrderReturnInfo orderReturnInfo = OrderReturnInfo.builder()
            .latestReturnDate(returnDate)
            .orderId(orderId)
            .build();
        when(orderReturnInfoRepository.findById(orderId))
            .thenReturn(Optional.of(orderReturnInfo));

        // act & assert
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId)).isPresent()
            .get()
            .usingRecursiveComparison()
            .isEqualTo(orderReturnInfo);
    }

    @Test
    void getLatestReturnDate_givenOrderReturnInfoNotPresent_optionalEmptyIsReturned() {
        // arrange
        var orderId = "2";
        when(orderReturnInfoRepository.findById(orderId))
            .thenReturn(Optional.empty());

        // act & assert
        assertThat(orderReturnInfoService.getOrderReturnInfo(orderId)).isEmpty();
    }

}
