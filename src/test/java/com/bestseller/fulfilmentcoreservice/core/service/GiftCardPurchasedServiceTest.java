package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator.createOrder;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GiftCardPurchasedServiceTest {

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @InjectMocks
    private GiftCardPurchasedServiceImpl giftCardPurchasedService;

    @Test
    void testProcess_invalidMessage() {
        // Arrange
        GiftCardPurchased message = createGiftCardPurchased();
        Order order = createOrder("orderId");
        when(orderService.findById(message.getOrderId())).thenReturn(order);

        // Act
        giftCardPurchasedService.process(message);

        // Assert
        verify(orderService).findById(message.getOrderId());
        verifyNoInteractions(orderStateService, orderStatusUpdatedProducerQueueProducer);
    }

    @Test
    void testProcess_OrderStatusUpdatedProducerCalled() {
        // Arrange
        GiftCardPurchased message = createGiftCardPurchased();
        Order order = createOrder("orderId");
        var orderLine = order.getOrderLines().stream().findFirst().get();
        orderLine.setLineNumber(message.getOrderLineNumber());
        orderLine.setVirtualProduct(true);
        var norVirtualProduct = new OrderLine();
        norVirtualProduct.setLineNumber(2);
        norVirtualProduct.setVirtualProduct(false);
        order.setOrderLines(List.of(
            orderLine,
            norVirtualProduct
        ));
        when(orderService.findById(message.getOrderId())).thenReturn(order);

        var payload = new DefaultOrderStatusUpdatedPayload();
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(payload);
        when(orderStatusUpdatedConverter.convertTo(order, payload, OrderStatusUpdated.Type.DISPATCHED))
            .thenReturn(new OrderStatusUpdated());

        // Act
        giftCardPurchasedService.process(message);

        // Assert
        verify(orderService).findById(message.getOrderId());
        verify(orderStateService).applyOrderState(order,
            orderLine,
            orderLine.getOrderLineQtyStatus().stream()
                .filter(orderLineQtyStatus -> orderLineQtyStatus.getIndexCol() == message.getIndex())
                .findFirst()
                .get(),
            OrderState.DISPATCHED);
        verify(orderStatusUpdatedProducerQueueProducer)
            .enqueue(EnqueueParams.create(
                orderStatusUpdatedConverter.convertTo(order, payload, OrderStatusUpdated.Type.DISPATCHED)));
    }

    @SuppressWarnings("MagicNumber")
    @Test
    void testProcess_noMatchingOrderLineNumber() {
        // Arrange
        GiftCardPurchased message = createGiftCardPurchased();
        Order order = createOrder("orderId");
        var orderLine = new OrderLine();
        orderLine.setLineNumber(99); // Intentionally does not match message.getOrderLineNumber()
        orderLine.setVirtualProduct(true);
        order.setOrderLines(List.of(orderLine));
        when(orderService.findById(message.getOrderId())).thenReturn(order);

        // Act
        giftCardPurchasedService.process(message);

        // Assert
        verify(orderService).findById(message.getOrderId());
        verifyNoInteractions(orderStateService, orderStatusUpdatedProducerQueueProducer);
    }

    @SuppressWarnings("MagicNumber")
    @Test
    void testProcess_noMatchingIndexCol() {
        // Arrange
        GiftCardPurchased message = createGiftCardPurchased();
        Order order = createOrder("orderId");
        var orderLine = new OrderLine();
        orderLine.setLineNumber(message.getOrderLineNumber());
        orderLine.setVirtualProduct(true);
        var orderLineQtyStatus = new OrderLineQtyStatus();
        orderLineQtyStatus.setIndexCol(99); // Intentionally does not match message.getIndex()
        orderLine.setOrderLineQtyStatus(List.of(orderLineQtyStatus));
        order.setOrderLines(List.of(orderLine));
        when(orderService.findById(message.getOrderId())).thenReturn(order);

        // Act
        giftCardPurchasedService.process(message);

        // Assert
        verify(orderService).findById(message.getOrderId());
        verifyNoInteractions(orderStateService, orderStatusUpdatedProducerQueueProducer);
    }

    private GiftCardPurchased createGiftCardPurchased() {
        return new GiftCardPurchased()
            .withOrderId("orderId")
            .withOrderLineNumber(1)
            .withIndex(0);
    }
}
