package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(MockitoExtension.class)
class OrderLineExportedStrategyTest {

    @InjectMocks
    private OrderLineExportedStrategy orderLineExportedStrategy;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var orderLine = OrderLine.builder()
            .ean("5715423786800")
            .ecomId(UUID.randomUUID())
            .originalQty(2)
            .build();
        var order = OrderGenerator.createOrder(orderId);
        var orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(order.getOrderId());
        orderLineExported.setEan(orderLine.getEan());
        orderLineExported.setQuantity(orderLine.getOriginalQty());
        orderLineExported.setExportDate(ZonedDateTime.now());

        // act
        List<OrderStatusUpdateStateChange> result =
            orderLineExportedStrategy.defineOrderStateChange(orderLineExported, order);

        // assert
        assertThat(result).isNotEmpty();
    }

    @Test
    public void testExecuteStrategy_whenEan_doesNotExists() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        OrderLineExported orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(orderId);
        orderLineExported.setEan("wrongEan");
        orderLineExported.setQuantity(2);
        orderLineExported.setExportDate(ZonedDateTime.now());

        //  act & assert
        var exception = assertThrows(
            IllegalArgumentException.class,
            () -> orderLineExportedStrategy.defineOrderStateChange(orderLineExported, order));
        assertThat(exception)
            .hasMessage("EAN wrongEan not found in order lines for orderId OL12212");
    }
}
