package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.InvalidStateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.monitoring.OrderStatusChangingMonitor;
import com.bestseller.fulfilmentcoreservice.core.service.helper.OrderStateHelper;
import com.bestseller.fulfilmentcoreservice.core.service.helper.OrderStateHelperImpl;
import com.bestseller.fulfilmentcoreservice.core.service.validator.OrderStateValidationService;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderBlock;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.VIRTUAL;
import static com.logistics.statetransition.OrderState.BLOCKED;
import static com.logistics.statetransition.OrderState.CANCELLED;
import static com.logistics.statetransition.OrderState.DISPATCHED;
import static com.logistics.statetransition.OrderState.EXPORTED;
import static com.logistics.statetransition.OrderState.PLACED;
import static com.logistics.statetransition.OrderState.PROCESSING;
import static com.logistics.statetransition.OrderState.PURGE;
import static com.logistics.statetransition.OrderState.RETURNED;
import static com.logistics.statetransition.OrderState.ROUTED;
import static com.logistics.statetransition.OrderState.ROUTING;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SuppressWarnings("MagicNumber")
@ExtendWith(MockitoExtension.class)
class OrderStateServiceTest {

    @Mock
    private OrderStateValidationService orderStateValidationService;

    @Mock
    private OrderStateHelper orderStateHelper;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private OrderStatusChangingMonitor orderStatusChangingMonitor;

    @Captor
    private ArgumentCaptor<List<StateChange>> stateChangesCaptor;

    @InjectMocks
    private OrderStateServiceImpl orderStateService;

    @Test
    void applyOrderState_givenOneOrderLineWithNoOrderLineQuantityStatus_stateIsApplied() {
        // arrange
        final int originalQty = 4;
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(List.of())
            .build();
        Order order = Order.builder()
            .orderId("OL1221")
            .orderLines(List.of(orderLine))
            .build();

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.applyOrderState(order, PLACED);

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        assertThat(stateChangesCaptured)
            .as("State changes have size of 4")
            .hasSize(originalQty);

        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine orderLineReturned = order.getOrderLines().iterator().next();
        assertThat(orderLineReturned.getOrderLineQtyStatus())
            .as("Order Line Captured has 4 order line quantity status update")
            .hasSize(originalQty)
            .extracting("orderState")
            .containsExactly(PLACED, PLACED, PLACED, PLACED);
        verify(orderStatusChangingMonitor, times(4)).increment(PLACED);
    }

    @Test
    void applyOrderState_givenAnOrderWithoutIdOneOrderLineWithNoOrderLineQuantityStatus_stateIsApplied() {
        // arrange
        final int originalQty = 4;
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(List.of())
            .build();
        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .build();

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.applyOrderState(order, PLACED);

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        assertThat(stateChangesCaptured)
            .as("State changes have size of 4")
            .hasSize(originalQty);

        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine orderLineReturned = order.getOrderLines().iterator().next();
        assertThat(orderLineReturned.getOrderLineQtyStatus())
            .as("Order Line Captured has 4 order line quantity status update")
            .hasSize(originalQty)
            .extracting("orderState")
            .containsExactly(PLACED, PLACED, PLACED, PLACED);
        verify(orderStatusChangingMonitor, times(4)).increment(PLACED);
    }

    @Test
    void applyOrderState_givenOneOrderLineWithNoOrderLineQuantityStatusAndChangeListIsNull_errorIsThrown() {
        // arrange
        final int originalQty = 4;
        String orderId = "OL1222";
        OrderLine orderLine = OrderLine.builder()
            .lineNumber(1)
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(List.of())
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenReturn(new HashMap<>());

        // act & assert
        var exception = assertThrows(
            StateTransitionException.class,
            () -> orderStateService.applyOrderState(order, null));
        assertThat(exception)
            .as("Exception message matches")
            .hasMessage(
                "Order: OL1222 | Order line: 1 - unacceptable state transition: the count of the change states is "
                    + "different from the count of the order line states.");
        verify(orderStateHelper, never()).updateMinMax(any());
        verify(orderRepository, never()).save(order);
        verify(orderStatusChangingMonitor, never()).increment(any());
    }

    @Test
    @SuppressWarnings("LineLength")
    void applyOrderState_givenOneOrderLineWithNoOrderLineQuantityStatusAndChangeListIsNotNullButHasDifferentSize_errorIsThrown() {
        // arrange
        final int originalQty = 4;
        String orderId = "OL1223";
        OrderLine orderLine = OrderLine.builder()
            .lineNumber(1)
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(List.of())
            .build();
        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenReturn(Map.of("EAN1", List.of(StateChange.builder().build(), StateChange.builder().build())));

        // act & assert
        var exception = assertThrows(
            StateTransitionException.class,
            () -> orderStateService.applyOrderState(order, null));
        assertThat(exception)
            .as("Exception message matches")
            .hasMessage(
                "Order: OL1223 | Order line: 1 - unacceptable state transition: "
                    + "the count of the change states is different from the count of the order line states.");
        verify(orderStateHelper, never()).updateMinMax(any());
        verify(orderStatusChangingMonitor, never()).increment(any());
    }

    @Test
    void applyOrderState_givenOneOrderLineWithDifferentStatusOnOrderLineQuantityStatus_errorIsThrown() {
        // arrange
        final int originalQty = 2;
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(List.of())
            .build();
        Order order = Order.builder()
            .orderId("OL1224")
            .orderLines(List.of(orderLine))
            .build();


        // This scenario should never happen.
        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenReturn(Map.of("EAN1", List.of(
                StateChange.builder().orderState(PLACED).build(),
                StateChange.builder().orderState(CANCELLED).build())));

        // act & assert
        var exception = assertThrows(
            StateTransitionException.class,
            () -> orderStateService.applyOrderState(order, PLACED));
        assertThat(exception)
            .as("Exception message matches")
            .hasMessage(
                "Order: OL1224 | All order lines must be initiated with the same state.");
        verify(orderStateHelper, never()).updateMinMax(any());
        verify(orderStatusChangingMonitor, never()).increment(any());
    }

    @Test
    void applyOrderState_givenOneOrderLineWithOrderLineQuantityStatus_stateIsApplied() {
        // arrange
        final int originalQty = 10;
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(PLACED).build(),
            OrderLineQtyStatus.builder().orderState(PLACED).build(),
            OrderLineQtyStatus.builder().orderState(PLACED).build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        Order order = Order.builder()
            .orderId("OL12230")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();


        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.applyOrderState(order, BLOCKED);

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        assertThat(stateChangesCaptured)
            .as("State changes have size of 3")
            .hasSize(orderLineQtyStatus.size())
            .extracting("orderState")
            .containsExactly(BLOCKED, BLOCKED, BLOCKED);


        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine orderLineReturned = order.getOrderLines().iterator().next();
        assertThat(orderLineReturned.getOrderLineQtyStatus())
            .as("Order Line Captured has 3 order line quantity status update")
            .hasSize(orderLineQtyStatus.size())
            .extracting("orderState")
            .containsExactly(BLOCKED, BLOCKED, BLOCKED);
        verify(orderRepository).save(order);
        verify(orderStatusChangingMonitor, times(3)).increment(BLOCKED);
    }

    @Test
    void applyOrderState_givenOneOrderLineWithOrderLineQuantityStatusForWarehouseStateChange_stateIsApplied() {
        // arrange
        final int originalQty = 10;
        final long orderStatusUpdateInfoId = 1233412L;

        Order order = Order.builder()
            .orderId("OL12230")
            .platform(Platform.DEMANDWARE)
            .build();

        String ean1 = "EAN1";
        OrderLine orderLine = OrderLine.builder()
            .ean(ean1)
            .originalQty(originalQty)
            .build();

        String returnShipmentId = "66777878";
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(
            OrderLineQtyStatus.builder().orderState(DISPATCHED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .id(orderStatusUpdateInfoId)
                    .returnShipmentId(returnShipmentId)
                    .order(order)
                    .build())
                .orderLine(orderLine)
                .build(),
            OrderLineQtyStatus.builder().orderState(DISPATCHED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .id(orderStatusUpdateInfoId)
                    .returnShipmentId(returnShipmentId)
                    .order(order)
                    .build())
                .orderLine(orderLine)
                .build(),
            OrderLineQtyStatus.builder().orderState(DISPATCHED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .id(orderStatusUpdateInfoId)
                    .returnShipmentId(returnShipmentId)
                    .order(order)
                    .build())
                .orderLine(orderLine)
                .build()
        );
        orderLine.setOrderLineQtyStatus(orderLineQtyStatusList);
        order.setOrderLines(List.of(orderLine));

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.apply(
            order,
            List.of(OrderStatusUpdateStateChange.builder()
                    .ean(ean1)
                    .orderState(RETURNED)
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                        .returnType(ReturnType.REJECTION)
                        .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                        .returnShipmentId("123412")
                        .build())
                    .build(),
                OrderStatusUpdateStateChange.builder()
                    .ean(ean1)
                    .orderState(RETURNED)
                    .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                        .returnType(ReturnType.REJECTION)
                        .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                        .returnShipmentId("123412")
                        .build())
                    .build())
        );

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        final int quantityOfStatusUpdated = 2;
        assertThat(stateChangesCaptured)
            .as("State changes have size of 3")
            .hasSize(quantityOfStatusUpdated)
            .extracting("orderState")
            .containsExactly(RETURNED, RETURNED);


        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine orderLineReturned = order.getOrderLines().iterator().next();
        assertThat(orderLineReturned.getOrderLineQtyStatus())
            .as("Order Line Captured has 3 order line quantity status update")
            .hasSize(orderLineQtyStatusList.size())
            .extracting("orderState")
            .containsExactly(RETURNED, RETURNED, DISPATCHED);
        assertThat(orderLineReturned.getOrderLineQtyStatus()
            .stream()
            .map(OrderLineQtyStatus::getOrderStatusUpdateInfo)
            .filter(orderStatusUpdateInfo -> orderStatusUpdateInfo.getId() != null
                && orderStatusUpdateInfo.getId() > 0)
            .findFirst()
            .orElseThrow().getId())
            .isEqualTo(orderStatusUpdateInfoId);

        verify(orderRepository).save(order);
        verify(orderStatusChangingMonitor, times(2)).increment(RETURNED);
    }

    @Test
    void applyOrderState_givenOneOrderLineWithProcessingState_stateIsApplied() {
        // arrange
        final int originalQty = 10;
        Order order = Order.builder()
            .orderId("OL12230")
            .platform(Platform.DEMANDWARE)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(EXPORTED).build(),
            OrderLineQtyStatus.builder().orderState(EXPORTED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .order(order)
                    .build()).build(),
            OrderLineQtyStatus.builder().orderState(EXPORTED).build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        order.setOrderLines(List.of(orderLine));

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.applyOrderState(order, PROCESSING);

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        assertThat(stateChangesCaptured)
            .as("State changes have size of 3")
            .hasSize(orderLineQtyStatus.size())
            .extracting("orderState")
            .containsExactly(PROCESSING, PROCESSING, PROCESSING);


        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine orderLineReturned = order.getOrderLines().iterator().next();
        assertThat(orderLineReturned.getOrderLineQtyStatus())
            .as("Order Line Captured has 3 order line quantity status update")
            .hasSize(orderLineQtyStatus.size())
            .extracting("orderState")
            .containsExactly(PROCESSING, PROCESSING, PROCESSING);
        verify(orderRepository).save(order);
        verify(orderStatusChangingMonitor, times(3)).increment(PROCESSING);
    }

    @Test
    void applyOrderState_givenOneOrderLineWithProcessingAndExportedState_stateIsApplied() {
        // arrange
        Order order = Order.builder()
            .orderId("OL12230")
            .platform(Platform.DEMANDWARE)
            .build();
        final int originalQty = 10;
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(EXPORTED).build(),
            OrderLineQtyStatus.builder().orderState(ROUTED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .order(order).build()).build(),
            OrderLineQtyStatus.builder().orderState(ROUTED)
                .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                    .order(order).build()).build());

        String ean1 = "EAN1";
        OrderLine orderLine = OrderLine.builder()
            .ean(ean1)
            .originalQty(originalQty)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        order.setOrderLines(List.of(orderLine));

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.apply(
            order,
            List.of(OrderStatusUpdateStateChange.builder()
                    .ean(ean1)
                    .orderState(PROCESSING)
                    .build(),
                OrderStatusUpdateStateChange.builder()
                    .ean(ean1)
                    .orderState(EXPORTED)
                    .build())
        );

        // assert
        List<StateChange> stateChangesCaptured = stateChangesCaptor.getValue();
        final int quantityOfStatusUpdated = 2;
        assertThat(stateChangesCaptured)
            .as("State changes have size of 3")
            .hasSize(quantityOfStatusUpdated)
            .extracting("orderState")
            .containsExactly(PROCESSING, EXPORTED);


        assertThat(order.getOrderLines())
            .as("Order Captured has only 1 order line")
            .hasSize(1);
        OrderLine lines = order.getOrderLines().iterator().next();
        assertThat(lines.getOrderLineQtyStatus())
            .as("Order Line Captured has 3 order line quantity status update")
            .hasSize(orderLineQtyStatus.size())
            .extracting("orderState")
            .containsExactly(PROCESSING, EXPORTED, ROUTED);

        verify(orderRepository).save(order);
        verify(orderStatusChangingMonitor).increment(PROCESSING);
        verify(orderStatusChangingMonitor).increment(EXPORTED);
    }

    @Test
    void applyOrderState_givenOneOrderLineWithOrderLineQuantityStatusAndImpossibleTransition_stateNotApplied() {
        // arrange
        final int originalQty = 10;
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(PURGE).build(),
            OrderLineQtyStatus.builder().orderState(PURGE).build(),
            OrderLineQtyStatus.builder().orderState(PURGE).build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        Order order = Order.builder()
            .orderId("OL12230")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();


        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));


        // act && assert
        assertThrows(
            InvalidStateTransitionException.class,
            () -> orderStateService.applyOrderState(order, BLOCKED));

        verify(orderRepository, never()).save(order);
        verify(orderStatusChangingMonitor, never()).increment(any());
    }

    @Test
    void applyOrderState_virtualOrderLine_invalidStateTransition() {
        final int originalQty = 10;
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(PLACED).build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        Order order = Order.builder()
            .orderId("OL12230")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .minStatus(PLACED)
            .maxStatus(PLACED)
            .build();

        // act && assert
        assertThrows(
            InvalidStateTransitionException.class,
            () -> orderStateService.applyOrderState(order, orderLine, orderLineQtyStatus.getFirst(), PURGE));
    }

    @Test
    void applyOnAllOrderLines_givenVirtualPlacedOrderLineAndPaymentIsCancelled_stateApplyFullyOnOrderLines() {
        // arrange
        final int originalQty = 2;
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().orderState(PLACED).build(),
            OrderLineQtyStatus.builder().orderState(PLACED).build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .originalQty(originalQty)
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatus).build();

        Order order = Order.builder()
            .orderId("OL12230")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .minStatus(PLACED)
            .maxStatus(PLACED)
            .build();

        when(orderStateHelper.getMapEanToStateChangeFromOrderLine(order.getOrderLines(), CANCELLED))
            .thenReturn(Map.of("EAN1", List.of(
                StateChange.builder().orderState(CANCELLED).build(),
                StateChange.builder().orderState(CANCELLED).build())));

        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.applyOnAllOrderLines(order, CANCELLED);

        // assert
        order.getOrderLines()
            .forEach(line -> line.getOrderLineQtyStatus()
                .forEach(lineStatus -> Assertions.assertEquals(lineStatus.getOrderState(), CANCELLED)));
    }

    @Test
    void getOrderState_givenBlockOrderAndOrderLineStatusWithoutState_blockedStateReturned() {
        // arrange
        List<OrderLineQtyStatus> orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder().build()
        );
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatus).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();


        // act
        OrderState orderStateReturned = orderStateService.getOrderState(
            order,
            orderLine,
            orderLineQtyStatus.iterator().next(),
            BLOCKED);


        // assert
        assertThat(orderStateReturned)
            .as("Blocked state returned")
            .isEqualTo(BLOCKED);

    }

    @Test
    void getOrderState_givenNorwegianOrder_dispatchedStateIsReturned() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .id(1L)
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act
        OrderState orderStateReturned = orderStateService.getOrderState(
            order,
            orderLine,
            orderLineQtyStatus,
            EXPORTED);

        // assert
        assertThat(orderStateReturned)
            .as("Blocked state returned")
            .isEqualTo(DISPATCHED);
    }

    @Test
    void getOrderState_givenVirtualOrder_dispatchedStateIsReturned() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .id(1L)
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingMethod(VIRTUAL)
            .platform(Platform.DEMANDWARE)
            .build();

        // act
        OrderState orderStateReturned = orderStateService.getOrderState(
            order,
            orderLine,
            orderLineQtyStatus,
            EXPORTED);

        // assert
        assertThat(orderStateReturned)
            .as("Dispatched state returned")
            .isEqualTo(DISPATCHED);

    }

    @Test
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenNotExportedOrderBecauseStatusIsNull_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine, null, null))
            .as("Should return false")
            .isFalse();
    }

    @Test
    @SuppressWarnings("LineLength")
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenNotExportedOrderBecauseStatusIsNotNullButOrderStateIsNotExported_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order,
            orderLine, orderLineQtyStatus, PLACED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenExportedButNotShippingAddressIsNull_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine,
            orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    @SuppressWarnings("LineLength")
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenExportedButNotShippingAddressIsNotNullButCountryIsNull_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine,
            orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    @SuppressWarnings("LineLength")
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenExportedButNotShippingAddressIsNotNullAndCountryIsNotNO_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("CH").build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine,
            orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenExportedAndForNOButTheresNoGiftCard_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine,
            orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    void isTheOrderLineGiftCardAndDispatchFromNorway_givenExportedAndForNOWithGiftCard_returnsTrue() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderLineGiftCardAndDispatchFromNorway(order, orderLine,
            orderLineQtyStatus, EXPORTED))
            .as("Should return true")
            .isTrue();
    }

    @Test
    void isTheOrderVirtualAndExported_givenExportedStatusButNoShippingMethod_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderVirtualAndExported(order, orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    void isTheOrderVirtualAndExported_givenExportedStatusWithShippingMethodButNotVirtual_returnsFalse() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .shippingMethod(ShippingMethod.EXPRESS)
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderVirtualAndExported(order, orderLineQtyStatus, EXPORTED))
            .as("Should return false")
            .isFalse();
    }

    @Test
    void isTheOrderVirtualAndExported_givenExportedStatusWithShippingMethodAndVirtual_returnsTrue() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .shippingMethod(ShippingMethod.VIRTUAL)
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(orderStateService.isTheOrderVirtualAndExported(order, orderLineQtyStatus, EXPORTED))
            .as("Should return true")
            .isTrue();
    }

    @ParameterizedTest
    @NullSource
    void getOrderState_givenOrderWithoutOrderBlocks_desiredStateIsReturned(OrderBlock orderBlock) {
        // arrange
        var orderLineQtyStatus = OrderLineQtyStatus.builder()
            .build();
        var orderLine = OrderLine.builder()
            .orderLineQtyStatus(List.of(orderLineQtyStatus))
            .build();
        var order = Order.builder()
            .orderBlock(orderBlock)
            .orderLines(List.of(orderLine))
            .build();

        // act
        var orderStateReturned = orderStateService.getOrderState(order, orderLineQtyStatus, PLACED);

        // assert
        assertThat(orderStateReturned).isEqualByComparingTo(PLACED);
    }

    @Test
    void getOrderState_givenOrderWithOrderBlocks_blockedOrderStateIsReturned() {
        // arrange
        var orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        var orderLine = OrderLine.builder()
            .orderLineQtyStatus(List.of(orderLineQtyStatus))
            .build();
        orderLineQtyStatus.setOrderLine(orderLine);
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .orderBlock(OrderBlock.builder().build())
            .orderLines(List.of(orderLine))
            .build();

        // act
        var orderStateReturned = orderStateService.getOrderState(order, orderLineQtyStatus, PLACED);

        // assert
        assertThat(orderStateReturned).isEqualByComparingTo(BLOCKED);
    }

    @Test
    void getOrderState_givenVirtualOrderWithoutOrderBlocksAndWithExportedDesiredStatus_dispatchedStatusIsReturned() {
        // arrange
        var orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(EXPORTED)
            .build();
        var orderLine = OrderLine.builder()
            .virtualProduct(true)
            .orderLineQtyStatus(List.of(orderLineQtyStatus))
            .build();
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .orderLines(List.of(orderLine))
            .shippingMethod(VIRTUAL)
            .build();

        // act
        var orderStateReturned = orderStateService.getOrderState(order, orderLineQtyStatus, EXPORTED);

        // assert
        assertThat(orderStateReturned).isEqualByComparingTo(DISPATCHED);
    }

    @Test
    void getOrderState_givenOrderWithOrderBlocks_desiredStateIsReturned() {
        // arrange
        var orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(EXPORTED)
            .build();
        var orderLine = OrderLine.builder()
            .orderLineQtyStatus(List.of(orderLineQtyStatus))
            .build();
        orderLineQtyStatus.setOrderLine(orderLine);
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .orderBlock(OrderBlock.builder().build())
            .orderLines(List.of(orderLine))
            .build();

        // act
        var orderStateReturned = orderStateService.getOrderState(order, orderLineQtyStatus, EXPORTED);

        // assert
        assertThat(orderStateReturned).isEqualByComparingTo(EXPORTED);
    }

    @Test
    void getOrderState_givenOrderWithGiftCardOrderLineAndDispatchFromNorway_dispatchedStatusIsReturned() {
        // arrange
        var orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        var orderLine = OrderLine.builder()
            .orderLineQtyStatus(List.of(orderLineQtyStatus))
            .virtualProduct(true)
            .build();
        orderLineQtyStatus.setOrderLine(orderLine);
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .orderLines(List.of(orderLine))
            .shippingAddress(
                Address.builder()
                    .countryCode("NO")
                    .build()
            )
            .build();

        // act
        var orderStateReturned = orderStateService.getOrderState(order, orderLineQtyStatus, EXPORTED);

        // assert
        assertThat(orderStateReturned).isEqualByComparingTo(DISPATCHED);
    }

    @Test
    void findValidOrderLineQtyStatusForStateChange_givenVirtualOrder_throwException() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine orderLine = OrderLine.builder()
            .ean("EAN1")
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(orderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .shippingMethod(ShippingMethod.VIRTUAL)
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThrows(
            InvalidStateTransitionException.class,
            () -> orderStateService.findValidOrderLineQtyStatusForStateChange(order,
                orderLine,
                orderLine.getOrderLineQtyStatus(),
                EXPORTED)
        );
    }

    @Test
    void apply_givenMixedOrder_noExceptionsThrown() {
        // arrange
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderState(PLACED)
            .build();
        List<OrderLineQtyStatus> orderLineQtyStatusList = List.of(orderLineQtyStatus);
        OrderLine virtualOrderLine = OrderLine.builder()
            .ean("EAN1")
            .virtualProduct(true)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        OrderLine nonVirtualOrderLine = OrderLine.builder()
            .ean("EAN2")
            .virtualProduct(false)
            .orderLineQtyStatus(orderLineQtyStatusList).build();

        Order order = Order.builder()
            .orderId("OL12231")
            .orderLines(List.of(virtualOrderLine, nonVirtualOrderLine))
            .shippingAddress(Address.builder().countryCode("NO").build())
            .shippingMethod(ShippingMethod.STANDARD)
            .platform(Platform.DEMANDWARE)
            .build();

        var stateChanges = List.of(OrderStatusUpdateStateChange.builder()
            .ean("EAN2")
            .orderState(ROUTING)
            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                .order(order)
                .build())
            .build());

        when(orderStateHelper.getMapEanToStateChange(stateChangesCaptor.capture()))
            .thenAnswer(invocation -> new OrderStateHelperImpl().getMapEanToStateChange(invocation.getArgument(0)));
        doNothing().when(orderStateHelper).updateMinMax(any(Order.class));

        // act
        orderStateService.apply(order, stateChanges);

        // assert
        verify(orderRepository).save(order);
    }
}
