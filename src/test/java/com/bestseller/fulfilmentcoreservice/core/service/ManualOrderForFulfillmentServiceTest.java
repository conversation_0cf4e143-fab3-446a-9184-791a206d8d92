package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.InvalidPaymentStatusUpdatedMessageException;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ManualOrderForFulfillmentServiceTest {

    @InjectMocks
    private ManualOrderForFulfillmentServiceImpl manualOrderForFulfillmentService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private PaymentStatusUpdatedService paymentStatusUpdatedService;

    @Test
    void fulfillOrder_givenPaymentAuthorisedBlockedOrder_triggersOrderForFulfillment() {
        // arrange
        Order order = mock(Order.class);
        when(order.getOrderId()).thenReturn("O123456");

        PaymentStatusUpdated paymentStatusUpdated = mock(PaymentStatusUpdated.class);
        AuthorizedPayload authorizedPayload = mock(AuthorizedPayload.class);
        when(paymentStatusUpdated.getPayload()).thenReturn(authorizedPayload);
        when(paymentStatusUpdated.getPaymentState()).thenReturn(PaymentStatusUpdated.PaymentState.AUTHORISED);
        when(paymentService.getPaymentStatusUpdatedMessage("O123456"))
            .thenReturn(paymentStatusUpdated);

        // act
        manualOrderForFulfillmentService.fulfillOrder(order);

        // assert
        verify(paymentService).getPaymentStatusUpdatedMessage("O123456");
        verify(paymentStatusUpdatedService).process(paymentStatusUpdated);
    }

    @Test
    void fulfillOrder_whenPaymentServiceFails_throwsException() {
        // Arrange
        Order order = mock(Order.class);
        when(order.getOrderId()).thenReturn("O123456");

        when(paymentService.getPaymentStatusUpdatedMessage("O123456"))
            .thenThrow(new RuntimeException("Payment service unavailable"));

        // Act & Assert
        Exception exception = assertThrows(RuntimeException.class, () ->
            manualOrderForFulfillmentService.fulfillOrder(order));

        assertEquals("Payment service unavailable", exception.getMessage());
        verify(paymentService).getPaymentStatusUpdatedMessage("O123456");
        verifyNoInteractions(paymentStatusUpdatedService);
    }

    @Test
    void fulfillOrder_givenNullPayload_fulfillmentNotTriggered() {
        // arrange
        Order order = mock(Order.class);
        when(order.getOrderId()).thenReturn("O123456");

        PaymentStatusUpdated paymentStatusUpdated = mock(PaymentStatusUpdated.class);
        when(paymentStatusUpdated.getPaymentState()).thenReturn(PaymentStatusUpdated.PaymentState.AUTHORISED);
        when(paymentStatusUpdated.getPayload()).thenReturn(null);

        when(paymentService.getPaymentStatusUpdatedMessage("O123456"))
            .thenReturn(paymentStatusUpdated);

        // act & assert
        assertThrows(InvalidPaymentStatusUpdatedMessageException.class,
            () -> manualOrderForFulfillmentService.fulfillOrder(order));
        verifyNoInteractions(paymentStatusUpdatedService);
    }

    @Test
    void fulfillOrder_givenNullMessage_fulfillmentNotTriggered() {
        // arrange
        Order order = mock(Order.class);
        when(order.getOrderId()).thenReturn("O123456");

        when(paymentService.getPaymentStatusUpdatedMessage("O123456"))
            .thenReturn(null);

        // act
        manualOrderForFulfillmentService.fulfillOrder(order);

        // assert
        verify(paymentService).getPaymentStatusUpdatedMessage(order.getOrderId());
        verifyNoInteractions(paymentStatusUpdatedService);
    }
}
