package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderFulfillmentPartServiceException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFulfillmentPartRepository;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderFulfilmentPartServiceTest {
    @InjectMocks
    private OrderFulfilmentPartServiceImpl orderFulfillmentPartService;
    @Mock
    private OrderFulfillmentPartRepository orderFulfillmentPartRepository;
    @Mock
    private OrderRepository orderRepository;
    @Mock
    private OrderFulfillmentPartUpdaterService orderFulfillmentPartUpdaterService;
    @Mock
    private OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNode_fulfilmentNodeIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode1")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber(null)
                .orderLineQtyStatus(List.of(order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst()))
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNode_trackingNumberIsNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode1")
            .trackingNumber(null)
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNode_trackingNumberIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber("trackingNumber")
                .orderLineQtyStatus(List.of(order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst()))
                .build());

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode1")
            .trackingNumber("trackingNumber")
            .build();

        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNode_trackingNumberIsBlank() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber(null)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenGetOrderFulfillmentParte_trackingNumberIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .active(true)
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void createOrderFulfillmentPart_whenExceptionRaised_orderLineQuantityStatusIsZero() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .build();

        order.getOrderLines().get(0).setOrderLineQtyStatus(Collections.emptyList());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // act & assert
        var exception = assertThrows(OrderFulfillmentPartServiceException.class,
            () -> orderFulfillmentPartService.handleOrderFulfilmentParts(
                order, orderFulfillmentPartModel));

        assertThat(exception)
            .as("Exception matches")
            .hasMessage("Order line quantity status is zero for orderId: OL12212 and ean: 5715423786800");
    }

    @Test
    void handleOrderFulfillmentPart_when_attachedOrderFulfillmentPartToOrderLine() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        //assert
        verify(orderFulfillmentPartRepository).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_when_updateOrderSucceed1() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        //assert
        verify(orderFulfillmentPartRepository).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_when_mismatchTrackingNumber() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setTrackingNumber("trackingNumber");
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        //assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_when_updateOrderSucceed() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .active(true)
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart());
        // act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        //assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldDeactivateAndSave_whenDifferentFulfillmentNode() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenActiveFalse() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .active(false)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldDeactivateAndSave_whenAllDataExists() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .storeId(1)
            .carrierName("carrierName")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .returnTrackingNumber("returnTrackingNumber")
            .trackingNumber("trackingNumber")
            .partNumber(1)
            .fulfillmentNode("fulfillmentNode")
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldDeactivateAndSave_whenPartNumberIsZero() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .partNumber(0)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_updateOrderFulfillmentPart_whenPartNumberIsNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .partNumber(null)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
        verify(orderFulfillmentPartUpdaterService).update(any(), any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldUpdatePartNumberWhenItIsMissing_whenPartNumberIsNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .partNumber(1)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_when_fulfilmentNodeIsEmpty() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenShouldUpdate_orderFulfilmentPart() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("")
            .storeId(1)
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .partNumber(1)
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .storeId(1)
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .partNumber(1)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_updatePartNumberWhenItIsMissing_partNumberIsExists() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .partNumber(1)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_updatePartNumberWhenItIsMissing_partNumberIsNotNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .partNumber(0)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("")
                .carrierName("carrierName")
                .trackingNumber("trackingNumber")
                .returnTrackingNumber("returnTrackingNumber")
                .orangePrinted(LocalDate.now())
                .dispatchDate(LocalDateTime.now())
                .active(true)
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_updatePartNumberWhenItIsMissing_partNumberIsZero() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .partNumber(0)
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("")
                .carrierName("carrierName")
                .trackingNumber("trackingNumber")
                .returnTrackingNumber("returnTrackingNumber")
                .orangePrinted(LocalDate.now())
                .dispatchDate(LocalDateTime.now())
                .partNumber(0)
                .active(true)
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_updatePartNumberWhenItIsMissing_partNumberIsNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .orangePrinted(LocalDate.now())
            .dispatchDate(LocalDateTime.now())
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("")
                .carrierName("carrierName")
                .trackingNumber("trackingNumber")
                .returnTrackingNumber("returnTrackingNumber")
                .orangePrinted(LocalDate.now())
                .dispatchDate(LocalDateTime.now())
                .active(true)
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenAttachedOrderFulfillmentPartToOrderLine_nonBlankFulfilmentNode() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenAttachedOrderFulfillmentPartToOrderLine_blankTrackingNumber() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber("")
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenAttachedOrderFulfillmentPartToOrderLine_notEqualFulfilmentNode() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode1")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber("trackingNumber1")
                .orderLineQtyStatus(List.of(order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst()))
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenAttachedOrderFulfillmentPartToOrderLine_notEqualTrackingNumber() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber("trackingNumber1")
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenDBTrackingNumberIsNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("")
                .partNumber(1)
                .active(true)
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenTrackingNumberIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenDBTrackingNumberIsNotNull() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setTrackingNumber("");
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenTrackingNumberIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setTrackingNumber("trackingNumber");

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setActive(true);

        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber1")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPartByEanAndTrackingNumber_whenTrackingNumberIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPartByEanAndTrackingNumber_whenTrackingNumberIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("trackingNumber");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any(OrderFulfillmentPart.class));
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPart_whenFulfilmentIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfilmentNode")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("fulfilmentNode");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any(OrderFulfillmentPart.class));
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPart_whenFulfilmentIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfilmentNode1")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("trackingNumber1");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository).save(any(OrderFulfillmentPart.class));
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPart_whenBlankFulfilmentNode() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("trackingNumber1");

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setFulfillmentNode("");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any(OrderFulfillmentPart.class));
    }

    @Test
    void handleOrderFulfillmentPart_getOrderFulfillmentPartByEanAndTrackingNumber_whenNonEqualTrackingNumber() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("trackingNumber1");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository).save(any(OrderFulfillmentPart.class));
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNodeFromOrder_fulfilmentNodeIsEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode1")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(OrderFulfillmentPart.builder()
                .order(order)
                .fulfillmentNode("fulfillmentNode")
                .trackingNumber(null)
                .orderLineQtyStatus(List.of(order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst()))
                .build());

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, times(1)).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_detachedFulfillmentNodeFromOrder_whenFulfillmentNodeIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .active(true)
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNodeFromOrder_trackingNumberIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .setOrderFulfillmentPart(orderFulfillmentPart);

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setTrackingNumber("trackingNumber1");

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_whenDetachedFulfillmentNodeFromOrder_fulfillmentNodeIsNotEqual() {
        // Arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("fulfillmentNode")
            .trackingNumber("trackingNumber")
            .build();

        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderFulfillmentPart(orderFulfillmentPart);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setFulfillmentNode("fulfillmentNode1");
        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);
        // Act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // Assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }

    @Test
    void handleOrderFulfillmentPart_shouldAttachNewOrderFulfillmentPart_whenOrderFulfilmentPartIsNull() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .eanQuantityPairs(order.getOrderLines().stream().map(orderLine ->
                OrderFulfilmentPartModel.EanQuantityPair.builder()
                    .ean(orderLine.getEan())
                    .quantity(orderLine.getOpenQty())
                    .build()
            ).toList())
            .build();

        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .order(order)
            .fulfillmentNode("")
            .partNumber(1)
            .trackingNumber("trackingNumber")
            .active(true)
            .build();

        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setOrderFulfillmentPart(null);

        when(orderFulfillmentPartConverter.toOrderFulfillmentPart(any())).thenReturn(orderFulfillmentPart);

        // act
        orderFulfillmentPartService.handleOrderFulfilmentParts(order, orderFulfillmentPartModel);

        // assert
        verify(orderFulfillmentPartRepository, never()).save(any());
    }
}
