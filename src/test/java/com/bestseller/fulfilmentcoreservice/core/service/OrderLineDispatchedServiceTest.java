package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.EnqueueResult;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderDispatchedStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFulfillmentPartConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderLineDispatchedStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderLineDispatchedServiceTest {

    @InjectMocks
    private OrderLineDispatchedServiceImpl orderLineDispatchedService;

    @Mock
    private OrderFulfilmentPartService orderFulfillmentPartService;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderFulfillmentPartConverter orderFulfillmentPartConverter;

    @Mock
    private OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    @Mock
    private TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    @Mock
    private OrderDispatchedStatusUpdatedPayloadConverter payloadConverter;

    @Mock
    private OrderLineDispatchedStrategy orderLineDispatchedStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    @BeforeEach
    public void setUp() {
        when(orderLineDispatchedStrategy.getMessageType()).thenReturn(OrderLineDispatched.class);
        context = new OrderStateChangeContext(List.of(orderLineDispatchedStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void process_givenUnknownOrderId_throwOrderNotFoundException() {
        // arrange
        var orderLineDispatched = new OrderLineDispatched();
        orderLineDispatched.setOrderId("OL12212");
        when(orderService.findById(orderLineDispatched.getOrderId())).thenThrow(OrderNotFoundException.class);

        // assert & act
        assertThrows(OrderNotFoundException.class, () ->
            orderLineDispatchedService.process(orderLineDispatched));
    }

    @Test
    void process_givenValidMessage_orderStatusUpdatedMessageIsPublished() {
        // arrange
        var orderLineDispatched = new OrderLineDispatched();
        orderLineDispatched.setOrderId(UUID.randomUUID().toString());
        orderLineDispatched.setEan(UUID.randomUUID().toString());
        final var quantity = 4;
        orderLineDispatched.setQuantity(quantity);

        var order = OrderGenerator.createOrder(orderLineDispatched.getOrderId());
        when(orderService.findById(orderLineDispatched.getOrderId())).thenReturn(order);

        doNothing().when(orderStateService).apply(any(), any());

        var orderUpdatedStatusPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(Collections.emptyList())
            .build();
        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(order.getOrderId())
            .withPayload(orderUpdatedStatusPayload)
            .withType(OrderStatusUpdated.Type.DISPATCHED);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.DISPATCHED))
            .thenReturn(orderStatusUpdated);
        when(orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdated)))
            .thenReturn(mock(EnqueueResult.class));

        when(payloadConverter.convert(order.getOrderLines(), orderLineDispatched))
            .thenReturn(orderUpdatedStatusPayload);

        // act
        orderLineDispatchedService.process(orderLineDispatched);

        // assert
        verify(orderService).findById(orderLineDispatched.getOrderId());
        verify(orderStateService).apply(any(Order.class), any());
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
        verify(orderStatusUpdatedConverter).convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.DISPATCHED);
        verify(payloadConverter).convert(order.getOrderLines(), orderLineDispatched);
    }

}
