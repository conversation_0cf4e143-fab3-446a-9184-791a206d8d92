package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdateTradeByteConverter;
import com.bestseller.fulfilmentcoreservice.core.model.TradeByteOrderStatusBaseModel;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdateTradebyte.OrderStatusUpdateTradebyte;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderStatusUpdatedTradeByteServiceTest {

    private static final String EAN_1 = "5715423786800";

    private static final String EAN_2 = "5715423786801";

    private static final int QUANTITY = 4;
    private static final int LINE_NUMBER_1 = 1;
    private static final int LINE_NUMBER_2 = 4;

    @InjectMocks
    private OrderStatusUpdatedTradeByteServiceImpl orderStatusUpdatedTradeByteService;

    @Mock
    private QueueProducer<OrderStatusUpdateTradebyte> orderStatusUpdateTradebyteProducerQueueProducer;

    @Mock
    private OrderStatusUpdateTradeByteConverter orderStatusUpdateTradeByteConverter;

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_WhenIsNotTB() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.RETURNED);
        order.setPlatform(Platform.DEMANDWARE);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
    }

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_whenReturnedMessagePublished() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setOrderState(OrderState.RETURNED);
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setChanged(true);
        when(orderStatusUpdateTradeByteConverter.toReturned(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter).toReturned(any(), any(), any());
    }

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_whenEanIsNotEqual() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.RETURNED)
            .ean(UUID.randomUUID().toString())
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.RETURNED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
    }

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_whenLineNumberIsNotEqual() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_2)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.RETURNED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_WhenIsNotTB() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.DISPATCHED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.DISPATCHED);
        order.setPlatform(Platform.DEMANDWARE);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            anyString());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_whenDispatchedMessagePublished() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.DISPATCHED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setOrderState(OrderState.DISPATCHED);
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setChanged(true);
        when(orderStatusUpdateTradeByteConverter.toDispatched(eq(order), any(), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter).toDispatched(any(), any(), any(), any());
    }

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_whenDispatchedMessagePublished() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setOrderState(OrderState.RETURNED);
        order.getOrderLines().getFirst().getOrderLineQtyStatus().getFirst().setChanged(true);
        when(orderStatusUpdateTradeByteConverter.toReturned(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter).toReturned(any(), any(), any());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_whenEanIsNotEqualAndLineNumberIsNotNull() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.DISPATCHED)
            .ean(UUID.randomUUID().toString())
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.DISPATCHED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            anyString());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_whenEanIsNotEqualAndLineNumberIsNull() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.DISPATCHED)
            .ean(UUID.randomUUID().toString())
            .quantity(QUANTITY)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.DISPATCHED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            anyString());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_whenStateIsNotValid() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.DISPATCHED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.PROCESSING);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToCancelled_orderStatusUpdatedTradeByte_whenStateIsCancelledWhenEanIsEqual() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean(EAN_1)
            .ecomId(UUID.randomUUID())
            .lineNumber(LINE_NUMBER_1)
            .originalQty(QUANTITY)
            .build();

        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.CANCELLED)
            .orderLines(List.of(orderLine))
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.CANCELLED);
        when(orderStatusUpdateTradeByteConverter.toCancelled(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter).toCancelled(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToCancelled_orderStatusUpdatedTradeByte_whenStateIsCancelledOrderLineNumberIsEqual() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean(EAN_1)
            .ecomId(UUID.randomUUID())
            .lineNumber(LINE_NUMBER_1)
            .originalQty(QUANTITY)
            .build();

        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.CANCELLED)
            .orderLines(List.of(orderLine))
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.CANCELLED);
        order.getOrderLines().get(0).setLineNumber(LINE_NUMBER_1);
        when(orderStatusUpdateTradeByteConverter.toCancelled(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter).toCancelled(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToCancelled_orderStatusUpdatedTradeByte_whenStateIsCancelledOrderLineNumberIsNotEqual() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean(EAN_1)
            .lineNumber(LINE_NUMBER_2)
            .ecomId(UUID.randomUUID())
            .originalQty(QUANTITY)
            .build();

        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.CANCELLED)
            .orderLines(List.of(orderLine))
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.CANCELLED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toCancelled(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToCancelled_orderStatusUpdatedTradeByte_whenStateIsCancelledEanIsNotEqual() {
        // arrange
        var orderLine = OrderLine.builder()
            .ean(EAN_2)
            .lineNumber(LINE_NUMBER_2)
            .ecomId(UUID.randomUUID())
            .originalQty(QUANTITY)
            .build();

        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.CANCELLED)
            .orderLines(List.of(orderLine))
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.CANCELLED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toCancelled(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToCancelled_orderStatusUpdatedTradeByte_whenStateIsCancelledAndOrderLineIsEmpty() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.CANCELLED)
            .orderLines(List.of())
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.CANCELLED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toCancelled(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toReturned(any(), any(), any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            any());
    }

    @Test
    void updateToDispatched_orderStatusUpdatedTradeByte_whenStateIsNotDispatched() {
        // arrange
        var tradeByteOrderStatusBaseModel = TradeByteOrderStatusBaseModel.builder()
            .orderId("TB12212")
            .orderState(OrderState.BLOCKED)
            .ean(EAN_1)
            .quantity(QUANTITY)
            .lineNumber(LINE_NUMBER_1)
            .build();

        var order = OrderGenerator.createOrder(tradeByteOrderStatusBaseModel.getOrderId());
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).setOrderState(OrderState.DISPATCHED);
        // act
        orderStatusUpdatedTradeByteService
            .produceOrderStatusUpdatedTradeByte(tradeByteOrderStatusBaseModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, never()).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, never()).toDispatched(any(), any(), any(),
            anyString());
    }

    @Test
    void updateToReturned_orderStatusUpdatedTradeByte_whenMultipleQuantitiesReturnedSeparately() {
        // arrange
        var totalQuantity = 2;
        var orderId = "TB12212";

        // First return request for quantity 1
        var firstReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(1)
            .lineNumber(LINE_NUMBER_1)
            .build();

        // Create order with 2 quantities
        var order = OrderGenerator.createOrder(orderId);
        var orderLine = order.getOrderLines().getFirst();
        orderLine.setOriginalQty(totalQuantity);
        orderLine.setEan(EAN_1);
        orderLine.setLineNumber(LINE_NUMBER_1);

        order.setPlatform(Platform.TRADEBYTE);

        var qtyStatuses = List.of(
            // the apply method has changed the state to returned
            OrderLineQtyStatus.builder()
                .orderState(OrderState.RETURNED)
                .isChanged(true)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build()
        );
        orderLine.setOrderLineQtyStatus(qtyStatuses);

        when(orderStatusUpdateTradeByteConverter.toReturned(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act - first return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(firstReturnModel, order);

        // Second return request for quantity 1
        var secondReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(1)
            .lineNumber(LINE_NUMBER_1)
            .build();

        qtyStatuses.getFirst().setChanged(false);
        // Set the second quantity status to returned. The apply method has already been called.
        qtyStatuses.get(1).setOrderState(OrderState.RETURNED);
        qtyStatuses.get(1).setChanged(true);

        // act - second return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(secondReturnModel, order);

        // assert - should only produce 2 messages total, not 3
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(2)).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, times(2)).toReturned(any(), any(), any());
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void updateToReturned_orderStatusUpdatedTradeByte_whenMultipleQuantitiesReturnedSeparately_v2() {
        // arrange
        var totalQuantity = 3;
        var orderId = "TB12212";

        // First return request for quantity 1
        var firstReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(1)
            .lineNumber(LINE_NUMBER_1)
            .build();

        // Create order with 3 quantities
        var order = OrderGenerator.createOrder(orderId);
        var orderLine = order.getOrderLines().getFirst();
        orderLine.setOriginalQty(totalQuantity);
        orderLine.setEan(EAN_1);
        orderLine.setLineNumber(LINE_NUMBER_1);

        order.setPlatform(Platform.TRADEBYTE);

        var qtyStatuses = List.of(
            // the apply method has changed the state to returned
            OrderLineQtyStatus.builder()
                .orderState(OrderState.RETURNED)
                .isChanged(true)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build()
        );
        orderLine.setOrderLineQtyStatus(qtyStatuses);

        when(orderStatusUpdateTradeByteConverter.toReturned(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act - first return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(firstReturnModel, order);

        // Second return request for quantity 2
        var secondReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(2)
            .lineNumber(LINE_NUMBER_1)
            .build();

        qtyStatuses.getFirst().setChanged(false);
        // Set the second and third quantities status to returned. The apply method has already been called.
        qtyStatuses.get(1).setOrderState(OrderState.RETURNED);
        qtyStatuses.get(2).setOrderState(OrderState.RETURNED);
        qtyStatuses.get(1).setChanged(true);
        qtyStatuses.get(2).setChanged(true);

        // act - second return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(secondReturnModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(3)).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, times(3)).toReturned(any(), any(), any());
    }

    @Test
    @SuppressWarnings("MagicNumber")
    void updateToReturned_orderStatusUpdatedTradeByte_whenMultipleQuantitiesReturnedSeparately_v3() {
        // arrange
        var totalQuantity = 5;
        var orderId = "TB12212";

        // First return request for quantity 1
        var firstReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(1)
            .lineNumber(LINE_NUMBER_1)
            .build();

        // Create order with 5 quantities
        var order = OrderGenerator.createOrder(orderId);
        var orderLine = order.getOrderLines().getFirst();
        orderLine.setOriginalQty(totalQuantity);
        orderLine.setEan(EAN_1);
        orderLine.setLineNumber(LINE_NUMBER_1);

        order.setPlatform(Platform.TRADEBYTE);

        var qtyStatuses = List.of(
            // the apply method has changed the state to returned
            OrderLineQtyStatus.builder()
                .orderState(OrderState.RETURNED)
                .isChanged(true)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.DISPATCHED)
                .build()
        );
        orderLine.setOrderLineQtyStatus(qtyStatuses);

        when(orderStatusUpdateTradeByteConverter.toReturned(eq(order), any(), any()))
            .thenReturn(new OrderStatusUpdateTradebyte());

        // act - first return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(firstReturnModel, order);

        // Second return request for quantity 2
        var secondReturnModel = TradeByteOrderStatusBaseModel.builder()
            .orderId(orderId)
            .orderState(OrderState.RETURNED)
            .ean(EAN_1)
            .quantity(2)
            .lineNumber(LINE_NUMBER_1)
            .build();

        qtyStatuses.getFirst().setChanged(false);
        // Set the second and third quantities status to returned. The apply method has already been called.
        qtyStatuses.get(1).setOrderState(OrderState.RETURNED);
        qtyStatuses.get(2).setOrderState(OrderState.RETURNED);
        qtyStatuses.get(1).setChanged(true);
        qtyStatuses.get(2).setChanged(true);

        // act - second return
        orderStatusUpdatedTradeByteService.produceOrderStatusUpdatedTradeByte(secondReturnModel, order);

        // assert
        verify(orderStatusUpdateTradebyteProducerQueueProducer, times(3)).enqueue(any());
        verify(orderStatusUpdateTradeByteConverter, times(3)).toReturned(any(), any(), any());
    }

}
