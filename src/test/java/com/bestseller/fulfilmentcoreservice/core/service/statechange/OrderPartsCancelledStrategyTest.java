package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CancelReason;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderPartsCancelledStrategyTest {

    @InjectMocks
    private OrderPartsCancelledStrategy orderPartsCancelledStrategy;

    @Mock
    private OrderService orderService;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse("Warehouse")
            .withCancellationDate(ZonedDateTime.now())
            .withOrderLines(List.of(
                new com.bestseller
                    .generated
                    .interfacecontracts
                    .kafkamessages
                    .pojos
                    .orderPartsCancelled.OrderLine()
                    .withEan("5715423786800")
                    .withLineNumber(1)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(2)));

        // act
        List<OrderStatusUpdateStateChange> result =
            orderPartsCancelledStrategy.defineOrderStateChange(orderPartsCancelled, order);

        // assert
        assertThat(result).isNotEmpty();
        assertThat(result).hasSize(2);

    }

    @Test
    public void givenOrderPartsCancelledWithOneQuantityForAnEan_ThenReturnOneStateChange() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId)
            .withIsTest(Boolean.TRUE)
            .withWarehouse("Warehouse")
            .withCancellationDate(ZonedDateTime.now())
            .withOrderLines(List.of(
                new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled
                    .OrderLine()
                    .withEan("5715423786800")
                    .withLineNumber(1)
                    .withCancelReason(CancelReason.STORE_REJECTION.name())
                    .withQuantity(1)));

        // act
        List<OrderStatusUpdateStateChange> result =
            orderPartsCancelledStrategy.defineOrderStateChange(orderPartsCancelled, order);

        // assert
        assertThat(result).isNotEmpty();
        assertThat(result).hasSize(1);
    }

}
