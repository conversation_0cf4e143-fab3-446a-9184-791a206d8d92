package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.ReturnFeeDecision;
import com.bestseller.fulfilmentcoreservice.persistence.entity.ReturnFeeDecisionId;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.repository.ReturnFeeDecisionRepository;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChargeReturnFeeServiceTest {

    @InjectMocks
    private ChargeReturnFeeServiceImpl chargeReturnFeeServiceImpl;

    @Mock
    private ReturnFeeDecisionRepository returnFeeDecisionRepository;

    @Test
    void shouldChargeReturnFee_givenEmployeeStoreOrder_shouldNotCharge() {
        // arrange
        var order = Order.builder()
            .customer(Customer.builder()
                .type(CustomerType.EMPLOYEE)
                .build())
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isFalse();
    }

    @Test
    void shouldChargeReturnFee_givenNotEmployeeStoreOrderAndAlreadyRefunded_shouldNotCharge() {
        // arrange
        var order = Order.builder()
            .returnFeeCharged(true)
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isFalse();
    }

    @Test
    void shouldChargeReturnFee_givenRegularCustomerOrderAndNoOrderLine_shouldCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of())
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.ROE)
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isTrue();
    }

    @Test
    void shouldChargeReturnFee_givenRegularCustomerOrderAndOrderLineWithNoOrderStatusUpdated_shouldCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of())
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isTrue();
    }

    @Test
    void shouldChargeReturnFee_givenRegularCustomerOrderNotReturnedState_shouldCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of(
                    OrderLineQtyStatus.builder().orderState(OrderState.PLACED).build()
                ))
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isTrue();
    }

    @Test
    void shouldChargeReturnFee_givenRegularCustomerOrderReturnedState_shouldCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of(
                    OrderLineQtyStatus.builder().orderState(OrderState.RETURNED).build()
                ))
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isFalse();
    }

    @Test
    void shouldChargeReturnFee_givenOrderReturnedStateAndReturnFeeDecisionSaysItRefundNeedsToBeMade_shouldCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of(
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.RETURNED)
                        .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                            .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                            .build())
                        .build()
                ))
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        when(returnFeeDecisionRepository.findById(Mockito.any(ReturnFeeDecisionId.class)))
            .thenReturn(Optional.of(ReturnFeeDecision.builder()
                .chargeReturnFee(true)
                .build()));

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isTrue();
    }

    @Test
    void shouldChargeReturnFee_givenOrderReturnedStateAndReturnFeeDecisionSaysItRefundDoesntCharge_shouldNotCharge() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of(
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.RETURNED)
                        .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                            .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                            .build())
                        .build()
                ))
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        when(returnFeeDecisionRepository.findById(Mockito.any(ReturnFeeDecisionId.class)))
            .thenReturn(Optional.of(ReturnFeeDecision.builder()
                .chargeReturnFee(false)
                .build()));

        // act & assert
        assertThat(chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isFalse();
    }

    @Test
    void shouldChargeReturnFee_givenReturnFeeDecisionNotFound_exceptionIsThrown() {
        // arrange
        var order = Order.builder()
            .orderLines(List.of(OrderLine.builder()
                .orderLineQtyStatus(List.of(
                    OrderLineQtyStatus.builder()
                        .orderState(OrderState.RETURNED)
                        .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                            .customerReturnReason(CustomerReturnReason.REASON_NOT_STATED_20)
                            .build())
                        .build()
                ))
                .build()))
            .shippingAddress(Address.builder()
                .countryCode("FR")
                .build())
            .market(Market.BSE_WORLD)
            .build();

        when(returnFeeDecisionRepository.findById(Mockito.any(ReturnFeeDecisionId.class)))
            .thenReturn(Optional.empty());

        // act & assert
        assertThatThrownBy(() -> chargeReturnFeeServiceImpl.shouldChargeReturnFee(order))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Refund decision not found for ecomCountry:")
            .hasMessageContaining("ROTW and customerReturnReason: REASON_NOT_STATED_20");
    }
}
