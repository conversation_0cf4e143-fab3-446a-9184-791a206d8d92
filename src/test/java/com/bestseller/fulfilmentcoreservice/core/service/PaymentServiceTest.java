package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.configuration.client.ServiceCredential;
import com.bestseller.fulfilmentcoreservice.configuration.client.ServiceCredentialConfig;
import com.bestseller.fulfilmentcoreservice.configuration.client.WebClientConfig;
import com.github.tomakehurst.wiremock.http.Fault;
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo;
import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.junit.jupiter.api.Assertions.assertThrows;

@WireMockTest
class PaymentServiceTest {

    private static final String API_VERSION = "/api/v1";
    private static final String PASSWORD = "password";
    private static final String USERNAME = "username";

    private PaymentServiceImpl paymentService;

    @BeforeEach
    void setUp(WireMockRuntimeInfo wmRuntimeInfo) {
        var credentials = Map.of("payment-service",
            ServiceCredential.builder()
                .auth(true)
                .url(wmRuntimeInfo.getHttpBaseUrl())
                .password(PASSWORD)
                .username(USERNAME)
                .build());
        var webClient = new WebClientConfig()
            .paymentServiceClient(new ServiceCredentialConfig(credentials));

        paymentService = new PaymentServiceImpl(webClient, Duration.parse("PT0.1s"));
    }

    @Test
    void validatePaymentCancellation_givenValidRequest_returnsOk() {
        // arrange
        var orderId = "OL1227897";
        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(ok()));

        // act
        paymentService.validateIfPaymentCanBeCancelled(orderId);

        // assert
        verify(1, getRequestedFor(urlEqualTo(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))));
    }

    @Test
    void validatePaymentCancellation_givenValidRequest_tcpErrorHappened() {
        // arrange
        var orderId = "OL1227897";
        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty TCP connection")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER))
            .willSetStateTo("First fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty TCP connection")
            .whenScenarioStateIs("First fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER))
            .willSetStateTo("Second fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty TCP connection")
            .whenScenarioStateIs("Second fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER))
            .willSetStateTo("Third fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty TCP connection")
            .whenScenarioStateIs("Third fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(ok()));

        // act
        paymentService.validateIfPaymentCanBeCancelled(orderId);

        // assert
        final int totalAttempts = 4;
        verify(totalAttempts, getRequestedFor(
            urlEqualTo(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))));
    }

    @Test
    void cancelPayment_givenValidRequest_otherErrors() {
        // arrange
        var orderId = "OL978163789";
        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty HTTP error")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withStatus(HttpStatus.REQUEST_TIMEOUT.value()))
            .willSetStateTo("First fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty HTTP error")
            .whenScenarioStateIs("First fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withStatus(HttpStatus.BAD_GATEWAY.value()))
            .willSetStateTo("Second fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty HTTP error")
            .whenScenarioStateIs("Second fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(aResponse().withStatus(HttpStatus.NOT_FOUND.value()))
            .willSetStateTo("Third fault"));

        stubFor(get(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))
            .inScenario("Faulty HTTP error")
            .whenScenarioStateIs("Third fault")
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(ok()));

        // act & assert
        assertThrows(
            WebClientResponseException.NotFound.class,
            () -> paymentService.validateIfPaymentCanBeCancelled(orderId));

        final int totalAttempts = 3;
        verify(totalAttempts, getRequestedFor(
            urlEqualTo(API_VERSION + "/payments/%s/validate-cancellation".formatted(orderId))));
    }

    @Test
    void getPaymentStatusUpdatedMessage_givenValidRequest_returnsOk() {
        // arrange
        var orderId = "OL1227898";
        stubFor(get(API_VERSION + "/orders/%s/payment-status-updated-message".formatted(orderId))
            .withBasicAuth(USERNAME, PASSWORD)
            .willReturn(ok()));

        // act
        paymentService.getPaymentStatusUpdatedMessage(orderId);

        // assert
        verify(1, getRequestedFor(urlEqualTo(API_VERSION + "/orders/%s/payment-status-updated-message"
            .formatted(orderId))));
    }

}
