package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.EnqueueResult;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderLineExportedStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderLineExportedServiceTest {

    @InjectMocks
    private OrderLineExportedServiceImpl orderLineExportedServiceImpl;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private OrderLineExportedStrategy orderLineExportedStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    @BeforeEach
    public void setUp() {
        when(orderLineExportedStrategy.getMessageType()).thenReturn(OrderLineExported.class);
        context = new OrderStateChangeContext(List.of(orderLineExportedStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void process_givenUnknownOrderId_throwOrderNotFoundException() {
        // arrange
        var orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId("OL12212");
        when(orderService.findById(orderLineExported.getOrderId())).thenThrow(OrderNotFoundException.class);

        // assert & act
        assertThrows(OrderNotFoundException.class, () ->
            orderLineExportedServiceImpl.process(orderLineExported));
    }

    @Test
    void process_givenValidMessage_orderStatusUpdatedAndOrderStatusUpdatedMessageIsPublished() {
        // arrange
        var orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(UUID.randomUUID().toString());
        orderLineExported.setEan(UUID.randomUUID().toString());
        final var quantity = 4;
        orderLineExported.setQuantity(quantity);

        var order = OrderGenerator.createOrder(orderLineExported.getOrderId());
        when(orderService.findById(orderLineExported.getOrderId())).thenReturn(order);

        doNothing().when(orderStateService).apply(any(), any());

        var orderUpdatedStatusPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(Collections.emptyList())
            .build();
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order))
            .thenReturn(orderUpdatedStatusPayload);
        var orderStatusUpdated = new OrderStatusUpdated();
        orderStatusUpdated.setOrderId(order.getOrderId());
        orderStatusUpdated.setPayload(orderUpdatedStatusPayload);
        orderStatusUpdated.setType(OrderStatusUpdated.Type.EXPORTED);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.EXPORTED))
            .thenReturn(orderStatusUpdated);
        when(orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdated)))
            .thenReturn(mock(EnqueueResult.class));

        // act
        orderLineExportedServiceImpl.process(orderLineExported);

        // assert
        verify(orderService).findById(orderLineExported.getOrderId());
        verify(orderStateService).apply(any(Order.class), any());
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
        verify(orderStatusUpdatedConverter).convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.EXPORTED);
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
    }

}
