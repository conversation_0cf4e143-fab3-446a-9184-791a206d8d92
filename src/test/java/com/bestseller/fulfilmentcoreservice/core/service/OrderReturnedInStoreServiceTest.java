package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.EnqueueResult;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderReturnedInStoreStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderReturnedInStoreGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderReturnedInStoreServiceTest {
    @InjectMocks
    private OrderReturnedInStoreServiceImpl orderReturnedInStoreService;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private QueueProducer<OrderReturnedInStore> refundRequestedProducerQueueProducer;

    @Mock
    private OrderReturnInfoService orderReturnInfoService;

    @Mock
    private OrderReturnedInStoreStrategy orderReturnedInStoreStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;OrderReturnedInStore&gt; and QueueProducer&lt;OrderStatusUpdated&gt; are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void injectQueueProducerMocks() {
        ReflectionTestUtils.setField(
            orderReturnedInStoreService,
            "orderStatusUpdatedProducerQueueProducer",
            orderStatusUpdatedProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            orderReturnedInStoreService,
            "refundRequestedProducerQueueProducer",
            refundRequestedProducerQueueProducer
        );
    }

    @BeforeEach
    public void setUp() {
        when(orderReturnedInStoreStrategy.getMessageType()).thenReturn(OrderReturnedInStore.class);
        context = new OrderStateChangeContext(List.of(orderReturnedInStoreStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void process_givenUnknownOrderId_throwOrderNotFoundException() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();

        when(orderService.findById(orderReturnedInStore.getOrderId())).thenThrow(OrderNotFoundException.class);

        // assert & act
        assertThrows(OrderNotFoundException.class, () ->
            orderReturnedInStoreService.process(orderReturnedInStore));
    }

    @Test
    void process_givenValidMessage_orderStatusUpdatedMessageIsPublished() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();

        var order = OrderGenerator.createOrder(orderReturnedInStore.getOrderId());
        OrderLine orderLine = order.getOrderLines().iterator().next();
        orderLine.setLineNumber(1);
        orderLine.setEan("5715513121535");

        when(orderService.findById(orderReturnedInStore.getOrderId())).thenReturn(order);

        doNothing().when(orderStateService).apply(Mockito.eq(order), any());

        var orderUpdatedStatusPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(Collections.emptyList())
            .build();
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order))
            .thenReturn(orderUpdatedStatusPayload);
        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(order.getOrderId())
            .withPayload(orderUpdatedStatusPayload)
            .withType(OrderStatusUpdated.Type.POS_RETURNED_IN_STORE);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.POS_RETURNED_IN_STORE))
            .thenReturn(orderStatusUpdated);
        when(orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdated)))
            .thenReturn(mock(EnqueueResult.class));

        // act
        orderReturnedInStoreService.process(orderReturnedInStore);

        // assert
        verify(orderService).findById(orderReturnedInStore.getOrderId());
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
        verify(orderStatusUpdatedConverter).convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.POS_RETURNED_IN_STORE);
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
    }

    @Test
    void process_givenInvalidMessage_whenApplyThrowEx() {
        // arrange
        var orderReturnedInStore = OrderReturnedInStoreGenerator.generate();
        var order = OrderGenerator.createOrder(orderReturnedInStore.getOrderId());
        when(orderService.findById(orderReturnedInStore.getOrderId())).thenReturn(order);

        // act
        doThrow(StateTransitionException.class).when(orderStateService).apply(any(), any());

        // assert
        assertThrows(StateTransitionException.class,
            () -> orderReturnedInStoreService.process(orderReturnedInStore));
    }
}
