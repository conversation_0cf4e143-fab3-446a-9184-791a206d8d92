package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.service.OrderService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderLineReturnedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerReturnReason;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ReturnType;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderLineReturnedGenerator.WAREHOUSE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class OrderLineReturnedStrategyTest {

    @InjectMocks
    private OrderLineReturnedStrategy orderLineReturnedStrategy;

    @Mock
    private OrderService orderService;

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        // act
        List<OrderStatusUpdateStateChange> result =
            orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order);

        // assert
        assertThat(result).isNotEmpty();
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * REJECTION.
     * both EANs were dispatched by the same warehouse, so, all of them will be returned completely.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedRejected_shouldReturnAllStatesInReturned() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.REJECTION.name());

        final int openQtySecondEAN = 2;

        String ean1 = orderLineReturned.getEan();
        String ean2 = orderLineReturned.getEan() + "1";

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build(),
                OrderLine.builder()
                    .ean(ean2)
                    .lineNumber(orderLineReturned.getLineNumber() + 1)
                    .openQty(openQtySecondEAN)
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act
        var result = orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order)
            .stream()
            .collect(Collectors.groupingBy(OrderStatusUpdateStateChange::getEan, Collectors.toList()));

        // assert
        assertThat(result.values().stream().flatMap(List::stream))
            .hasSize(order.getOrderLines().stream().mapToInt(OrderLine::getOpenQty).sum());
        assertThat(result.get(ean1))
            .hasSize(orderLineReturned.getQuantity());
        assertThat(result.get(ean2))
            .hasSize(openQtySecondEAN);

        result.get(ean1)
            .forEach(item ->
                assertThat(item)
                    .extracting("orderState", "ean",
                        "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                    .containsExactly(OrderState.RETURNED, ean1, ReturnType.REJECTION,
                        CustomerReturnReason.REASON_NOT_STATED_20)
            );

        result.get(ean2)
            .forEach(item ->
                assertThat(item)
                    .extracting("orderState", "ean",
                        "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                    .containsExactly(OrderState.RETURNED, ean2, ReturnType.REJECTION,
                        CustomerReturnReason.REASON_NOT_STATED_20)
            );
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * REJECTION.
     * both EANs were dispatched by different warehouses, so, only one of them will be returned completely.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedRejectedOnlyOneWarehouse_shouldReturnAllStatesInReturned() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.REJECTION.name());

        final int openQtySecondEAN = 2;

        String ean1 = orderLineReturned.getEan();
        String ean2 = orderLineReturned.getEan() + "1";

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build(),
                OrderLine.builder()
                    .ean(ean2)
                    .lineNumber(orderLineReturned.getLineNumber() + 1)
                    .openQty(openQtySecondEAN)
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        "WAREHOUSE2"))
                    .build()
            ))
            .build();

        // act
        var result = orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order)
            .stream()
            .collect(Collectors.groupingBy(OrderStatusUpdateStateChange::getEan, Collectors.toList()));

        // assert
        assertThat(result.values().stream().flatMap(List::stream))
            .hasSize(orderLineReturned.getQuantity());
        assertThat(result.get(ean1))
            .hasSize(orderLineReturned.getQuantity());

        result.get(ean1)
            .forEach(item ->
                assertThat(item)
                    .extracting("orderState", "ean",
                        "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                    .containsExactly(OrderState.RETURNED, ean1, ReturnType.REJECTION,
                        CustomerReturnReason.REASON_NOT_STATED_20)
            );
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * REJECTION.
     * EAN has 0 open quantity, so, nothing is really returned.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedRejectedNoOpenQuantity_shouldReturnAllStatesInReturned() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.REJECTION.name());
        orderLineReturned.setWarehouse(WAREHOUSE);

        String ean1 = orderLineReturned.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .openQty(0)
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act
        var result = orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order)
            .stream()
            .collect(Collectors.groupingBy(OrderStatusUpdateStateChange::getEan, Collectors.toList()));

        // assert
        assertThat(result.values().stream().flatMap(List::stream)).isEmpty();
    }

    /**
     * This test is checking the case when the customer return reason is something unknown.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedRejectedButUnknownCustomerReason_exceptionIsThrown() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.REJECTION.name());
        orderLineReturned.setReturnReason("SOMETHING_WEIRD");
        orderLineReturned.setWarehouse(WAREHOUSE);

        String ean1 = orderLineReturned.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .openQty(1)
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act & assert
        assertThatThrownBy(() -> orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Invalid return reason SOMETHING_WEIRD");
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * CUSTOMER_RETURNED.
     * <p>
     * It will return the same amount of open quantity.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedCustomerReturned_shouldReturnAllStatesInReturned() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.CUSTOMER_RETURN.name());
        orderLineReturned.setWarehouse(WAREHOUSE);

        String ean1 = orderLineReturned.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act
        var result = orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order);

        // assert
        assertThat(result)
            .hasSize(orderLineReturned.getQuantity());

        result.forEach(item ->
            assertThat(item)
                .extracting("orderState", "ean",
                    "orderStatusUpdateInfo.returnType", "orderStatusUpdateInfo.customerReturnReason")
                .containsExactly(OrderState.RETURNED, ean1, ReturnType.CUSTOMER_RETURN,
                    CustomerReturnReason.REASON_NOT_STATED_20)
        );
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * CUSTOMER_RETURNED.
     * <p>
     * It will throw exception because the item couldn't be found.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedCustomerReturnedButItemNotFound_exceptionIsThrown() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.CUSTOMER_RETURN.name());
        orderLineReturned.setWarehouse(WAREHOUSE);

        String ean1 = orderLineReturned.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber() + 1)
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act & assert
        assertThatThrownBy(() -> orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("EAN 1234567891023 not found in order lines for line number 1");
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * CUSTOMER_RETURNED.
     * <p>
     * It will throw exception because the item couldn't be found.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedForVirtualItem_exceptionIsThrown() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.CUSTOMER_RETURN.name());
        orderLineReturned.setWarehouse(WAREHOUSE);

        String ean1 = orderLineReturned.getEan();

        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(ean1)
                    .lineNumber(orderLineReturned.getLineNumber())
                    .virtualProduct(true)
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(generateOrderLineQtyStatus(
                        orderLineReturned.getQuantity(),
                        OrderState.DISPATCHED,
                        WAREHOUSE))
                    .build()
            ))
            .build();

        // act & assert
        assertThatThrownBy(() -> orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("EAN 1234567891023 not found in order lines for line number 1");
    }

    /**
     * This test is verifying if the state change is correctly defined for a given order line returned and the type is
     * something weird.
     * <p>
     * It will throw exception because the item couldn't be found.
     */
    @Test
    void defineStateChangeFor_givenOrderLineReturnedWeirdReturnedType_exceptionIsThrown() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType("WEIRD_RETURN_TYPE");
        var order = Order.builder().build();

        // act & assert
        assertThatThrownBy(() -> orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessageContaining("No enum constant")
            .hasMessageContaining("WEIRD_RETURN_TYPE");
    }

    @Test
    void defineOrderStateChange_givenOrderLineQtyStatusesWithoutOrderFulfillmentPartReference_nothingReturn() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();
        orderLineReturned.setReturnType(ReturnType.REJECTION.name());
        var order = Order.builder()
            .orderLines(List.of(
                OrderLine.builder()
                    .ean(orderLineReturned.getEan())
                    .openQty(orderLineReturned.getQuantity())
                    .orderLineQtyStatus(
                        Collections.nCopies(
                            orderLineReturned.getQuantity(),
                            OrderLineQtyStatus.builder()
                                .orderState(OrderState.RETURNED)
                                .orderFulfillmentPart(null)
                                .build())
                    )
                    .build()))
            .build();

        // act
        var result = orderLineReturnedStrategy.defineOrderStateChange(orderLineReturned, order);

        // assert
        assertThat(result).isEmpty();
    }

    private List<OrderLineQtyStatus> generateOrderLineQtyStatus(int quantity,
                                                                OrderState orderState,
                                                                String warehouse) {
        return Collections.nCopies(quantity,
            OrderLineQtyStatus.builder()
                .orderState(orderState)
                .orderFulfillmentPart(OrderFulfillmentPart.builder()
                    .fulfillmentNode(warehouse)
                    .build())
                .build());
    }
}
