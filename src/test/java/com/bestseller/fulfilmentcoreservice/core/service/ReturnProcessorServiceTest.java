package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReturnProcessorServiceTest {

    @InjectMocks
    private ReturnProcessorServiceImpl returnProcessorService;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private OrderRefundService orderRefundService;

    @Test
    void processReturns_givenLinesBeingReturnedFromDd_processIsCalledMoreThanOnce() {
        // arrange
        var orderId1 = "12233213";
        var orderId2 = "87613688";
        when(orderRepository.findAllReturnableOrderIds()).thenReturn(List.of(orderId1, orderId2));

        // act
        returnProcessorService.processReturns();

        // assert
        verify(orderRefundService).processRefund(orderId1);
        verify(orderRefundService).processRefund(orderId2);
    }

    @Test
    void processReturns_givenLinesBeingReturnedFromDd_exceptionIsIgnoredAndFlowIsNotBlocked() {
        // arrange
        var orderId1 = "12233213";
        var orderId2 = "87613688";
        when(orderRepository.findAllReturnableOrderIds()).thenReturn(List.of(orderId1, orderId2));
        doThrow(new RuntimeException("error")).when(orderRefundService).processRefund(orderId1);

        // act
        returnProcessorService.processReturns();

        // assert
        verify(orderRefundService).processRefund(orderId1);
        verify(orderRefundService).processRefund(orderId2);
    }

    @Test
    void processReturns_givenNotLinesIsReturnedFromDd_nothingIsProcessed() {
        // arrange
        when(orderRepository.findAllReturnableOrderIds()).thenReturn(List.of());

        // act
        returnProcessorService.processReturns();

        // assert
        verifyNoInteractions(orderRefundService);
    }

    @Test
    void processReturns_givenExceptionWhileRetrievingReturns_logsError() {
        // arrange
        doThrow(new RuntimeException("error")).when(orderRepository).findAllReturnableOrderIds();

        // act
        returnProcessorService.processReturns();

        // assert
        verifyNoInteractions(orderRefundService);
    }

}
