package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.EnqueueResult;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderLineReturnedStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderLineReturnedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderLineReturnedServiceTest {

    @InjectMocks
    private OrderLineReturnedServiceImpl orderLineReturnedService;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    @Mock
    private TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    @Mock
    private OrderReturnInfoService orderReturnInfoService;

    @Mock
    private OrderLineReturnedStrategy orderLineReturnedStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    @BeforeEach
    public void setUp() {
        when(orderLineReturnedStrategy.getMessageType()).thenReturn(OrderLineReturned.class);
        context = new OrderStateChangeContext(List.of(orderLineReturnedStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void process_givenUnknownOrderId_throwOrderNotFoundException() {
        // arrange
        var orderLineReturned = new OrderLineReturned();
        orderLineReturned.setOrderId("OL12212");
        when(orderService.findById(orderLineReturned.getOrderId())).thenThrow(OrderNotFoundException.class);

        // act & assert
        assertThrows(OrderNotFoundException.class, () ->
            orderLineReturnedService.process(orderLineReturned));
    }

    @Test
    void process_givenValidMessage_orderStatusUpdatedMessageIsPublished() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();

        var order = OrderGenerator.createOrder(orderLineReturned.getOrderId());
        OrderLine orderLine = order.getOrderLines().iterator().next();
        orderLine.setLineNumber(orderLineReturned.getLineNumber());
        orderLine.setEan(orderLineReturned.getEan());


        when(orderService.findById(orderLineReturned.getOrderId())).thenReturn(order);

        doNothing().when(orderStateService).apply(Mockito.eq(order), any());

        var orderUpdatedStatusPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(Collections.emptyList())
            .build();
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order))
            .thenReturn(orderUpdatedStatusPayload);
        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(order.getOrderId())
            .withPayload(orderUpdatedStatusPayload)
            .withType(OrderStatusUpdated.Type.RETURNED);
        when(orderStatusUpdatedConverter.convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.RETURNED))
            .thenReturn(orderStatusUpdated);
        when(orderStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(orderStatusUpdated)))
            .thenReturn(mock(EnqueueResult.class));

        // act
        orderLineReturnedService.process(orderLineReturned);

        // assert
        verify(orderService).findById(orderLineReturned.getOrderId());
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(orderStatusUpdated));
        verify(orderStatusUpdatedConverter).convertTo(
            order, orderUpdatedStatusPayload, OrderStatusUpdated.Type.RETURNED);
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
        verify(orderReturnInfoService).updateLatestReturnDate(orderLineReturned.getOrderId(),
            orderLineReturned.getEffectiveDate().toLocalDate());
    }

    @Test
    void process_givenInvalidMessage_whenApplyThrowEx() {
        // arrange
        var orderLineReturned = OrderLineReturnedGenerator.generate();

        var order = OrderGenerator.createOrder(orderLineReturned.getOrderId());
        when(orderService.findById(orderLineReturned.getOrderId())).thenReturn(order);
        doThrow(StateTransitionException.class).when(orderStateService).apply(any(), any());
        // act
        assertThrows(StateTransitionException.class,
            () -> orderLineReturnedService.process(orderLineReturned));
    }
}
