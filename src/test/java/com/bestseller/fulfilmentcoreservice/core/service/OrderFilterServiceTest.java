package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFilter;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderFilterRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderFilterServiceTest {

    @InjectMocks
    private OrderFilterServiceImpl orderFilterService;

    @Mock
    private OrderFilterRepository orderFilterRepository;

    @Captor
    private ArgumentCaptor<OrderFilter> orderFilterCaptor;

    @Test
    void createFilter_givenFilterAlreadyExisting_anotherFilterIsNotCreated() {
        // arrange
        var orderId = "OL12220";
        var orderFilter = OrderFilter.builder()
            .orderId(orderId)
            .build();
        when(orderFilterRepository.findByOrderId(orderId)).thenReturn(Optional.of(orderFilter));

        // act
        orderFilterService.createFilter(orderId);

        // assert
        verify(orderFilterRepository).findByOrderId(orderId);
        verifyNoMoreInteractions(orderFilterRepository);
    }

    @Test
    void createFilter_givenFilterNotExisting_filterIsCreated() {
        // arrange
        var orderId = "OL12221";
        when(orderFilterRepository.findByOrderId(orderId)).thenReturn(Optional.empty());
        when(orderFilterRepository.save(orderFilterCaptor.capture())).thenReturn(null);

        // act
        orderFilterService.createFilter(orderId);

        // assert
        var orderFilter = orderFilterCaptor.getValue();
        assertThat(orderFilter.getOrderId())
            .isEqualTo(orderId);

        verify(orderFilterRepository).save(any(OrderFilter.class));
    }

    @Test
    void removeFilter_givenFilterAlreadyExisting_filterIsRemoved() {
        // arrange
        var orderId = "OL12222";
        var orderFilter = OrderFilter.builder()
            .orderId(orderId)
            .build();
        when(orderFilterRepository.findByOrderId(orderId)).thenReturn(Optional.of(orderFilter));

        // act
        orderFilterService.removeFilter(orderId);

        // assert
        verify(orderFilterRepository).delete(orderFilter);
    }

    @Test
    void removeFilter_givenFilterNotExisting_filterIsNotRemoved() {
        // arrange
        var orderId = "OL12223";
        when(orderFilterRepository.findByOrderId(orderId)).thenReturn(Optional.empty());

        // act
        orderFilterService.removeFilter(orderId);

        // assert
        verify(orderFilterRepository, never()).delete(any(OrderFilter.class));
    }

    @Test
    void isFilterExist_isTrue() {
        // arrange
        var orderId = "OL12223";
        when(orderFilterRepository.existsByOrderId(orderId)).thenReturn(true);

        // act & assert
        assertTrue(orderFilterService.isFilterExist(orderId));
    }
}
