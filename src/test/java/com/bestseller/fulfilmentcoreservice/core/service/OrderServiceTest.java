package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderFinalizedConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.OrderViewConverter;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderIdOnly;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderManualUpdate.OrderManualUpdate;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.SimpleTransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    private static final int FINALIZE_ORDERS_PAGE_SIZE = 100;

    @InjectMocks
    private OrderServiceImpl orderService;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private OrderViewConverter orderViewConverter;

    @Mock
    private OrderFinalizedConverter orderFinalizedConverter;

    @Mock
    private QueueProducer<OrderFinalized> orderFinalizedProducerQueueProducer;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private QueueProducer<OrderManualUpdate> orderManualUpdateProducerQueueProducer;

    @Mock
    private Clock clock;

    @Captor
    private ArgumentCaptor<EnqueueParams<OrderManualUpdate>> orderManualUpdateEnqueueParamsCaptor;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;OrderFinalized&gt; and QueueProducer&lt;OrderManualUpdate&gt; are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(
            orderService,
            "orderFinalizedProducerQueueProducer",
            orderFinalizedProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            orderService,
            "orderManualUpdateProducerQueueProducer",
            orderManualUpdateProducerQueueProducer
        );
    }

    @Test
    void findOrder_givenValidOrderId_orderIsReturned() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderView = OrderView.builder().id(orderId).build();
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(orderViewConverter.convert(order)).thenReturn(orderView);

        // act
        var result = orderService.findOrder(orderId);

        // assert
        assertThat(result)
            .as("It's present")
            .isPresent();
        assertThat(result.get())
            .as("The return matches")
            .isEqualTo(orderView);
    }

    @Test
    void findOrder_givenValidOrderId_orderIsNotReturned() {
        // arrange
        var orderId = "OL12212";
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // act
        var result = orderService.findOrder(orderId);

        // assert
        assertThat(result)
            .as("It's not present")
            .isNotPresent();
    }

    @Test
    void finalizeOrders_givenOrdersToBeFinalized_ordersAreFinalized() {
        // arrange
        List<Order> orders = List.of(
            OrderGenerator.createOrder("OL12212"),
            OrderGenerator.createOrder("OL12211"));

        List<OrderIdOnly> orderIdOnlyList = orders
            .stream()
            .map(order -> new OrderIdOnly(order.getOrderId()))
            .toList();
        when(orderRepository.findOrdersToBeFinalized(0, FINALIZE_ORDERS_PAGE_SIZE)).thenReturn(orderIdOnlyList);
        when(orderFinalizedConverter.toOrderFinalized(any())).thenReturn(new OrderFinalized());
        when(orderRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(orderRepository.findById("OL12212")).thenReturn(Optional.of(orders.get(0)));
        when(orderRepository.findById("OL12211")).thenReturn(Optional.of(orders.get(1)));

        ReflectionTestUtils.setField(orderService, "finalizeOrdersQueryPageSize", FINALIZE_ORDERS_PAGE_SIZE);

        // Act: Simulate the transaction template execution for each order finalization
        when(transactionTemplate.execute(any())).thenAnswer(invocation -> {
            var callback = invocation.getArgument(0, TransactionCallback.class);
            return callback.doInTransaction(new SimpleTransactionStatus());
        });

        // act
        var result = orderService.finalizeOrders();

        // assert
        assertThat(result)
            .as("The number of orders finalized is correct")
            .isEqualTo(orders.size());

        for (var order : orders) {
            assertThat(order.isFinalized())
                .as("The order is finalized")
                .isTrue();
            verify(orderRepository).save(order);
        }

        verify(orderFinalizedProducerQueueProducer, times(orders.size())).enqueue(any());
    }

    @Test
    void finalizeOrders_givenOrdersToBeFinalizedAndExceptionOccurs_someOrdersAreFinalized() {
        // arrange
        List<Order> orders = List.of(
            OrderGenerator.createOrder("OL12212"),
            OrderGenerator.createOrder("OL12211"));

        List<OrderIdOnly> orderIdOnlyList = orders
            .stream()
            .map(order -> new OrderIdOnly(order.getOrderId()))
            .toList();
        when(orderRepository.findOrdersToBeFinalized(0, FINALIZE_ORDERS_PAGE_SIZE)).thenReturn(orderIdOnlyList);
        when(orderFinalizedConverter.toOrderFinalized(any())).thenReturn(new OrderFinalized());
        when(orderRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        when(orderRepository.findById("OL12212")).thenReturn(Optional.of(orders.getFirst()));
        when(orderRepository.findById("OL12211")).thenThrow(new RuntimeException());

        ReflectionTestUtils.setField(orderService, "finalizeOrdersQueryPageSize", FINALIZE_ORDERS_PAGE_SIZE);

        // Act: Simulate the transaction template execution for each order finalization
        when(transactionTemplate.execute(any(TransactionCallback.class))).thenAnswer(invocation -> {
            TransactionCallback<?> callback = invocation.getArgument(0);
            return callback.doInTransaction(new SimpleTransactionStatus());
        });

        // act
        var result = orderService.finalizeOrders();

        // assert
        assertThat(result)
            .as("The number of orders finalized is correct")
            .isEqualTo(1);

        for (var order : orders) {
            if (order.getOrderId().equals("OL12212")) {
                assertThat(order.isFinalized())
                    .as("The order is finalized")
                    .isTrue();
                verify(orderRepository).save(order);
            } else {
                assertThat(order.isFinalized())
                    .as("The order is not finalized")
                    .isFalse();
            }

        }

        verify(orderFinalizedProducerQueueProducer, times(1)).enqueue(any());
    }

    @Test
    void isDuplicateRequest_duplicateRequest() {
        // arrange
        var orderId = "OL12212";
        var targetState = OrderState.PROCESSING;
        when(orderRepository.getMinOrderStatus(orderId)).thenReturn("DISPATCHED");

        // act
        var result = orderService.isDuplicateRequest(orderId, targetState);

        // assert
        assertThat(result)
            .as("The result is correct")
            .isTrue();
    }

    @Test
    void isDuplicateRequest_notDuplicateRequest() {
        // arrange
        var orderId = "OL12212";
        var targetState = OrderState.RETURNED;
        when(orderRepository.getMinOrderStatus(orderId)).thenReturn("DISPATCHED");

        // act
        var result = orderService.isDuplicateRequest(orderId, targetState);

        // assert
        assertThat(result)
            .as("The result is correct")
            .isFalse();
    }

    @Test
    void isDuplicateRequest_minStatusIsNull_notDuplicateRequest() {
        // arrange
        var orderId = "OL12212";
        var targetState = OrderState.RETURNED;
        when(orderRepository.getMinOrderStatus(orderId)).thenReturn(null);

        // act
        var result = orderService.isDuplicateRequest(orderId, targetState);

        // assert
        assertThat(result)
            .as("The result is correct")
            .isFalse();
    }

    @Test
    void isDuplicateRequest_duplicateRequestWithEan() {
        // arrange
        var orderId = "OL12212";
        var ean = "1234567890123";
        var targetState = OrderState.PROCESSING;
        when(orderRepository.getOrderStateByOrderId(orderId, ean)).thenReturn(List.of("DISPATCHED", "RETURNED"));

        // act
        var result = orderService.isDuplicateRequest(orderId, ean, targetState);

        // assert
        assertThat(result)
            .as("The result is correct")
            .isTrue();
    }

    @Test
    void isDuplicateRequest_notDuplicateRequestWithEan() {
        // arrange
        var orderId = "OL12212";
        var ean = "1234567890123";
        var targetState = OrderState.DISPATCHED;
        when(orderRepository.getOrderStateByOrderId(orderId, ean)).thenReturn(List.of("DISPATCHED", "PROCESSING"));

        // act
        var result = orderService.isDuplicateRequest(orderId, ean, targetState);

        // assert
        assertThat(result)
            .as("The result is correct")
            .isFalse();
    }

    @Test
    void isDuplicateRequest_orderPartsRouted__orderNotFound_returnsFalse() {
        // arrange
        var orderPartsRouted = new OrderPartsRouted();
        orderPartsRouted.setOrderId("OL12212");
        when(orderRepository.findById(orderPartsRouted.getOrderId())).thenReturn(Optional.empty());

        // act
        var result = orderService.isDuplicateRequest(orderPartsRouted);

        // assert
        assertThat(result).isFalse();
    }

    @Test
    void isDuplicateRequest_noMatchingEan_returnsFalse() {
        // arrange
        var orderId = "OL12212";
        var orderPartsRouted = new OrderPartsRouted();
        orderPartsRouted.setOrderId(orderId);
        orderPartsRouted.setOrderLines(List.of(new OrderLine().withEan("1234567890123")));
        var order = OrderGenerator.createOrder(orderId);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.isDuplicateRequest(orderPartsRouted);

        // assert
        assertThat(result).isFalse();
    }

    @Test
    void isDuplicateRequest_matchingEanAndFulfillmentNode_returnsTrue() {
        // arrange
        var orderId = "OL12212";
        var ean = "1234567890123";
        var fulfillmentNode = "NODE1";
        var orderPartsRouted = new OrderPartsRouted();
        orderPartsRouted.setOrderId(orderId);
        orderPartsRouted.setOrderLines(List.of(new OrderLine().withEan(ean)));
        orderPartsRouted.setFulfillmentNode(fulfillmentNode);
        var order = OrderGenerator.createOrder(orderId);
        order.getOrderLines().get(0).setEan(ean);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setFulfillmentNode(fulfillmentNode);

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.isDuplicateRequest(orderPartsRouted);

        // assert
        assertThat(result).isTrue();
    }

    @Test
    void isDuplicateRequest_matchingEanButDifferentFulfillmentNode_returnsFalse() {
        // arrange
        var orderId = "OL12212";
        var ean = "1234567890123";
        var savedFulfillmentNode = "NODE1";
        var incomingFulfillmentNode = "NODE2";
        var orderPartsRouted = new OrderPartsRouted();
        orderPartsRouted.setOrderId(orderId);
        orderPartsRouted.setOrderLines(List.of(new OrderLine().withEan(ean)));
        orderPartsRouted.setFulfillmentNode(incomingFulfillmentNode);
        var order = OrderGenerator.createOrder(orderId);
        order.getOrderLines().get(0).setEan(ean);
        order.getOrderLines().get(0).getOrderLineQtyStatus().get(0).getOrderFulfillmentPart()
            .setFulfillmentNode(savedFulfillmentNode);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.isDuplicateRequest(orderPartsRouted);

        // assert
        assertThat(result).isFalse();
    }

    @Test
    void findOrderById_givenValidOrderId_orderIsNotReturned() {
        // arrange
        var orderId = "OL12212";
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // act
        var result = orderService.findOrderById(orderId);

        // assert
        assertThat(result).isNotPresent();
    }

    @Test
    void findOrderById_givenValidOrderId_orderIsReturned() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.findOrderById(orderId);

        // assert
        assertThat(result).isPresent();
        assertThat(result.get())
            .usingRecursiveComparison()
            .isEqualTo(order);
    }

    @Test
    void orderService_givenValidId_updateEmailAddress() {
        // arrange
        var orderId = "OL12222";
        var customerId = "123456";
        var emailAddress = "<EMAIL>";

        var order = Order.builder()
            .orderId(orderId)
            .customer(
                Customer.builder()
                    .customerId(customerId)
                    .build()
            )
            .build();
        Instant now = Instant.now();
        var orderManualUpdate = new OrderManualUpdate()
            .withOrderId(orderId)
            .withKey("email")
            .withValue(emailAddress)
            .withTimestamp(now.atZone(ZoneOffset.UTC));
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(clock.instant()).thenReturn(now);
        when(clock.getZone()).thenReturn(ZoneOffset.UTC);

        // act
        orderService.updateEmailAddress(orderId, emailAddress);

        // assert
        verify(orderRepository, times(1)).save(order);
        verify(orderManualUpdateProducerQueueProducer).enqueue(orderManualUpdateEnqueueParamsCaptor.capture());
        assertThat(orderManualUpdateEnqueueParamsCaptor.getValue().getPayload())
            .usingRecursiveComparison()
            .isEqualTo(orderManualUpdate);
    }

    @Test
    void validateIfOrderCanBeCancelled_givenValidOrderId_orderIsPresent() {
        // arrange
        var orderId = "OL12222";
        var order = OrderGenerator.createOrder(orderId);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.validateIfOrderCanBeCancelled(orderId);

        // assert
        assertThat(result).isTrue();
    }

    @Test
    void validateIfOrderCanBeCancelled_givenInvalidOrderId_orderIsNotPresent() {
        // arrange
        var orderId = "OL12222";
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // act
        var result = orderService.validateIfOrderCanBeCancelled(orderId);

        // assert
        assertThat(result).isFalse();
    }

    @ParameterizedTest
    @MethodSource("validateIfOrderCanBeCancelledData")
    void validateIfOrderCanBeCancelled_givenValidOrderId_orderInState(
        OrderState orderState, boolean expectedResult) {
        // arrange
        var orderId = "OL12222";
        var order = OrderGenerator.createOrder(orderId);
        order.setMinStatus(orderState);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // act
        var result = orderService.validateIfOrderCanBeCancelled(orderId);

        // assert
        assertThat(result).isEqualTo(expectedResult);
    }

    private static Stream<Arguments> validateIfOrderCanBeCancelledData() {
        return Stream.of(
            Arguments.of(OrderState.PLACED, true),
            Arguments.of(OrderState.BLOCKED, true),
            Arguments.of(OrderState.ROUTING, false),
            Arguments.of(OrderState.ROUTED, false),
            Arguments.of(OrderState.PROCESSING, false),
            Arguments.of(OrderState.DISPATCHED, false),
            Arguments.of(OrderState.RETURNED, false),
            Arguments.of(OrderState.RETURN_PROCESSED, false),
            Arguments.of(OrderState.POS_RETURNED_IN_STORE, false),
            Arguments.of(OrderState.CANCELLED, false),
            Arguments.of(OrderState.CANCELLATION_REFUND, false),
            Arguments.of(OrderState.RETURNED_IN_STORE, false)
        );
    }
}
