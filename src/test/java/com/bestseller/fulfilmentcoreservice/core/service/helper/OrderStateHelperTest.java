package com.bestseller.fulfilmentcoreservice.core.service.helper;

import com.bestseller.fulfilmentcoreservice.persistence.dto.StateChange;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.logistics.statetransition.OrderState.BLOCKED;
import static com.logistics.statetransition.OrderState.CANCELLED;
import static com.logistics.statetransition.OrderState.PLACED;
import static com.logistics.statetransition.OrderState.PURGE;
import static com.logistics.statetransition.OrderState.ROUTING;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderStateHelperTest {
    private static final int QUANTITY_OF_5 = 5;
    private static final int QUANTITY_OF_3 = 3;
    private static final int QUANTITY_OF_4 = 4;
    private static final int QUANTITY_OF_2 = 2;
    private static final int QUANTITY_OF_1 = 1;

    @InjectMocks
    private OrderStateHelperImpl orderStateHelper;

    @Test
    void getMapEanToStateChange_whenStateChangesIsNull_returnsEmptyMap() {
        // arrange

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChange(null);

        // assert
        assertThat(result).as("Result is empty").isEmpty();
    }

    @Test
    void getMapEanToStateChange_whenStateChangesIsEmpty_returnsEmptyMap() {
        // arrange
        List<StateChange> stateChanges = new ArrayList<>();

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChange(stateChanges);

        // assert
        assertThat(result).isEmpty();
    }

    @Test
    void getMapEanToStateChange_whenStateChangesHasOneElement_returnsMapWithOneEntry() {
        // arrange
        List<StateChange> stateChanges = new ArrayList<>();
        StateChange stateChange = new StateChange("EAN1", PLACED);
        stateChanges.add(stateChange);

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChange(stateChanges);

        // assert
        assertThat(result).as("Result has only 1 item").hasSize(1);
        assertThat(result.get("EAN1")).as("Result has the correct state").containsOnly(stateChange);
    }

    @Test
    void getMapEanToStateChange_whenStateChangesHasMultipleElements_returnsMapWithCorrectEntries() {
        // arrange
        List<StateChange> stateChanges = new ArrayList<>();
        StateChange stateChange1 = new StateChange("EAN1", PLACED);
        StateChange stateChange2 = new StateChange("EAN2", BLOCKED);
        StateChange stateChange3 = new StateChange("EAN1", ROUTING);
        stateChanges.add(stateChange1);
        stateChanges.add(stateChange2);
        stateChanges.add(stateChange3);

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChange(stateChanges);

        // assert
        assertThat(result).hasSize(QUANTITY_OF_2);
        assertThat(result.get("EAN1")).as("EAN1 should contains Placed and Routing states")
            .containsExactly(stateChange1, stateChange3);
        assertThat(result.get("EAN2")).as("EAN2 should contains Blocked state").containsOnly(stateChange2);
    }

    @Test
    public void getMapEanToStateChangeFromOrderLine_whenOrderLinesIsNull_returnsEmptyMap() {
        // arrange

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChangeFromOrderLine(null, PLACED);

        // assert
        assertThat(result).as("Result is empty").isEmpty();
    }

    @Test
    void getMapEanToStateChangeFromOrderLine_whenOrderLineQuantityStatusIsNotEmpty_returnsMapWithCorrectEntries() {
        // arrange
        OrderLine orderLine1 = new OrderLine();
        orderLine1.setEan("EAN1");
        orderLine1.setOriginalQty(QUANTITY_OF_1);
        OrderLineQtyStatus qtyStatus1 = new OrderLineQtyStatus();
        qtyStatus1.setOrderState(PLACED);
        orderLine1.setOrderLineQtyStatus(List.of(qtyStatus1));

        OrderLine orderLine2 = new OrderLine();
        orderLine2.setEan("EAN2");
        orderLine2.setOriginalQty(QUANTITY_OF_2);

        OrderLineQtyStatus qtyStatus2 = new OrderLineQtyStatus();
        qtyStatus2.setOrderState(PLACED);

        OrderLineQtyStatus qtyStatus3 = new OrderLineQtyStatus();
        qtyStatus3.setOrderState(PLACED);

        orderLine2.setOrderLineQtyStatus(List.of(qtyStatus2, qtyStatus3));

        List<OrderLine> orderLines = List.of(orderLine1, orderLine2);

        // act
        Map<String, List<StateChange>> result = orderStateHelper.getMapEanToStateChangeFromOrderLine(orderLines,
            CANCELLED);

        // assert
        assertThat(result).hasSize(2);
        assertThat(result.get("EAN1")).hasSize(QUANTITY_OF_1);
        assertThat(result.get("EAN2")).hasSize(QUANTITY_OF_2);
    }

    @Test
    public void updateMinMax_givenPlacedCancelledAndPurgedStates_setsMinAndMaxStatusCorrectly() {
        // arrange

        OrderLineQtyStatus qtyStatus1 = OrderLineQtyStatus.builder().orderState(PLACED).build();
        OrderLineQtyStatus qtyStatus2 = OrderLineQtyStatus.builder().orderState(CANCELLED).build();
        OrderLineQtyStatus qtyStatus3 = OrderLineQtyStatus.builder().orderState(PURGE).build();

        OrderLine orderLine1 = new OrderLine();
        orderLine1.setOriginalQty(QUANTITY_OF_5);
        orderLine1.setOrderLineQtyStatus(List.of(qtyStatus1, qtyStatus2, qtyStatus3));

        OrderLine orderLine2 = new OrderLine();
        orderLine2.setOriginalQty(QUANTITY_OF_3);
        orderLine2.setOrderLineQtyStatus(List.of(qtyStatus1, qtyStatus2));

        Order order = Order.builder()
            .orderLines(List.of(orderLine1, orderLine2))
            .build();

        // act
        orderStateHelper.updateMinMax(order);

        // assert
        assertThat(order.getMinStatus())
            .as("Order minstatus should be placed")
            .isEqualTo(PLACED);
        assertThat(order.getMaxStatus())
            .as("Order maxstatus should be purged")
            .isEqualTo(PURGE);
        assertThat(orderLine1.getOpenQty())
            .as("Open quantity should be 4")
            .isEqualTo(QUANTITY_OF_4);
        assertThat(orderLine2.getOpenQty())
            .as("Open quantity should be 2")
            .isEqualTo(QUANTITY_OF_2);
    }

    @Test
    public void updateMinMax_givenCancelledAndPurgedStates_setsMinAndMaxStatusCorrectly() {
        // arrange
        OrderState cancelledState = CANCELLED;
        OrderState purgeState = PURGE;

        OrderLineQtyStatus qtyStatus2 = new OrderLineQtyStatus();
        qtyStatus2.setOrderState(cancelledState);
        OrderLineQtyStatus qtyStatus3 = new OrderLineQtyStatus();
        qtyStatus3.setOrderState(purgeState);

        OrderLine orderLine1 = new OrderLine();
        orderLine1.setOriginalQty(QUANTITY_OF_5);
        orderLine1.setOrderLineQtyStatus(List.of(qtyStatus2, qtyStatus3));

        OrderLine orderLine2 = new OrderLine();
        orderLine2.setOriginalQty(QUANTITY_OF_3);
        orderLine2.setOrderLineQtyStatus(List.of(qtyStatus2));

        Order order = Order.builder()
            .orderLines(List.of(orderLine1, orderLine2))
            .build();

        // act
        orderStateHelper.updateMinMax(order);

        // assert
        assertThat(order.getMinStatus())
            .as("Order minstatus should be cancelled")
            .isEqualTo(cancelledState);
        assertThat(order.getMaxStatus())
            .as("Order maxstatus should be purged")
            .isEqualTo(purgeState);
        assertThat(orderLine1.getOpenQty())
            .as("Open quantity should be 4")
            .isEqualTo(QUANTITY_OF_4);
        assertThat(orderLine2.getOpenQty())
            .as("Open quantity should be 2")
            .isEqualTo(QUANTITY_OF_2);
    }

    @Test
    public void updateMinMax_givenPurgeAndCancelledStates_setsMinAndMaxStatusCorrectly() {
        // arrange

        OrderState cancelledState = CANCELLED;
        OrderState purgeState = PURGE;

        OrderLineQtyStatus qtyStatus2 = new OrderLineQtyStatus();
        qtyStatus2.setOrderState(cancelledState);
        OrderLineQtyStatus qtyStatus3 = new OrderLineQtyStatus();
        qtyStatus3.setOrderState(purgeState);

        OrderLine orderLine1 = new OrderLine();
        orderLine1.setOriginalQty(QUANTITY_OF_5);
        orderLine1.setOrderLineQtyStatus(List.of(qtyStatus3, qtyStatus2));

        Order order = Order.builder()
            .orderLines(List.of(orderLine1))
            .build();

        // act
        orderStateHelper.updateMinMax(order);

        // assert
        assertThat(order.getMinStatus())
            .as("Order minstatus should be cancelled")
            .isEqualTo(cancelledState);
        assertThat(order.getMaxStatus())
            .as("Order maxstatus should be purged")
            .isEqualTo(purgeState);
        assertThat(orderLine1.getOpenQty())
            .as("Open quantity should be 4")
            .isEqualTo(QUANTITY_OF_4);
        assertThat(orderLine1.getOpenQty())
            .as("Open quantity should be 3")
            .isEqualTo(QUANTITY_OF_4);
    }
}
