package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderFulfilmentPartUpdaterServiceTest {

    private static final int PART_NUMBER_1 = 123;
    private static final int PART_NUMBER_2 = 456;

    @InjectMocks
    private OrderFulfillmentPartUpdaterServiceImpl orderFulfillmentPartUpdaterService;

    @Mock
    private OrderFulfillmentPart orderFulfillmentPartFromDB;

    @Mock
    private OrderFulfillmentPart orderFulfillmentPartFromMessage;

    @Test
    void updateOrderFulfillmentPart() {
        // Arrange
        when(orderFulfillmentPartFromMessage.getPartNumber()).thenReturn(PART_NUMBER_1);
        when(orderFulfillmentPartFromMessage.getFulfillmentNode()).thenReturn("Node1");
        when(orderFulfillmentPartFromMessage.getOrangePrinted()).thenReturn(
            LocalDate.parse("2023-10-28"));
        when(orderFulfillmentPartFromMessage.getStoreId()).thenReturn(1);
        when(orderFulfillmentPartFromMessage.getCarrierName()).thenReturn("Carrier");
        when(orderFulfillmentPartFromMessage.getReturnTrackingNumber()).thenReturn("Return123");
        when(orderFulfillmentPartFromMessage.getDispatchDate()).thenReturn(
            LocalDateTime.parse("2023-10-28T00:00:00"));
        when(orderFulfillmentPartFromMessage.getTrackingNumber()).thenReturn("Track123");

        // Act
        orderFulfillmentPartUpdaterService.update(orderFulfillmentPartFromDB,
            orderFulfillmentPartFromMessage);

        //Assert
        verify(orderFulfillmentPartFromDB).setPartNumber(PART_NUMBER_1);
        verify(orderFulfillmentPartFromDB).setFulfillmentNode("Node1");
        verify(orderFulfillmentPartFromDB).setStoreId(1);
        verify(orderFulfillmentPartFromDB).setDispatchDate(LocalDateTime.parse("2023-10-28T00:00:00"));
        verify(orderFulfillmentPartFromDB).setOrangePrinted(LocalDate.parse("2023-10-28"));
        verify(orderFulfillmentPartFromDB).setCarrierName("Carrier");
        verify(orderFulfillmentPartFromDB).setReturnTrackingNumber("Return123");
        verify(orderFulfillmentPartFromDB).setTrackingNumber("Track123");
    }

    @Test
    public void testUpdatePartNumberWhenMessagePartNumberNotNullAndDbPartNumberNull() {
        when(orderFulfillmentPartFromMessage.getPartNumber()).thenReturn(PART_NUMBER_1);
        when(orderFulfillmentPartFromDB.getPartNumber()).thenReturn(null);

        orderFulfillmentPartUpdaterService.update(orderFulfillmentPartFromDB,
            orderFulfillmentPartFromMessage);

        verify(orderFulfillmentPartFromDB).setPartNumber(PART_NUMBER_1);
    }

    @Test
    public void testUpdatePartNumberWhenMessagePartNumberNotNullAndDbPartNumberZero() {
        when(orderFulfillmentPartFromMessage.getPartNumber()).thenReturn(PART_NUMBER_1);
        when(orderFulfillmentPartFromDB.getPartNumber()).thenReturn(0);

        orderFulfillmentPartUpdaterService.update(orderFulfillmentPartFromDB,
            orderFulfillmentPartFromMessage);

        verify(orderFulfillmentPartFromDB).setPartNumber(PART_NUMBER_1);
    }

    @Test
    public void testUpdatePartNumberWhenMessagePartNumberNull() {
        when(orderFulfillmentPartFromMessage.getPartNumber()).thenReturn(null);
        // Use lenient() for the unnecessary stubbing
        lenient().when(orderFulfillmentPartFromDB.getPartNumber()).thenReturn(PART_NUMBER_1);

        orderFulfillmentPartUpdaterService.update(orderFulfillmentPartFromDB,
            orderFulfillmentPartFromMessage);

        // Ensure setPartNumber is not called when message part number is null
        verify(orderFulfillmentPartFromDB, never()).setPartNumber(anyInt());
    }

    @Test
    public void testUpdatePartNumberWhenMessagePartNumberNotNullAndDbPartNumberNotNull() {
        when(orderFulfillmentPartFromMessage.getPartNumber()).thenReturn(PART_NUMBER_1);
        when(orderFulfillmentPartFromDB.getPartNumber()).thenReturn(PART_NUMBER_2);

        orderFulfillmentPartUpdaterService.update(
            orderFulfillmentPartFromDB, orderFulfillmentPartFromMessage);

        // Ensure setPartNumber is not called when both message and DB part numbers are not null
        verify(orderFulfillmentPartFromDB, never()).setPartNumber(anyInt());
    }
}
