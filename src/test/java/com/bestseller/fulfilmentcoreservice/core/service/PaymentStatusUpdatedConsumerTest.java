package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderCancelledConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderForFulfilmentConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.PaymentStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.MessageNotProcessedException;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.model.OrderDetailsModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderForFulfilmentModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderLineModel;
import com.bestseller.fulfilmentcoreservice.core.model.PaymentStatusUpdatedModel;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.PaymentStatusUpdatedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderCancelled.OrderCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentStatusUpdatedConsumerTest {

    private static final String EAN = "1234567890123";

    @InjectMocks
    private PaymentStatusUpdatedServiceImpl paymentStatusService;

    @Mock
    private OrderForFulfilmentService orderForFulfilmentService;

    @Mock
    private PaymentStatusUpdatedConverter paymentStatusUpdatedConverter;

    @Mock
    private OrderForFulfilmentConverter orderForFulfilmentConverter;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private QueueProducer<OrderCancelled> orderCancelledProducerQueueProducer;

    @Mock
    private OrderCancelledConverter orderCancelledConverter;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderService orderService;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Test
    void accept_givenPaymentStatusAuthorised_orderExists() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.AUTHORISED);
        var orderForFulfilmentModel = OrderForFulfilmentModel
            .builder().orderDetails(OrderDetailsModel.builder().build())
            .orderLines(List.of(OrderLineModel.builder()
                .ean(EAN).build())).build();

        when(orderService.findById(orderId)).thenReturn(order);
        when(orderForFulfilmentConverter.toOrderForFulfilmentModel(any(), any()))
            .thenReturn(orderForFulfilmentModel);
        when(paymentStatusUpdatedConverter.toPaymentStatusUpdatedModel(anyString(), any()))
            .thenReturn(PaymentStatusUpdatedModel.builder().orderId(orderId).build());

        // act
        paymentStatusService.process(paymentStatusUpdated);

        // assert
        verify(orderService).findById(orderId);
        verify(orderService).authorizePayment(order);
        verify(orderForFulfilmentService).process(any());
    }

    @Test
    void accept_givenPaymentStatusAuthorised_orderNotFoundExceptionThrown() {
        // arrange
        var orderId = "OL12212";
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.AUTHORISED);

        when(orderService.findById(orderId)).thenThrow(OrderNotFoundException.class);

        // act & assert
        assertThrows(OrderNotFoundException.class, () ->
            paymentStatusService.process(paymentStatusUpdated));
    }

    @Test
    void accept_givenPaymentStatusAuthorised_orderPayloadIsNull() {
        // arrange
        var orderId = "OL12212";
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.AUTHORISED);
        paymentStatusUpdated.setPayload(null);

        // act & assert
        assertThrows(NullPointerException.class, () ->
            paymentStatusService.process(paymentStatusUpdated));
    }

    @Test
    void accept_givenPaymentStatusCancelled_orderExists() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.CANCELLED);
        when(orderService.findById(orderId)).thenReturn(order);
        when(orderCancelledConverter.toCancelled(order)).thenReturn(new OrderCancelled());
        when(orderStatusUpdatedConverter.convertTo(eq(order), any(), any())).thenReturn(new OrderStatusUpdated());

        // act
        paymentStatusService.process(paymentStatusUpdated);

        // assert
        verify(orderStateService).applyOnAllOrderLines(order, OrderState.CANCELLED);
        verify(orderService, never()).save(any());
        verify(orderForFulfilmentService, never()).process(any());
    }

    @Test
    void accept_givenPaymentStatusCancelledForAuthorisedOrderNotDeliveredToWarehouse_skipProcessing() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.setOrderPaymentAuthorised(true);
        order.setMinStatus(OrderState.ROUTING);

        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.CANCELLED);
        when(orderService.findById(orderId)).thenReturn(order);

        // act
        var exception = assertThrows(MessageNotProcessedException.class, () ->
            paymentStatusService.process(paymentStatusUpdated));

        // assert
        assertThat(exception)
            .as("Exception matches")
            .hasMessage("The payment status has already been updated to AUTHORISED,"
                + " so payment cancellation is no longer possible for order: OL12212");
        verify(orderService).findById(orderId);
        verify(orderService, never()).authorizePayment(order);
        verify(orderStateService, never()).applyOrderState(order, OrderState.CANCELLED);
        verify(orderService, never()).save(any());
        verify(orderForFulfilmentService, never()).process(any());
    }

    @Test
    void accept_givenPaymentStatusCancelledForAuthorisedOrderDeliveredToWarehouse_skipProcessing() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.setOrderPaymentAuthorised(true);
        order.setMinStatus(OrderState.EXPORTED);

        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.CANCELLED);
        when(orderService.findById(orderId)).thenReturn(order);

        // act
        paymentStatusService.process(paymentStatusUpdated);

        // assert

        verify(orderService).findById(orderId);
        verify(orderService, never()).authorizePayment(order);
        verify(orderStateService, never()).applyOrderState(order, OrderState.CANCELLED);
        verify(orderService, never()).save(any());
        verify(orderForFulfilmentService, never()).process(any());
    }

    @Test
    void accept_givenPaymentStatusAuthorised_orderHasBeenAuthorized() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.setOrderPaymentAuthorised(true);

        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.AUTHORISED);
        when(orderService.findById(orderId)).thenReturn(order);
        // act
        paymentStatusService.process(paymentStatusUpdated);

        // assert
        verify(orderService).findById(orderId);
        verify(orderService).authorizePayment(order);
        verify(orderForFulfilmentService).process(any());
    }

    @Test
    void accept_givenPaymentStatusCancelled_orderNotFoundExceptionThrown() {
        // arrange
        var orderId = "OL12212";
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.CANCELLED);

        when(orderService.findById(orderId)).thenThrow(OrderNotFoundException.class);
        // act & assert
        assertThrows(OrderNotFoundException.class, () ->
            paymentStatusService.process(paymentStatusUpdated));
    }

    @Test
    void accept_givenPaymentStatusAuthorised_virtualOrder() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        order.setShippingMethod(ShippingMethod.VIRTUAL);
        var paymentStatusUpdated = PaymentStatusUpdatedGenerator
            .toPaymentStatusUpdated(orderId,
                PaymentStatusUpdated.PaymentState.AUTHORISED);

        when(orderService.findById(orderId)).thenReturn(order);
        when(paymentStatusUpdatedConverter.toPaymentStatusUpdatedModel(anyString(), any()))
            .thenReturn(PaymentStatusUpdatedModel.builder().orderId(orderId).build());

        // act
        paymentStatusService.process(paymentStatusUpdated);

        // assert
        verify(orderService).findById(orderId);
        verify(orderService).authorizePayment(order);
        verify(orderForFulfilmentService, never()).process(any());
    }
}
