package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverter;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderPartsCancelledStrategy;
import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsCancelledGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPartsCancelledServiceTest {

    @InjectMocks
    private OrderPartsCancelledServiceImpl orderPartsCancelledService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderStateService orderStateService;

    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;

    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;

    @Mock
    private TradeByteOrderStatusBaseConverter tradeByteOrderStatusBaseConverter;

    @Mock
    private OrderStatusUpdatedTradeByteService orderStatusUpdatedTradeByteService;

    @Mock
    private OrderPartsCancelledStrategy orderLineReturnedStrategy;

    private OrderStateChangeContext context;

    @Spy
    private StateChangeService stateChangeService;

    @BeforeEach
    public void setUp() {
        when(orderLineReturnedStrategy.getMessageType()).thenReturn(OrderPartsCancelled.class);
        context = new OrderStateChangeContext(List.of(orderLineReturnedStrategy));
        stateChangeService = Mockito.spy(new StateChangeServiceImpl(context));
    }

    @Test
    void updateOrderStatusAndGenerateOrderFulfillmentPart() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderPartsCancelled = OrderPartsCancelledGenerator.generateOrderPartsCancelled(orderId);
        order.setOrderLines(List.of(
            OrderLine.builder().ecomId(UUID.randomUUID()).orderLineQtyStatus(
                List.of(OrderLineQtyStatus.builder()
                    .orderState(OrderState.PLACED)
                    .build())).build()));

        var routedPayload = DefaultOrderStatusUpdatedPayload
            .builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(UUID.randomUUID())
                        .states(List.of(OrderStatusUpdated.Type.CANCELLED.name()))
                        .build()
                )
            )
            .build();

        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(orderId)
            .withPayload(routedPayload)
            .withType(OrderStatusUpdated.Type.CANCELLED);

        when(orderService.findById(orderId)).thenReturn(order);
        when(orderStatusUpdatedConverter.convertTo(any(), any(), any())).thenReturn(orderStatusUpdated);
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(routedPayload);

        // act
        orderPartsCancelledService.process(orderPartsCancelled);

        // assert
        verify(orderStateService).apply(any(), any());
        verify(orderStatusUpdatedConverter).convertTo(any(), any(), any());
        verify(orderStatusUpdatedTradeByteService).produceOrderStatusUpdatedTradeByte(any(), any());
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
    }
}
