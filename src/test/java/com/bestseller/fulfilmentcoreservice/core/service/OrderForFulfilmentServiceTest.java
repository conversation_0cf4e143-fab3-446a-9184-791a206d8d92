package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderForFulfilmentConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderStatusUpdatedConverter;
import com.bestseller.fulfilmentcoreservice.converter.messaging.OrderToOrderStatusUpdatedPayloadConverter;
import com.bestseller.fulfilmentcoreservice.core.exception.OrderNotFoundException;
import com.bestseller.fulfilmentcoreservice.core.exception.StateTransitionException;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderForFulfilmentGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.repository.OrderRepository;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderForFulfillment.OrderForFulfillment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderForFulfilmentServiceTest {

    @InjectMocks
    private OrderForFulfilmentServiceImpl orderForFulfilmentService;
    @Mock
    private OrderRepository orderRepository;
    @Mock
    private OrderStateService orderStateService;
    @Mock
    private QueueProducer<OrderForFulfillment> orderForFulfilmentProducerQueueProducer;
    @Mock
    private QueueProducer<OrderStatusUpdated> orderStatusUpdatedProducerQueueProducer;
    @Mock
    private OrderToOrderStatusUpdatedPayloadConverter orderToOrderStatusUpdatedPayloadConverter;
    @Mock
    private OrderStatusUpdatedConverter orderStatusUpdatedConverter;

    @Mock
    private OrderForFulfilmentConverter orderForFulfilmentConverter;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;OrderForFulfillment&gt; and QueueProducer&lt;OrderStatusUpdated&gt; are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(
            orderForFulfilmentService,
            "orderForFulfilmentProducerQueueProducer",
            orderForFulfilmentProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            orderForFulfilmentService,
            "orderStatusUpdatedProducerQueueProducer",
            orderStatusUpdatedProducerQueueProducer
        );
    }

    @Test
    void updateOrderStatus_WhenOrderExists_Success() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderForFulfilment = OrderForFulfilmentGenerator.generateOrderForFulfilmentModel(orderId);
        var routingPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(UUID.randomUUID())
                        .states(List.of(OrderStatusUpdated.Type.ROUTING.name()))
                        .build()
                )
            )
            .build();

        var orderStatusUpdated = new OrderStatusUpdated()
            .withOrderId(orderId)
            .withPayload(routingPayload)
            .withType(OrderStatusUpdated.Type.ROUTING);

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(orderStatusUpdatedConverter.convertTo(any(), any(), any())).thenReturn(orderStatusUpdated);
        when(orderToOrderStatusUpdatedPayloadConverter.convert(order)).thenReturn(routingPayload);
        when(orderForFulfilmentConverter.toOrderForFulfilment(orderForFulfilment))
            .thenReturn(new OrderForFulfillment());

        // act
        orderForFulfilmentService.process(orderForFulfilment);

        // assert
        verify(orderStateService).applyOrderState(any(), any());
        verify(orderForFulfilmentProducerQueueProducer).enqueue(EnqueueParams.create(
            orderForFulfilmentConverter.toOrderForFulfilment(orderForFulfilment)
        ));
        verify(orderStatusUpdatedProducerQueueProducer).enqueue(EnqueueParams.create(
            orderStatusUpdatedConverter.convertTo(any(), any(), any())
        ));
        verify(orderToOrderStatusUpdatedPayloadConverter).convert(order);
    }

    @Test
    void updateOrderStatus_WhenOrderNotFound_ExceptionThrown() {
        // arrange
        var orderId = "OL12212";
        var orderForFulfilment = OrderForFulfilmentGenerator.generateOrderForFulfilmentModel(orderId);
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());
        // act & assert
        assertThrows(OrderNotFoundException.class, () ->
            orderForFulfilmentService.process(orderForFulfilment));
    }

    @Test
    void updateOrderStatus_WhenStateTransition_ExceptionThrownAndCatch() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        var orderForFulfilment = OrderForFulfilmentGenerator.generateOrderForFulfilmentModel(orderId);
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        doThrow(StateTransitionException.class).when(orderStateService).applyOrderState(any(), any());
        // act & assert
        assertThrows(StateTransitionException.class, () ->
            orderForFulfilmentService.process(orderForFulfilment));
    }

}
