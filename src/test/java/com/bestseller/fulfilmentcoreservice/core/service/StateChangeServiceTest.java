package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.service.statechange.OrderStateChangeContext;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class StateChangeServiceTest {

    @InjectMocks
    private StateChangeServiceImpl stateChangeService;

    @Mock
    private OrderStateChangeContext context;

    @Test
    void createCopiesOfStateChange_givenValidInputs_correctQuantityIsReturned() {
        // arrange
        final String ean = "122312";
        final int quantity = 3;

        // act & assert
        assertThat(stateChangeService.createCopiesOfStateChange(ean, quantity, OrderState.PROCESSING))
            .hasSize(quantity)
            .allMatch(item -> item.getEan().equals(ean))
            .allMatch(item -> item.getOrderState() == OrderState.PROCESSING);
    }
}
