package com.bestseller.fulfilmentcoreservice.core.service.validator;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class PickupPointValidatorTest {

    @InjectMocks
    private PickupPointValidator pickupPointValidator;

    @ParameterizedTest
    @MethodSource("data")
    void isPickupPointIdInvalid(String addressLine3, String countryCode, String carrier, boolean expected) {
        // arrange & act
        boolean actual = pickupPointValidator.isPickupPointIdInvalid(addressLine3, countryCode, carrier);

        // assert
        assertThat(actual).isEqualTo(expected);
    }

    private static Stream<Arguments> data() {
        return Stream.of(
            Arguments.of(null, null, null, false),
            Arguments.of("", "", "", false),
            Arguments.of("1363LT", "country", "carrier", true),
            Arguments.of("1363", "country", "carrier", false),

            Arguments.of("1363LT", "NL", null, false),
            Arguments.of("1363LT", "NL", "MONDIALRELAY", false),

            Arguments.of("1363LT", "DE", "DHL", false),
            Arguments.of("1363LT", "DE", "", true),
            Arguments.of("1363LT", "DE", "", true),

            Arguments.of("1363LT", "ES", "CORREOS", false),
            Arguments.of("1363LT", "ES", "", true),
            Arguments.of("1363LT", "ES", "", true),

            Arguments.of("1363LT", "PL", "INPOST", false),
            Arguments.of("1363LT", "", "INPOST", true),
            Arguments.of("1363LT", "PL", "", true),

            Arguments.of("1363LT", "FR", "MONDIALRELAY", false),
            Arguments.of("1363LT", "", "MONDIALRELAY", true),
            Arguments.of("1363LT", "FR", "", true),

            Arguments.of("1363LT", "ES", "DHLCON", false),
            Arguments.of("1363LT", "EE", "DHLCON", false),
            Arguments.of("1363LT", "LT", "DHLCON", false),
            Arguments.of("1363LT", "HU", "DHLCON", false),
            Arguments.of("1363LT", "CZ", "DHLCON", false),
            Arguments.of("1363LT", "FR", "DHLCON", true)
        );
    }

}
