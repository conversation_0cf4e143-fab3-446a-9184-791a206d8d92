package com.bestseller.fulfilmentcoreservice.core.service.statechange;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderLineDispatchedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.dto.OrderStatusUpdateStateChange;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderStateChangeContextTest {

    @Mock
    private OrderLineDispatchedStrategy orderLineDispatchedStrategy;

    private OrderStateChangeContext context;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(orderLineDispatchedStrategy.getMessageType()).thenReturn(OrderLineDispatched.class);
        context = new OrderStateChangeContext(List.of(orderLineDispatchedStrategy));
    }

    @Test
    public void testExecuteStrategy() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        OrderLineDispatched orderLineDispatched = OrderLineDispatchedGenerator.generate(orderId);
        List<OrderStatusUpdateStateChange> expectedStateChanges =
            List.of(new OrderStatusUpdateStateChange());

        // act
        when(orderLineDispatchedStrategy.defineOrderStateChange(orderLineDispatched, order))
            .thenReturn(expectedStateChanges);

        List<OrderStatusUpdateStateChange> result = context.executeStrategy(orderLineDispatched, order);

        // assert
        assertEquals(expectedStateChanges, result);
    }

    @Test
    public void testExecuteStrategy_whenContext_doesNotExists() {
        // arrange
        var orderId = "OL12212";
        var order = OrderGenerator.createOrder(orderId);
        OrderLineExported orderLineExported = new OrderLineExported();
        orderLineExported.setOrderId(orderId);

        // act & assert
        assertThrows(IllegalArgumentException.class, () ->
            context.executeStrategy(orderLineExported, order));
    }
}
