package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;
import com.bestseller.fulfilmentcoreservice.persistence.repository.PartnerChannelRepository;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartnerChannelServiceTest {

    @InjectMocks
    private PartnerChannelServiceImpl partnerChannelService;

    @Mock
    private PartnerChannelRepository partnerChannelRepository;

    @Test
    void getPartnerChannel_givenDemandwareOrder_willReturnNull() {
        // arrange
        String channel = "foo";
        var order = Order.builder()
            .platform(Platform.DEMANDWARE)
            .build();

        // act & assert
        assertThat(partnerChannelService.getPartnerChannel(order, channel))
            .as("PartnerChannel should be null for Demandware orders")
            .isNull();
    }

    @Test
    void getPartnerChannel_givenTradebyteOrderWithValidChannel_willNotReturnNull() {
        // arrange
        String channel = "buzz";
        var order = Order.builder()
            .platform(Platform.TRADEBYTE)
            .build();
        when(partnerChannelRepository.findById(channel))
            .thenReturn(Optional.of(new PartnerChannel()));

        // act & assert
        assertThat(partnerChannelService.getPartnerChannel(order, channel))
            .as("PartnerChannel should not be null for Tradebyte orders")
            .isNotNull();
    }

    @Test
    void getPartnerChannel_givenTradebyteOrderWithoutValidChannel_willThrowException() {
        // arrange
        String channel = "bar";
        var order = Order.builder()
            .platform(Platform.TRADEBYTE)
            .build();
        when(partnerChannelRepository.findById(channel))
            .thenReturn(Optional.empty());

        // act & assert
        var exception = assertThrows(
            RuntimeException.class,
            () -> partnerChannelService.getPartnerChannel(order, channel));
        assertThat(exception)
            .hasMessage("PartnerChannel not found for id bar");
    }
}
