package com.bestseller.fulfilmentcoreservice.core.service;

import com.bestseller.fulfilmentcoreservice.core.exception.ValidationException;
import com.bestseller.fulfilmentcoreservice.core.service.validator.PickupPointValidator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.FulfillmentAdvice;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingCharge;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator.createBasicOrderPlacedMessage;
import static com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator.createFullOrderLine;
import static com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator.createPhoenixOrderPlacedMessage;
import static com.bestseller.fulfilmentcoreservice.core.utils.OrderPlacedGenerator.createTradeByteOrderPlacedMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderPlacedValidationServiceImplTest {

    private static final String VALID_HO_NO = "A";
    private static final String COUNTRY = "NLBB";
    private OrderPlaced testMessage;

    @InjectMocks
    private OrderPlacedValidationServiceImpl orderPlacedValidationServiceImpl;
    @Mock
    private PickupPointValidator pickupPointValidator;

    @Mock
    private ValidationService validationService;

    @BeforeEach
    public void setUp() {
        testMessage = createBasicOrderPlacedMessage();
    }

    @Test
    void validate_validFullMessage_tradeByte_noExceptionsThrown() {
        // arrange
        OrderPlaced validTbMessage = createTradeByteOrderPlacedMessage();

        // act and assert - no exception thrown
        orderPlacedValidationServiceImpl.validate(validTbMessage);
    }

    @Test
    void validate_FullMessage_unKnown_ExceptionsThrown() {
        // arrange
        OrderPlaced message = createTradeByteOrderPlacedMessage();
        message.setStore("nothing");

        // act and assert - no exception thrown
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
        assertEquals("Unable to determine platform from order id orderId.", exception.getMessage());
    }

    @Test
    void validate_validFullMessage_demandware_noExceptionsThrown() {
        // arrange
        OrderPlaced validDmwMessage = createPhoenixOrderPlacedMessage();

        // act and assert - no exceptions thrown
        orderPlacedValidationServiceImpl.validate(validDmwMessage);
    }

    @Test
    void validate_emptyMessage_exceptionThrown() {
        // arrange
        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(null));
        assertEquals("\"OrderPlaced\" must not be empty or null!", exception.getMessage());
    }

    @Test
    void validate_missingStore_exceptionThrown() {
        // arrange
        testMessage.setStore(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.store\" must not be empty or null!", exception.getMessage());
    }

    @Test
    void validate_when_missingEanIsNull_exceptionThrown() {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingEan = createFullOrderLine();
        orderLineWithMissingEan.setEan(null);
        orderLines.add(orderLineWithMissingEan);
        testMessage.setOrderLines(orderLines);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.orderLines.ean\" must not be empty or null!", exception.getMessage());
    }

    @Test
    void validate_when_missingEanIsEmpty_exceptionThrown() {
        // arrange
        List<OrderLine> orderLines = new ArrayList<>();
        OrderLine orderLineWithMissingEan = createFullOrderLine();
        orderLineWithMissingEan.setEan(" ");
        orderLines.add(orderLineWithMissingEan);
        testMessage.setOrderLines(orderLines);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.orderLines.ean\" must not be empty or null!", exception.getMessage());
    }

    @Test
    void validate_missingShippingInformation_exceptionThrown() {
        // arrange
        testMessage.setShippingInformation(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation\" must not be empty or null!", exception.getMessage());
    }

    @Test
    void validate_missingShippingAddress_exceptionThrown() {
        // arrange
        testMessage.setStore(Platform.DEMANDWARE.name());
        testMessage.getOrderDetails().setShippingMethod(ShippingMethod.STANDARD.name());
        testMessage.getShippingInformation().setShippingAddress(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingBillingAddressFirstName_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setFirstName(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.firstName\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingBillingAddressLastName_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setLastName(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.lastName\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingBillingAddressCity_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setCity(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.city\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingBillingAddressAddressLine1_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setAddressLine1(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.addressLine1\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_billingAddressMissingHouseNumberExtended_noExceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setHouseNumberExtended(null);

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_billingAddressValidHouseNumberExtended_noExceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setHouseNumberExtended(VALID_HO_NO);

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_missingBillingAddressCountry_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setCountry(null);

        // act and assert - no exception
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.country\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_billingAddressCountryLengthExceedsMaxLength_exceptionThrown() {
        // arrange
        testMessage.getCustomerInformation().getBillingAddress().setCountry(COUNTRY);

        // act and assert - no exception
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.country\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_billingAddress_missingZipCode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setZipcode("");

        // act and assert - exception thrown
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.zipcode\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_billingAddress_invalidNetherlandsZipCode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setCountry("NL");
        message.getCustomerInformation().getBillingAddress().setZipcode("1234");

        // act and assert - exception thrown
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
        assertEquals("\"OrderPlaced.customerInformation.billingAddress.zipcode\" must be valid!",
            exception.getMessage());
    }

    @Test
    void validate_billingAddress_NetherlandsWithValidZipcode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setCountry("NL");
        message.getCustomerInformation().getBillingAddress().setZipcode("1363LT");

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_missingFulfillmentNode_exceptionThrown() {
        // arrange
        testMessage.setFulfillmentAdvice(new FulfillmentAdvice().withFulfillmentNode(null));

        // act and assert - exception thrown
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.getFulfillmentAdvice.fulfillmentNode\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_fulfillmentAdviceValidFulfillmentNode_noExceptionThrown() {
        // arrange
        testMessage.setFulfillmentAdvice(new FulfillmentAdvice()
            .withFulfillmentNode(OrderPlacedGenerator.FulfillmentAdviceProperties.FULFILLMENT_NODE));

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_missingFulfillmentAdvice_noExceptionThrown() {
        // arrange
        testMessage.setFulfillmentAdvice(null);

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_parcelShopWithAddressLine3_noExceptionThrown() {
        // arrange
        testMessage.getOrderDetails().setCarrierVariant(CarrierVariant.LOCKER.name());
        testMessage.getShippingInformation().getShippingAddress().setAddressLine3("parcelshop address");

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_parcelShopWithoutAddressLine3_exceptionThrown() {
        // arrange
        testMessage.getOrderDetails().setCarrierVariant(CarrierVariant.LOCKER.name());
        testMessage.getShippingInformation().getShippingAddress().setAddressLine3(null);

        // act and assert - exception expected
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
    }

    @Test
    void validate_missingShippingInformationShippingAddress_tb_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.getShippingInformation().setShippingAddress(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_shippingInformationAndShippingAddressAreNull_tb_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.setShippingInformation(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_missingShippingAddressFirstName_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setFirstName(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.firstName\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingShippingAddressLastName_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setLastName(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.lastName\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingShippingAddressCity_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setCity(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.city\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_missingShippingAddressAddressLine1_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setAddressLine1(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.addressLine1\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_shippingAddressMissingHouseNumberExtended_noExceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setHouseNumberExtended(null);

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_shippingAddressValidHouseNumberExtended_noExceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setHouseNumberExtended(VALID_HO_NO);

        // act and assert - no exception
        orderPlacedValidationServiceImpl.validate(testMessage);
    }

    @Test
    void validate_missingShippingAddressCountry_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setCountry(null);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.country\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_shippingAddressCountryLengthExceedsMaxLength_exceptionThrown() {
        // arrange
        testMessage.getShippingInformation().getShippingAddress().setCountry(COUNTRY);

        // act and assert - exception expected
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(testMessage));
        assertEquals("\"OrderPlaced.shippingInformation.shippingAddress.country\" must not be empty or null!",
            exception.getMessage());
    }

    @Test
    void validate_shippingAddress_missingZipCode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress().setZipcode("");

        // act and assert - exception expected
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_invalidNetherlandsZipCode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress().setCountry("NL");
        message.getShippingInformation().getShippingAddress().setZipcode("1234");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_FRCountryCode_noExceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress().setCountry("FR");
        message.getShippingInformation().getShippingAddress().setZipcode("1234AC");

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_shippingAddress_validNetherlandsZipCode_exceptionThrown() {
        // arrange
        OrderPlaced message = createBasicOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress().setCountry("NL");
        message.getShippingInformation().getShippingAddress().setZipcode("1364LT");

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_tradeByteOrder_withVirtualShipping_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.getOrderDetails().setShippingMethod("VIRTUAL");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_demandwareOrder_withNullShipping_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.setStore(Platform.DEMANDWARE.name());
        invalidMessage.getOrderDetails().setShippingMethod(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_demandwareOrder_withVirtualShippingMethod_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.setStore(Platform.DEMANDWARE.name());
        invalidMessage.getOrderDetails().setShippingMethod("VIRTUAL");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_demandwareOrder_parcelShop_whenIsPickupPointIdInvalid_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.setStore(Platform.DEMANDWARE.name());
        invalidMessage.getCustomerInformation().getBillingAddress().setAddressLine1("Ams1");
        invalidMessage.getCustomerInformation().getBillingAddress().setAddressLine2("Ams2");
        invalidMessage.getCustomerInformation().getBillingAddress().setAddressLine3("Ams3");

        invalidMessage.getShippingInformation().getShippingAddress().setAddressLine1("Ams1");
        invalidMessage.getShippingInformation().getShippingAddress().setAddressLine2("Ams2");
        invalidMessage.getShippingInformation().getShippingAddress().setAddressLine3("Ams3");

        when(pickupPointValidator.isPickupPointIdInvalid(anyString(), anyString(), anyString())).thenReturn(true);
        // act and assert - exception expected
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_tradebyteOrder_withVirtualProducts_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        OrderLine virtualLine = OrderPlacedGenerator.createVirtualProductOrderLine();
        invalidMessage.getOrderLines().add(virtualLine);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_missingOrderDetailsExternalOrderNumber_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createTradeByteOrderPlacedMessage();
        invalidMessage.getOrderDetails().setExternalOrderNumber(null);

        // act and assert - exception thrown
        Exception exception = assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
        assertEquals("\"OrderPlaced.orderDetails.externalOrderNumber\" must not be empty or null!",
            exception.getMessage());
    }

    // Test demandware specific rules
    @Test
    void validate_shippingInformation_missingShippingAddress_physicalProducts_dmw_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createPhoenixOrderPlacedMessage();

        invalidMessage.setOrderLines(new ArrayList<>());
        invalidMessage.getOrderLines().add(OrderPlacedGenerator.createPhysicalProductOrderLine());
        invalidMessage.getOrderLines().add(OrderPlacedGenerator.createVirtualProductOrderLine());

        invalidMessage.getShippingInformation().setShippingAddress(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(invalidMessage));
    }

    @Test
    void validate_shippingInformation_physicalProducts_dmw_exceptionThrown() {
        // arrange
        OrderPlaced invalidMessage = createPhoenixOrderPlacedMessage();

        invalidMessage.setOrderLines(new ArrayList<>());
        invalidMessage.getOrderLines().add(OrderPlacedGenerator.createPhysicalProductOrderLine());
        invalidMessage.getOrderLines().add(OrderPlacedGenerator.createVirtualProductOrderLine());
        invalidMessage.getOrderLines().forEach(orderLine -> orderLine.setVirtualProduct(Boolean.TRUE));
        invalidMessage.getOrderDetails().setShippingMethod(ShippingMethod.VIRTUAL.name());

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(invalidMessage);
    }

    @Test
    void validate_shippingInformation_missingShippingAddress_onlyVirtualProducts_dmw_noExceptionThrown() {
        // arrange
        OrderPlaced validMessage = createPhoenixOrderPlacedMessage();
        validMessage.setOrderLines(new ArrayList<>());
        validMessage.getOrderLines().add(OrderPlacedGenerator.createVirtualProductOrderLine());

        // act and assert - no exception thrown
        orderPlacedValidationServiceImpl.validate(validMessage);
    }

    @Test
    void validate_missingShippingCharges_dmw_virtualOrder_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.setOrderLines(new ArrayList<>());
        message.getOrderLines().add(OrderPlacedGenerator.createVirtualProductOrderLine());
        message.getShippingInformation().setShippingCharges(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_missingShippingCharges_dmw_physicalOrder_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.setOrderLines(new ArrayList<>());
        message.getOrderLines().add(OrderPlacedGenerator.createPhysicalProductOrderLine());
        message.getShippingInformation().setShippingCharges(null);

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void check_areShippingChargesValid() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.setOrderLines(new ArrayList<>());
        message.getOrderLines().add(OrderPlacedGenerator.createVirtualProductOrderLine());
        message.getShippingInformation().setShippingCharges(List.of(new ShippingCharge()));

        // act and assert - exception thrown
        assertThrows(ValidationException.class, () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_billingAddress_address1ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress()
            .setAddressLine1("A very long address that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_billingAddress_forAnotherCountry_noExceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setAddressLine1("An address");
        message.getCustomerInformation().getBillingAddress().setCountry("FR");

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_billingAddress_forNL_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setAddressLine1("An address");
        message.getCustomerInformation().getBillingAddress().setZipcode("1023LT");
        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_missingBillingAddress_address2_noExceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setAddressLine2(null);

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_billingAddress_address2ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress()
            .setAddressLine2("A very long address that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_billingAddress_address3ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress()
            .setAddressLine3("A very long address that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_billingAddress_cityExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getCustomerInformation().getBillingAddress().setCity("A very long city that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_address1ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress()
            .setAddressLine1("A very long city that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_address2ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress()
            .setAddressLine2("A very long city that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_address3ExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress()
            .setAddressLine3("A very long city that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_shippingAddress_cityExceedsLength_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getShippingInformation().getShippingAddress().setCity("A very long city that exceeds the limit");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

    @Test
    void validate_nullPayments_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.setPayments(null);

        // act and assert - exception thrown
        orderPlacedValidationServiceImpl.validate(message);
    }

    @Test
    void validate_invalidShippingMethod_exceptionThrown() {
        // arrange
        OrderPlaced message = createPhoenixOrderPlacedMessage();
        message.getOrderDetails().setShippingMethod("INVALID_SHIPPING_METHOD");

        // act and assert - exception thrown
        assertThrows(ValidationException.class,
            () -> orderPlacedValidationServiceImpl.validate(message));
    }

}
