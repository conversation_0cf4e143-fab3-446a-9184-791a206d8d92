package com.bestseller.fulfilmentcoreservice.core.monitoring;

import com.logistics.statetransition.OrderState;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.assertj.core.api.Assertions.assertThat;

public class OrderStatusChangingMonitorTest {

    private static final String METRIC_NAME = "order.status.changing.total";
    private static final String TAG_NAME = "status";

    private OrderStatusChangingMonitor orderStatusChangingMonitor;
    private MeterRegistry meterRegistry;

    @BeforeEach
    void beforeEach() {
        meterRegistry = new SimpleMeterRegistry();
        orderStatusChangingMonitor = new OrderStatusChangingMonitor(meterRegistry);
    }

    @ParameterizedTest
    @EnumSource(OrderState.class)
    void test(OrderState orderState) {
        // act
        orderStatusChangingMonitor.increment(orderState);

        // assert
        assertThat(
            meterRegistry
                .get(METRIC_NAME)
                .tag(TAG_NAME, orderState.name().toLowerCase())
                .counter()
                .count()
        ).isEqualTo(1);
    }

}
