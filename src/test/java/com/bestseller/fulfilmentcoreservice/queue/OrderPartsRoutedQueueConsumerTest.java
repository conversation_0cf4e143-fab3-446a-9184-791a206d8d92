package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderPartsRoutedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsRouted.OrderPartsRouted;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderPartsRoutedQueueConsumerTest {

    @InjectMocks
    private OrderPartsRoutedQueueConsumer consumer;

    @Mock
    private OrderPartsRoutedService orderPartsRoutedService;

    @Mock
    private TaskPayloadTransformer<OrderPartsRouted> taskPayloadTransformer;

    @Test
    void consume_givenMessage_messageIsProcessed() {
        // arrange
        var orderPartsRouted = new OrderPartsRouted()
            .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderPartsRouted);

        // assert
        verify(orderPartsRoutedService).process(orderPartsRouted);
    }

    @Test
    void getPayloadTransformer_givenMessage_returnsPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void getMessageDetails_givenMessage_returnsMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderPartsRouted = new OrderPartsRouted()
            .withOrderId(orderId);

        // Act
        String messageDetails = consumer.getMessageDetails(orderPartsRouted);

        // Assert
        assertEquals("orderId=12345, orderLines=[], fulfillmentNode=null", messageDetails);
    }
}
