package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineExportedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderLineExportedQueueConsumerTest {

    @InjectMocks
    private OrderLineExportedQueueConsumer consumer;

    @Mock
    private OrderLineExportedService orderLineExportedService;

    @Mock
    private TaskPayloadTransformer<OrderLineExported> taskPayloadTransformer;

    @Test
    void shouldConsumeTask_whenOrderLineExported() {
        // arrange
        var orderLineExported = new OrderLineExported()
                .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderLineExported);

        // assert
        verify(orderLineExportedService).process(orderLineExported);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderLineExported = new OrderLineExported()
            .withOrderId(orderId);
        // Act
        String messageDetails = consumer.getMessageDetails(orderLineExported);

        // Assert
        assertEquals("orderId=12345, ean=", messageDetails);
    }
}
