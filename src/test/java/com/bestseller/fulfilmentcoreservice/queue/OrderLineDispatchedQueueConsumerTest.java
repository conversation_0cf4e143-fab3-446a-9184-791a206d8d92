package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineDispatchedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderLineDispatchedQueueConsumerTest {

    @InjectMocks
    private OrderLineDispatchedQueueConsumer consumer;

    @Mock
    private OrderLineDispatchedService orderLineDispatchedService;

    @Mock
    private TaskPayloadTransformer<OrderLineDispatched> taskPayloadTransformer;

    @Test
    void shouldConsume_whenOrderLineDispatched() {
        // arrange
        var orderLineDispatched = new OrderLineDispatched()
            .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderLineDispatched);

        // assert
        verify(orderLineDispatchedService).process(orderLineDispatched);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderLineDispatched = new OrderLineDispatched()
            .withOrderId(orderId);
        // Act
        String messageDetails = consumer.getMessageDetails(orderLineDispatched);

        // Assert
        assertEquals("orderId=12345, ean=", messageDetails);
    }
}
