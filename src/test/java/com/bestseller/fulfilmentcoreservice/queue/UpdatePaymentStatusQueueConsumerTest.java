package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.PaymentStatusUpdatedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class UpdatePaymentStatusQueueConsumerTest {

    @InjectMocks
    private UpdatePaymentStatusQueueConsumer consumer;

    @Mock
    private PaymentStatusUpdatedService paymentStatusUpdatedService;

    @Mock
    private TaskPayloadTransformer<PaymentStatusUpdated> taskPayloadTransformer;

    @Test
    void shouldExecuteTask_whenOrderCancelled() {
        // arrange
        String orderId = UUID.randomUUID().toString();
        var paymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(orderId)
            .withPaymentState(PaymentStatusUpdated.PaymentState.CANCELLED)
            .withPayload(new AuthorizedPayload());

        // act
        consumer.consume(paymentStatusUpdated);

        // assert
        verify(paymentStatusUpdatedService).process(paymentStatusUpdated);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var paymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(orderId)
            .withPaymentState(PaymentStatusUpdated.PaymentState.CANCELLED)
            .withPayload(new AuthorizedPayload());

        // Act
        String messageDetails = consumer.getMessageDetails(paymentStatusUpdated);

        // Assert
        assertEquals("orderId=12345, paymentState=CANCELLED", messageDetails);
    }
}
