package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedQueueConsumerTest {

    @InjectMocks
    private ValidOrderPlacedQueueConsumer consumer;

    @Mock
    private MessageConsumerService<ValidOrderPlaced> messageConsumerService;

    @Mock
    private TaskPayloadTransformer<ValidOrderPlaced> taskPayloadTransformer;

    @Test
    void consume_givenValidOrderPlaced_messageIsProcessed() {
        // arrange
        var validOrderPlaced = new ValidOrderPlaced()
            .withOrderId(UUID.randomUUID().toString())
            .withStore("DEMANDWARE")
            .withOrderDetails(new OrderDetails().withBrandedShipping("SL"));

        // act
        consumer.consume(validOrderPlaced);

        // assert
        verify(messageConsumerService).process(validOrderPlaced);
    }

    @Test
    void consume_givenValidOrderPlacedWithBlankBrand_messageIsProcessed() {
        // arrange
        var validOrderPlaced = new ValidOrderPlaced()
            .withOrderId(UUID.randomUUID().toString())
            .withOrderDetails(new OrderDetails()
                .withBrandedShipping(""))
            .withStore("DEMANDWARE");

        // act
        consumer.consume(validOrderPlaced);

        // assert
        verify(messageConsumerService).process(validOrderPlaced);
    }

    @Test
    void getPayloadTransformer_returnPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void getMessageDetails_givenValidOrderPlaced_orderIdIsReturned() {
        // arrange
        var validOrderPlaced = new ValidOrderPlaced()
            .withOrderId("12345");

        // act
        var messageDetails = consumer.getMessageDetails(validOrderPlaced);

        // assert
        assertThat(messageDetails).isEqualTo("orderId=%s".formatted(validOrderPlaced.getOrderId()));
    }
}
