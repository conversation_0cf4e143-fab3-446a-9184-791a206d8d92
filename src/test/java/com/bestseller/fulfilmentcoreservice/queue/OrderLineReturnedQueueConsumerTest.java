package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineReturnedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderLineReturnedQueueConsumerTest {

    @InjectMocks
    private OrderLineReturnedQueueConsumer consumer;

    @Mock
    private OrderLineReturnedService orderLineReturnedService;

    @Mock
    private TaskPayloadTransformer<OrderLineReturned> taskPayloadTransformer;

    @Test
    void shouldExecuteTask_whenOrderLineReturned() {
        // arrange
        var orderLineReturned = new OrderLineReturned()
                .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderLineReturned);

        // assert
        verify(orderLineReturnedService).process(orderLineReturned);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderLineReturned = new OrderLineReturned()
            .withOrderId(orderId);
        // Act
        String messageDetails = consumer.getMessageDetails(orderLineReturned);

        // Assert
        assertEquals("orderId=12345, ean=null", messageDetails);
    }
}
