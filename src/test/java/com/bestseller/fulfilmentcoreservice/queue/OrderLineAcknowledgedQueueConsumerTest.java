package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderLineAcknowledgedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderLineAcknowledgedQueueConsumerTest {

    @InjectMocks
    private OrderLineAcknowledgedQueueConsumer consumer;

    @Mock
    private OrderLineAcknowledgedService orderLineAcknowledgedService;

    @Mock
    private TaskPayloadTransformer<OrderLineAcknowledged> taskPayloadTransformer;

    @Test
    void shouldConsume_whenOrderLineAcknowledged() {
        // arrange
        var orderLineAcknowledged = new OrderLineAcknowledged()
                .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderLineAcknowledged);

        // assert
        verify(orderLineAcknowledgedService).process(orderLineAcknowledged);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderLineAcknowledged = new OrderLineAcknowledged()
            .withOrderId(orderId);
        // Act
        String messageDetails = consumer.getMessageDetails(orderLineAcknowledged);

        // Assert
        assertEquals("orderId=12345, ean=", messageDetails);
    }
}
