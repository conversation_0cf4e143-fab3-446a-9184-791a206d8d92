package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.messaging.service.MessageConsumerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class AbstractMessageProcessorQueueConsumerTest {
    private AbstractMessageProcessorQueueConsumer abstractMessageProcessorQueueConsumer;

    @Mock
    private MessageConsumerService<String> messageConsumerService;

    @Mock
    private TaskPayloadTransformer<String> taskPayloadTransformer;

    @Mock
    private IdempotencyChecker<String> idempotencyChecker;

    @Mock
    private QueueConfig queueConfig;

    @BeforeEach
    void setUp() {
        abstractMessageProcessorQueueConsumer = Mockito.spy(
            new AbstractMessageProcessorQueueConsumer(
                idempotencyChecker,
                queueConfig,
                taskPayloadTransformer,
                messageConsumerService) {

                @Override
                protected String getMessageDetails(Object o) {
                    return "validOrderPlaced";
                }
            });
    }

    @Test
    void consume_givenValidOrderPlaced_messageIsProcessed() {
        // arrange
        var validOrderPlaced = "validOrderPlaced";

        // act
        abstractMessageProcessorQueueConsumer.consume(validOrderPlaced);

        // assert
        verify(messageConsumerService).process(validOrderPlaced);
    }

}
