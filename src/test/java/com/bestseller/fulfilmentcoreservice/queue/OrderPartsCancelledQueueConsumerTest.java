package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderPartsCancelledService;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsCancelledGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderPartsCancelledQueueConsumerTest {

    @InjectMocks
    private OrderPartsCancelledQueueConsumer consumer;

    @Mock
    private OrderPartsCancelledService orderPartsCancelledService;

    @Mock
    private TaskPayloadTransformer<OrderPartsCancelled> taskPayloadTransformer;

    @Test
    void shouldExecuteTask_whenOrderCancelled() {
        // arrange
        var orderPartsCancelled =
            OrderPartsCancelledGenerator.generateOrderPartsCancelled(UUID.randomUUID().toString());

        // act
        consumer.consume(orderPartsCancelled);

        // assert
        verify(orderPartsCancelledService).process(orderPartsCancelled);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(orderId);

        // Act
        String messageDetails = consumer.getMessageDetails(orderPartsCancelled);

        // Assert
        assertEquals("orderId=12345, orderLines=[]", messageDetails);
    }
}
