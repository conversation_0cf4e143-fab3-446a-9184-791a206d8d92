package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.fulfilmentcoreservice.core.service.OrderReturnedInStoreService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderReturnedInStore.OrderReturnedInStore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderReturnedInStoreQueueConsumerTest {

    @InjectMocks
    private OrderReturnedInStoreQueueConsumer consumer;

    @Mock
    private OrderReturnedInStoreService orderReturnedInStoreService;

    @Mock
    private TaskPayloadTransformer<OrderReturnedInStore> taskPayloadTransformer;

    @Test
    void shouldConsume_whenOrderReturnedInStore() {
        // arrange
        var orderReturnedInStore = new OrderReturnedInStore()
                .withOrderId(UUID.randomUUID().toString());

        // act
        consumer.consume(orderReturnedInStore);

        // assert
        verify(orderReturnedInStoreService).process(orderReturnedInStore);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var orderReturnedInStore = new OrderReturnedInStore()
            .withOrderId(orderId);

        // Act
        String messageDetails = consumer.getMessageDetails(orderReturnedInStore);

        // Assert
        assertEquals("orderId=12345, returnId=null", messageDetails);
    }
}
