package com.bestseller.fulfilmentcoreservice.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.fulfilmentcoreservice.core.service.GiftCardPurchasedService;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardPurchased.GiftCardPurchased;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class GiftCardPurchasedQueueConsumerTest {

    @InjectMocks
    private GiftCardPurchasedQueueConsumer consumer;

    @Mock
    private GiftCardPurchasedService giftCardPurchasedService;

    @Mock
    private QueueConfig queueConfig;

    @Mock
    private TaskPayloadTransformer<GiftCardPurchased> taskPayloadTransformer;

    @Test
    void shouldProcessMessage_whenGiftCardPurchasedTaskExecute() {
        // Arrange
        var giftCardPurchased = new GiftCardPurchased();

        // Act
        consumer.consume(giftCardPurchased);

        // Assert
        verify(giftCardPurchasedService).process(giftCardPurchased);
    }

    @Test
    void shouldGetPayloadTransformer() {
        // act
        var result = consumer.getPayloadTransformer();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(taskPayloadTransformer);
    }

    @Test
    void shouldGetQueueConfig() {
        // act
        var result = consumer.getQueueConfig();

        // assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(queueConfig);
    }

    @Test
    void testGetMessageDetails() {
        // Arrange
        var orderId = "12345";
        var giftCardPurchased = new GiftCardPurchased().withOrderId(orderId);

        // Act
        String messageDetails = consumer.getMessageDetails(giftCardPurchased);

        // Assert
        assertEquals("orderId=12345, giftCardNumber=", messageDetails);
    }
}
