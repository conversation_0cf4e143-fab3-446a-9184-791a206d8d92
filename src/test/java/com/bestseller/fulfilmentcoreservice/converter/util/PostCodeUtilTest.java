package com.bestseller.fulfilmentcoreservice.converter.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PostCodeUtilTest {

    @Test
    public void adjustPostCode_remove_white_space() {
        // arrange
        String rawPostCode = "      123 4YZ ";
        String desiredPostCode = "1234 YZ";

        // act
        String result = PostCodeUtil.adjustPostCode(rawPostCode);

        // assert
        Assertions.assertEquals(desiredPostCode, result, "Non-alphanumeric chars should be removed");
    }

    @Test
    public void adjustPostCode_remove_nonAlphanumeric() {
        // arrange
        String rawPostCode = "1234$YZ*&^%";
        String desiredPostCode = "1234 YZ";

        // act
        String result = PostCodeUtil.adjustPostCode(rawPostCode);

        // assert
        Assertions.assertEquals(desiredPostCode, result, "Whitespace should be removed");
    }

    @Test
    public void adjustPostCode_NL_postCode() {
        // arrange
        String rawPostCode = "NL1234YZ";
        String desiredPostCode = "1234 YZ";

        // act
        String result = PostCodeUtil.adjustPostCode(rawPostCode);

        // assert
        Assertions.assertEquals(desiredPostCode, result, "NL should be removed");
    }

    @Test
    public void adjustPostCode_NLWrongCode_postCode() {
        // arrange
        String rawPostCode = "NLIUIOUOIUI";

        // act
        PostCodeUtil.adjustPostCode(rawPostCode);

        // assert
        // No assertion, just to complete the coverage
    }

}
