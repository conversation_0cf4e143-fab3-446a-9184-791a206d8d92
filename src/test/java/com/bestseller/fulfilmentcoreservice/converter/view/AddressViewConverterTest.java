package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.AddressView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class AddressViewConverterTest {

    @InjectMocks
    private AddressViewConverterImpl converter;

    @Test
    void convert_givenAddress_returnAddressView() {
        // arrange
        var address = Address.builder()
            .id(1L)
            .postCode("1234YZ")
            .firstName("John")
            .lastName("Doe")
            .address1("Street 1")
            .address2("Street 2")
            .address3("Street 3")
            .countryCode("NL")
            .city("Amsterdam")
            .state("NH")
            .phone("1234567890")
            .houseNumber("1")
            .build();

        // act
        var addressView = converter.convert(address);

        // assert
        assertThat(addressView)
            .usingRecursiveComparison()
            .isEqualTo(
                AddressView.builder()
                    .id(1L)
                    .postCode("1234YZ")
                    .firstName("John")
                    .lastName("Doe")
                    .address1("Street 1")
                    .address2("Street 2")
                    .address3("Street 3")
                    .countryCode("NL")
                    .city("Amsterdam")
                    .state("NH")
                    .phone("1234567890")
                    .houseNumber("1")
                    .build()
            );
    }

}
