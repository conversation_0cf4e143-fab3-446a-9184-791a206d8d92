package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.AddressView;
import com.bestseller.fulfilmentcoreservice.api.dto.CustomerView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderBlockView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderCancelView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderView;
import com.bestseller.fulfilmentcoreservice.api.dto.PartnerChannelView;
import com.bestseller.fulfilmentcoreservice.api.dto.ShipmentView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderBlock;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.PartnerChannel;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ChannelType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod;
import com.logistics.statetransition.OrderState;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderViewConverterTest {

    @InjectMocks
    private OrderViewConverterImpl converter;

    @Mock
    private AddressViewConverter addressViewConverter;

    @Mock
    private CustomerViewConverter customerViewConverter;

    @Mock
    private OrderLineViewConverter orderLineViewConverter;

    @Mock
    private ShipmentViewConverter shipmentViewConverter;

    @Mock
    private PartnerChannelViewConverter partnerChannelViewConverter;

    @Test
    @SuppressWarnings("MethodLength")
    void convert_givenOrder_returnOrderView() {
        // arrange
        var address = Address.builder()
            .id(1L)
            .postCode("1234YZ")
            .firstName("John")
            .lastName("Doe")
            .address1("Street 1")
            .address2("Street 2")
            .address3("Street 3")
            .countryCode("NL")
            .city("Amsterdam")
            .state("NH")
            .phone("1234567890")
            .houseNumber("1")
            .build();
        var addressView = AddressView.builder()
            .id(1L)
            .postCode("1234YZ")
            .firstName("John")
            .lastName("Doe")
            .address1("Street 1")
            .address2("Street 2")
            .address3("Street 3")
            .countryCode("NL")
            .city("Amsterdam")
            .state("NH")
            .phone("1234567890")
            .houseNumber("1")
            .build();
        when(addressViewConverter.convert(address)).thenReturn(addressView);

        var customer = Customer.builder()
            .id(1L)
            .name("John Doe")
            .email("<EMAIL>")
            .employeeId("123456")
            .type(CustomerType.CUSTOMER)
            .isLoggedIn(true)
            .build();
        var customerView = CustomerView.builder()
            .id("1")
            .name("John Doe")
            .email("<EMAIL>")
            .employeeId("123456")
            .type(CustomerType.CUSTOMER.name())
            .isLoggedIn(true)
            .build();
        when(customerViewConverter.convert(customer)).thenReturn(customerView);
        var orderLine = OrderLine.builder()
            .lineNumber(1)
            .ean("1234567890123")
            .brandDescription("Brand")
            .name("Product")
            .build();
        var orderLineView = OrderLineView.builder()
            .orderLineNumber(1)
            .lineDescription("Product")
            .brand("Brand")
            .build();
        when(orderLineViewConverter.convert(orderLine)).thenReturn(orderLineView);
        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .partNumber(1)
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .dispatchDate(LocalDateTime.now())
            .fulfillmentNode("fulfilmentNode")
            .build();
        var shipmentView = ShipmentView.builder()
            .partNumber(1L)
            .carrier("carrierName")
            .trackingNumber("trackingNumber")
            .returnShipmentId("returnTrackingNumber")
            .dispatchDate(orderFulfillmentPart.getDispatchDate().atZone(ZoneOffset.UTC))
            .fulfilmentNode("fulfilmentNode")
            .build();
        when(shipmentViewConverter.convert(orderFulfillmentPart)).thenReturn(shipmentView);

        var partnerChannel = PartnerChannel.builder()
            .channelId("channelId")
            .description("Test Channel Description")
            .build();
        var partnerChannelView = PartnerChannelView.builder()
            .channelId("channelId")
            .description("Test Channel Description")
            .build();
        when(partnerChannelViewConverter.convert(partnerChannel)).thenReturn(partnerChannelView);

        var order = Order.builder()
            .orderId("1")
            .orderDate(ZonedDateTime.now())
            .createdTS(ZonedDateTime.now())
            .lastModifiedTS(ZonedDateTime.now())
            .version(1)
            .checkoutScopeId("checkoutScopeId")
            .market(Market.BSE_NORDIC)
            .shippingMethod(ShippingMethod.STANDARD)
            .minStatus(OrderState.PLACED)
            .maxStatus(OrderState.DISPATCHED)
            .shippingAddress(address)
            .billingAddress(address)
            .customer(customer)
            .brand(Brand.JJ)
            .currency("EUR")
            .orderLines(List.of(orderLine))
            .partnerChannel(partnerChannel)
            .platform(Platform.DEMANDWARE)
            .channelType(ChannelType.STOREFRONT)
            .orderFulfillmentParts(List.of(orderFulfillmentPart))
            .isoStoreId("1234")
            .build();

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderView.builder()
                    .id("1")
                    .orderDate(order.getOrderDate())
                    .createdAt(order.getCreatedTS())
                    .lastUpdate(order.getLastModifiedTS())
                    .version(1)
                    .checkout("checkoutScopeId")
                    .market("BSE-Nordic")
                    .store("DEMANDWARE")
                    .shippingMethod("STANDARD")
                    .minStatus(OrderState.PLACED.getIdentifier())
                    .maxStatus(OrderState.DISPATCHED.getIdentifier())
                    .shippingAddress(addressView)
                    .billingAddress(addressView)
                    .customer(customerView)
                    .brand(Brand.JJ.getDescription())
                    .brandAbbreviation(Brand.JJ.getBrandAbbreviation())
                    .currency("EUR")
                    .orderLines(List.of(orderLineView))
                    .shipments(List.of(shipmentView))
                    .orderBlock(
                        OrderBlockView.builder()
                            .canBeBlocked(true)
                            .canBeUnblocked(false)
                            .build()
                    )
                    .orderCancel(
                        OrderCancelView.builder()
                            .canBeCancelled(false)
                            .build()
                    )
                    .partnerChannel(partnerChannelView)
                    .isoStoreId("1234")
                    .channelType(ChannelType.STOREFRONT.name())
                    .build()
            );
    }

    @Test
    void convert_givenOrderWithBlockedMinStatus_orderCanBeCancelled() {
        // arrange
        var order = createOrder(OrderState.BLOCKED);

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderCancel().canBeCancelled()).isTrue();
    }

    @Test
    void convert_givenOrderWithBlockedMinStatus_orderCanNotBeCancelled() {
        // arrange
        var order = createOrder(OrderState.DISPATCHED);

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderCancel().canBeCancelled()).isFalse();
    }

    @Test
    void convert_givenOrderWithPlacedMinStatusAndEmptyOrderBlock_orderCanBeUnblocked() {
        // arrange
        var order = createOrder(OrderState.PLACED);
        order.setOrderBlock(OrderBlock.builder()
            .resumeState(OrderState.RESUBMIT)
            .build());

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderBlock().canBeUnblocked()).isTrue();
    }

    @Test
    void convert_givenOrderWithCancelledMinStatus_orderCanNotBeUnblocked() {
        // arrange
        var order = createOrder(OrderState.CANCELLED);

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderBlock().canBeUnblocked()).isFalse();
    }

    @ParameterizedTest
    @NullSource
    void convert_givenOrderWithoutOrderBlocks_orderCanBeBlocked(OrderBlock orderBlock) {
        // arrange
        var order = Order.builder()
            .minStatus(OrderState.PLACED)
            .platform(Platform.DEMANDWARE)
            .orderBlock(orderBlock)
            .orderLines(List.of(OrderLine.builder().build()))
            .build();

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderBlock().canBeBlocked()).isTrue();
    }

    @Test
    void convert_givenOrderWithOrderBlocks_orderCanNotBeBlocked() {
        // arrange
        var order = Order.builder()
            .minStatus(OrderState.PLACED)
            .platform(Platform.DEMANDWARE)
            .orderBlock(
                OrderBlock.builder()
                    .resumeState(OrderState.RESUBMIT)
                    .build()
            )
            .orderLines(List.of(OrderLine.builder().build()))
            .build();

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderBlock().canBeBlocked()).isFalse();
    }

    @Test
    void convert_givenOrderContainsVirtualProducts_orderCanNotBeBlocked() {
        // arrange
        var order = Order.builder()
            .minStatus(OrderState.PLACED)
            .platform(Platform.DEMANDWARE)
            .orderLines(List.of(
                OrderLine.builder()
                    .virtualProduct(true)
                    .build(),
                OrderLine.builder()
                    .virtualProduct(false)
                    .build()
            ))
            .build();

        // act
        var orderView = converter.convert(order);

        // assert
        assertThat(orderView.orderBlock().canBeBlocked()).isFalse();
    }

    private Order createOrder(OrderState minStatus) {
        return Order.builder()
            .minStatus(minStatus)
            .orderLines(List.of(OrderLine.builder().build()))
            .build();
    }
}
