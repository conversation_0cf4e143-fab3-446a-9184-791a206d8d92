package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.CustomerView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class CustomerViewConverterTest {

    @InjectMocks
    private CustomerViewConverterImpl converter;

    @Test
    void convert_givenCustomer_returnCustomerView() {
        // arrange
        var customer = Customer.builder()
            .customerId("1")
            .name("John Doe")
            .email("<EMAIL>")
            .employeeId("123456")
            .type(CustomerType.CUSTOMER)
            .isLoggedIn(true)
            .build();

        // act
        var customerView = converter.convert(customer);

        // assert
        assertThat(customerView)
            .usingRecursiveComparison()
            .isEqualTo(
                CustomerView.builder()
                    .id("1")
                    .name("John Doe")
                    .email("<EMAIL>")
                    .employeeId("123456")
                    .type(CustomerType.CUSTOMER.name())
                    .isLoggedIn(true)
                    .build()
            );
    }

}
