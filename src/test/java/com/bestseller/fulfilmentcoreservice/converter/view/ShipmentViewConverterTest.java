package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.ShipmentView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class ShipmentViewConverterTest {

    @InjectMocks
    private ShipmentViewConverterImpl converter;

    @Test
    void convert_givenShipment_returnShipmentView() {
        // arrange
        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .partNumber(1)
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .dispatchDate(LocalDateTime.now())
            .fulfillmentNode("fulfilmentNode")
            .build();

        // act
        var shipmentView = converter.convert(orderFulfillmentPart);

        // assert
        assertThat(shipmentView)
            .usingRecursiveComparison()
            .isEqualTo(
                ShipmentView.builder()
                    .partNumber(1L)
                    .carrier("carrierName")
                    .trackingNumber("trackingNumber")
                    .returnShipmentId("returnTrackingNumber")
                    .dispatchDate(orderFulfillmentPart.getDispatchDate().atZone(ZoneOffset.UTC))
                    .fulfilmentNode("fulfilmentNode")
                    .build()
            );
    }

    @Test
    void convert_givenNullShipmentDispatchDate_returnShipmentView() {
        // arrange
        var orderFulfillmentPart = OrderFulfillmentPart.builder()
            .partNumber(1)
            .carrierName("carrierName")
            .trackingNumber("trackingNumber")
            .returnTrackingNumber("returnTrackingNumber")
            .dispatchDate(null)
            .fulfillmentNode("fulfilmentNode")
            .build();

        // act
        var shipmentView = converter.convert(orderFulfillmentPart);

        // assert
        assertThat(shipmentView)
            .usingRecursiveComparison()
            .isEqualTo(
                ShipmentView.builder()
                    .partNumber(1L)
                    .carrier("carrierName")
                    .trackingNumber("trackingNumber")
                    .returnShipmentId("returnTrackingNumber")
                    .dispatchDate(null)
                    .fulfilmentNode("fulfilmentNode")
                    .build()
            );
    }

}
