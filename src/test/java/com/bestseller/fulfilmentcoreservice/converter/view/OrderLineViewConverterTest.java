package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineQuantityStatusView;
import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderLineViewConverterTest {

    @InjectMocks
    private OrderLineViewConverterImpl converter;

    @Mock
    private OrderLineQuantityStatusViewConverter orderLineQuantityStatusViewConverter;

    @Test
    void convert_givenOrderLine_returnOrderLineView() {
        // arrange
        var orderLineQtyStatuses = List.of(
            OrderLineQtyStatus.builder()
                .orderState(OrderState.PLACED)
                .orderFulfillmentPart(
                    OrderFulfillmentPart.builder()
                        .fulfillmentNode("fulfillmentNode1")
                        .build()
                )
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.CANCELLED)
                .orderFulfillmentPart(null)
                .build()
        );
        var orderLineQuantityStatusViews = List.of(
            OrderLineQuantityStatusView.builder()
                .orderLineStatus("PLACED")
                .fulfillmentNode("fulfillmentNode1")
                .build(),
            OrderLineQuantityStatusView.builder()
                .orderLineStatus("CANCELLED")
                .build()
        );
        when(orderLineQuantityStatusViewConverter.convert(orderLineQtyStatuses))
            .thenReturn(orderLineQuantityStatusViews);

        var orderLine = OrderLine.builder()
            .lineNumber(1)
            .ean("1234567890123")
            .brandDescription("Brand")
            .name("Product")
            .orderLineQtyStatus(orderLineQtyStatuses)
            .build();

        // act
        var orderLineView = converter.convert(orderLine);

        // assert
        assertThat(orderLineView)
            .usingRecursiveComparison()
            .isEqualTo(
                OrderLineView.builder()
                    .orderLineNumber(1)
                    .lineDescription("Product")
                    .ean("1234567890123")
                    .brand("Brand")
                    .orderLineQuantityStatus(orderLineQuantityStatusViews)
                    .build()
            );
    }

}
