package com.bestseller.fulfilmentcoreservice.converter.view;

import com.bestseller.fulfilmentcoreservice.api.dto.OrderLineQuantityStatusView;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderLineQuantityStatusViewConverterTest {

    @InjectMocks
    private OrderLineQuantityStatusViewConverterImpl converter;

    @Test
    void convert_givenListOfOrderLineQtyStatus_returnListOfOrderLineQuantityStatusView() {
        // arrange
        var orderLineQtyStatus = List.of(
            OrderLineQtyStatus.builder()
                .orderState(OrderState.PLACED)
                .orderFulfillmentPart(
                    OrderFulfillmentPart.builder()
                        .fulfillmentNode("fulfillmentNode1")
                        .build()
                )
                .build(),
            OrderLineQtyStatus.builder()
                .orderState(OrderState.CANCELLED)
                .orderFulfillmentPart(null)
                .build()
        );

        // act
        var orderLineQuantityStatusView = converter.convert(orderLineQtyStatus);

        // assert
        assertThat(orderLineQuantityStatusView)
            .usingRecursiveComparison()
            .isEqualTo(
                List.of(
                    OrderLineQuantityStatusView.builder()
                        .orderLineStatus("PLACED")
                        .fulfillmentNode("fulfillmentNode1")
                        .build(),
                    OrderLineQuantityStatusView.builder()
                        .orderLineStatus("CANCELLED")
                        .build()
                )
            );
    }

}
