package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderModelGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Brand;
import com.bestseller.fulfilmentcoreservice.persistence.enums.Market;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderModelGenerator.ANNOUNCED_DELIVERY_DATE;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.Brand.JJ;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.STANDARD;
import static com.bestseller.fulfilmentcoreservice.persistence.enums.ShippingMethod.VIRTUAL;
import static com.logistics.statetransition.OrderState.PLACED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OrderEntityConverterTest {

    @InjectMocks
    private OrderEntityConverterImpl converter;

    @Mock
    private AddressEntityConverter addressEntityConverter;

    @Mock
    private CustomerEntityConverter customerEntityConverter;

    @Mock
    private OrderLineEntityConverter orderLineEntityConverter;

    @Test
    void convert_givenValidDemandwareOrderModel_returnsOrder() {
        // arrange
        var orderId = "612678123";
        var tbOrderId = "TB".concat(orderId);
        var message = OrderModelGenerator.generateOrderModel(tbOrderId);

        // act
        Order result = converter.convert(message);

        // assert
        assertThat(result)
            .as("OrderModel was converted to OrderEntity")
            .extracting("orderId", "orderDate", "checkoutScopeId", "market",
                "platform", "channelType", "shippingMethod", "currency",
                "minStatus", "maxStatus", "externalOrderId", "test", "billToMatchesShipTo",
                "brand", "partnerChannel", "announcedDeliveryDate")
            .containsExactlyInAnyOrder(message.getOrderId(), message.getOrderDate(),
                message.getCheckoutScopeId(), Market.BSE_DACH, Platform.DEMANDWARE, null,
                STANDARD, null, PLACED, PLACED, message.getExternalOrderId(), message.isTest(),
                message.getBillToMatchesShipTo(), JJ, null, ANNOUNCED_DELIVERY_DATE);

        verify(addressEntityConverter).convert(message.getShippingAddress());
        verify(addressEntityConverter).convert(message.getBillingAddress());
        verify(customerEntityConverter).convert(message.getCustomer());
        verify(orderLineEntityConverter, times(2)).convert(any());
    }

    @Test
    void convert_givenValidDemandwareOrderModelWithoutBrandedShipping_returnsOrder() {
        // arrange
        var orderId = "612678123";
        var tbOrderId = "TB".concat(orderId);
        var message = OrderModelGenerator.generateOrderModel(tbOrderId);
        message.setBrandedShipping(null);

        // act
        var result = converter.convert(message);

        // assert
        assertThat(result)
            .as("OrderModel was converted to OrderEntity")
            .extracting("orderId", "orderDate", "checkoutScopeId", "market",
                "platform", "channelType", "shippingMethod", "currency",
                "minStatus", "maxStatus", "externalOrderId", "test", "billToMatchesShipTo",
                "brand", "partnerChannel")
            .containsExactlyInAnyOrder(message.getOrderId(), message.getOrderDate(),
                message.getCheckoutScopeId(), Market.BSE_DACH, Platform.DEMANDWARE, null,
                STANDARD, null, PLACED, PLACED, message.getExternalOrderId(),
                message.isTest(), message.getBillToMatchesShipTo(), Brand.MB,
                null);

        verify(addressEntityConverter).convert(message.getShippingAddress());
        verify(addressEntityConverter).convert(message.getBillingAddress());
        verify(customerEntityConverter).convert(message.getCustomer());
        verify(orderLineEntityConverter, times(2)).convert(any());
    }

    @Test
    void convert_givenValidTradebyteOrderModel_returnsOrder() {
        // arrange
        var orderId = "612678123";
        var tbOrderId = "TB".concat(orderId);
        var message = OrderModelGenerator.generateOrderModel(tbOrderId);
        message.getOrderLines().forEach(orderLine -> orderLine.setVirtualProduct(true));
        message.setPlatform(Platform.TRADEBYTE.name());

        // act
        var result = converter.convert(message);

        // assert
        assertThat(result)
            .as("OrderModel was converted to OrderEntity")
            .extracting("orderId", "orderDate", "checkoutScopeId", "market",
                "platform", "channelType", "shippingMethod", "currency",
                "minStatus", "maxStatus", "externalOrderId", "test", "billToMatchesShipTo",
                "brand", "partnerChannel", "offlinePayment")
            .containsExactlyInAnyOrder(message.getOrderId(), message.getOrderDate(),
                message.getCheckoutScopeId(), Market.BSE_DACH, Platform.TRADEBYTE, null,
                STANDARD, null, PLACED, PLACED, message.getExternalOrderId(),
                message.isTest(), message.getBillToMatchesShipTo(), null, null, true);

        verify(addressEntityConverter).convert(message.getShippingAddress());
        verify(addressEntityConverter).convert(message.getBillingAddress());
        verify(customerEntityConverter).convert(message.getCustomer());
        verify(orderLineEntityConverter, times(2)).convert(any());
    }

    @Test
    void convert_givenValidVirtualDemandwareOrderModel_returnsOrder() {
        // arrange
        var orderId = "612678123";
        var tbOrderId = "TB".concat(orderId);
        var message = OrderModelGenerator.generateOrderModel(tbOrderId);
        message.setPlatform(Platform.DEMANDWARE.name());
        message.getOrderLines().forEach(orderLine -> orderLine.setVirtualProduct(true));

        // act
        var result = converter.convert(message);

        // assert
        assertThat(result)
            .as("OrderModel was converted to OrderEntity")
            .extracting("orderId", "orderDate", "checkoutScopeId", "market",
                "platform", "channelType", "shippingMethod", "currency",
                "minStatus", "maxStatus", "externalOrderId", "test", "billToMatchesShipTo",
                "brand", "partnerChannel")
            .containsExactlyInAnyOrder(message.getOrderId(), message.getOrderDate(),
                message.getCheckoutScopeId(), Market.BSE_DACH, Platform.DEMANDWARE, null,
                VIRTUAL, null, PLACED, PLACED, message.getExternalOrderId(),
                message.isTest(), message.getBillToMatchesShipTo(), JJ, null);

        verify(addressEntityConverter).convert(message.getShippingAddress());
        verify(addressEntityConverter).convert(message.getBillingAddress());
        verify(customerEntityConverter).convert(message.getCustomer());
        verify(orderLineEntityConverter, times(2)).convert(any());
    }
}
