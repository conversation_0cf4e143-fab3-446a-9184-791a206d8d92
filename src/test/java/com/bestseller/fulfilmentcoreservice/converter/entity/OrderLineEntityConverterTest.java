package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.model.OrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderLineModelGenerator;
import com.logistics.statetransition.OrderState;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderLineEntityConverterTest {

    private final OrderLineEntityConverter converter = Mappers.getMapper(OrderLineEntityConverter.class);

    @Test
    void convert_givenOrderLine_successfullyConverted() {
        // arrange
        var orderLineModel = OrderLineModelGenerator.generateOrderLine();

        // act
        var orderLine = converter.convert(orderLineModel);

        // assert
        assertThat(orderLine)
            .as("Order line root is successfully converted")
            .extracting("bonusProduct", "lineNumber", "brandDescription", "ecomId")
            .containsExactlyInAnyOrder(orderLineModel.isBonusProduct(), orderLineModel.getLineNumber(),
                orderLineModel.getBrandDescription(), OrderLineModelGenerator.ECOM_ID);

        OrderLineQtyStatusModel orderLineQtyStatusModel = orderLineModel.getOrderLineQtyStatus().get(0);
        assertThat(orderLine.getOrderLineQtyStatus())
            .as("Order line root is successfully converted")
            .extracting("orderState", "prevStatus", "tradebyteOrderLineQtyStatus.originalLineNumber",
                "tradebyteOrderLineQtyStatus.lastTradebyteStatus",
                "indexCol")
            .containsExactlyInAnyOrder(Tuple.tuple(OrderState.PLACED, OrderState.PLACED,
                orderLineQtyStatusModel.getTradebyteOrderLineQtyStatus().getOriginalLineNumber(),
                OrderState.EXPORTED, 0));
    }
}
