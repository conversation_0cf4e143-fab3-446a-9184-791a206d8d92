package com.bestseller.fulfilmentcoreservice.converter.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class CarrierVariantConverterTest {

    @InjectMocks
    private CarrierVariantMapperImpl converter;

    @Test
    void convert_givenEmptyCarrierVariant_returnsNull() {
        // arrange
        var carrierVariant = "";

        // act
        var result = converter.convert(carrierVariant);

        // assert
        assertThat(result)
            .as("CarrierVariant is empty")
            .isNull();
    }

    @Test
    void convert_givenNullCarrierVariant_returnsNull() {
        // arrange
        String carrierVariant = null;

        // act
        var result = converter.convert(carrierVariant);

        // assert
        assertThat(result)
            .as("CarrierVariant is null")
            .isNull();
    }
}
