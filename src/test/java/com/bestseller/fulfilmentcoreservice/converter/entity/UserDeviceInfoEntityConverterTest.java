package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.utils.CustomerModelGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class UserDeviceInfoEntityConverterTest {

    @InjectMocks
    private UserDeviceInfoEntityConverterImpl userDeviceInfoEntityConverter;

    @Test
    void convertToUserDeviceInfo_givenOrderAndCustomerModel() {
        // arrange
        String testOrderId = "OL1200297343";
        var customerModel = CustomerModelGenerator.generateCustomer();

        // act
        var userDeviceInfo = userDeviceInfoEntityConverter.toUserDeviceInfo(customerModel);

        // assert
        assertThat(userDeviceInfo)
            .as("UserDeviceInfo is successfully converted")
            .extracting("locale", "platform",
                "appVersion", "originalScopeId")
            .containsExactlyInAnyOrder("en-dk", "mobile", "version",
                "originalScopeId");
    }
}
