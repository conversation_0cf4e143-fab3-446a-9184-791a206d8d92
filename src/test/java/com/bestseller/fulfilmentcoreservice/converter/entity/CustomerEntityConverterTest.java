package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.utils.CustomerModelGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Customer;
import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class CustomerEntityConverterTest {
    private final CustomerEntityConverter converter = Mappers.getMapper(CustomerEntityConverter.class);

    @Test
    void convert_givenValidCustomer_returnsCustomerEntity() {
        // arrange
        var customerModel = CustomerModelGenerator.generateCustomer();

        // act
        Customer customer = converter.convert(customerModel);

        // assert
        assertThat(customer)
            .as("Customer conversion successful completed")
            .extracting("customerId", "name", "email", "type", "employeeId",
                "externalCustomerId", "isLoggedIn")
            .containsExactlyInAnyOrder(customerModel.getCustomerId(), customerModel.getName(),
                customerModel.getEmail(), CustomerType.EMPLOYEE,
                customerModel.getEmployeeId(), customerModel.getExternalCustomerId(),
                true);
    }
}
