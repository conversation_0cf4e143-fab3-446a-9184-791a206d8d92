package com.bestseller.fulfilmentcoreservice.converter.entity;

import com.bestseller.fulfilmentcoreservice.core.utils.AddressModelGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.Address;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.bestseller.fulfilmentcoreservice.persistence.enums.CarrierVariant.HOME;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class AddressEntityConverterTest {

    private static final String CHAR_SET =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    private static final int SIZE_4 = 4;
    private static final int SIZE_10 = 10;
    private static final int SIZE_24 = 24;
    private static final int SIZE_25 = 25;
    private static final int SIZE_26 = 26;
    private static final int SIZE_32 = 32;
    private static final int SIZE_40 = 40;
    private static final int SIZE_20 = 20;

    @InjectMocks
    private AddressEntityConverterImpl converter;

    @Mock
    private CarrierVariantMapper carrierVariantMapper;

    @Test
    void convert_givenValidAddress_returnsAddressEntity() {
        // arrange
        var addressModel = AddressModelGenerator.generateAddress();

        // act
        Address addressEntity = converter.convert(addressModel);

        // assert
        assertThat(addressEntity)
            .as("Conversion successful completed")
            .extracting("postCode", "firstName", "lastName", "address1", "address2",
                "address3", "countryCode", "city", "state", "phone", "salutation", "carrierVariant",
                "physicalStoreId", "deliveryOption", "parcelLocker",
                "houseNumber", "houseNumberExt")
            .containsExactlyInAnyOrder(addressModel.getPostCode(), addressModel.getFirstName(),
                addressModel.getLastName(), addressModel.getAddress1(), addressModel.getAddress2(),
                addressModel.getAddress3(), addressModel.getCountryCode(), addressModel.getCity(),
                addressModel.getState(), addressModel.getPhone(), addressModel.getSalutation(),
                HOME, addressModel.getPhysicalStoreId(),
                addressModel.getDeliveryOption(), addressModel.getParcelLocker(),
                addressModel.getHouseNumber(), addressModel.getHouseNumberExt().substring(0, SIZE_10));
    }

    @Test
    void convert_givenLongAddressInOrderModel_returnsOrder() {
        // arrange
        var addressModel = AddressModelGenerator.generateAddress();

        addressModel.setAddress1(CHAR_SET.substring(0, SIZE_40));
        addressModel.setAddress2(CHAR_SET.substring(0, SIZE_40));
        addressModel.setAddress3(CHAR_SET.substring(0, SIZE_32));
        addressModel.setCity(CHAR_SET.substring(0, SIZE_40));
        addressModel.setCountryCode(CHAR_SET.substring(0, SIZE_40));
        addressModel.setPostCode(CHAR_SET.substring(0, SIZE_40));
        addressModel.setLastName(CHAR_SET.substring(0, SIZE_40));
        addressModel.setFirstName(CHAR_SET.substring(0, SIZE_40));
        addressModel.setSalutation(CHAR_SET.substring(0, SIZE_40));
        addressModel.setState(CHAR_SET.substring(0, SIZE_40));
        addressModel.setHouseNumber(CHAR_SET.substring(0, SIZE_40));
        addressModel.setHouseNumberExt(CHAR_SET.substring(0, SIZE_40));
        addressModel.setPhone(CHAR_SET.substring(0, SIZE_40));

        // act
        var result = converter.convert(addressModel);

        // assert
        assertThat(result).isNotNull();
        // Check address field lengths
        assertThat(result.getAddress1()).hasSize(SIZE_32);
        assertThat(result.getAddress2()).hasSize(SIZE_32);
        assertThat(result.getAddress3()).hasSize(SIZE_25);
        assertThat(result.getCity()).hasSize(SIZE_24);
        assertThat(result.getPhone()).hasSize(SIZE_20);
        assertThat(result.getFirstName()).hasSize(SIZE_32);
        assertThat(result.getLastName()).hasSize(SIZE_26);
        assertThat(result.getHouseNumber()).hasSize(SIZE_32);
        assertThat(result.getHouseNumberExt()).hasSize(SIZE_10);
        assertThat(result.getCountryCode()).hasSize(SIZE_4);
        assertThat(result.getSalutation()).hasSize(SIZE_10);
        assertThat(result.getPostCode()).hasSize(SIZE_10);
        assertThat(result.getState()).hasSize(SIZE_10);
    }
}
