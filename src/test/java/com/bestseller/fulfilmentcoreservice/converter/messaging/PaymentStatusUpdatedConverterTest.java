package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderDetails;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderLine;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.Payment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class PaymentStatusUpdatedConverterTest {
    private static final String PAYMENT_SUB_METHOD = "ADYEN_CREDIT_CARD";
    private static final String PAYMENT_METHOD_NAME = "ADYEN_CARD";
    private static final Double ORDER_VALUE = 399.95;
    private static final Double SHIPPING_FEES = 0.0;
    private static final Double SHIPPING_FEES_TAX_PERCENTAGE = 0.25;

    @InjectMocks
    private PaymentStatusUpdatedConverterImpl converter;

    @Test
    public void convert_paymentStatusUpdated_payload() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var payload = AuthorizedPayload.builder()
            .payments(List.of(Payment.builder()
                .name(PAYMENT_METHOD_NAME)
                .subMethod(PAYMENT_SUB_METHOD)
                .amount(BigDecimal.valueOf(ORDER_VALUE))
                .build()))
            .orderDetails(OrderDetails.builder()
                .orderValue(BigDecimal.valueOf(ORDER_VALUE))
                .shippingFees(BigDecimal.valueOf(SHIPPING_FEES))
                .shippingFeesTaxPercentage(BigDecimal.valueOf(SHIPPING_FEES_TAX_PERCENTAGE))
                .shippingFeesCancelled(Boolean.FALSE).build())
            .orderLines(List.of(
                OrderLine.builder()
                    .ean("5715423786800")
                    .discountValue(BigDecimal.valueOf(SHIPPING_FEES))
                    .taxPercentage(BigDecimal.valueOf(SHIPPING_FEES_TAX_PERCENTAGE))
                    .retailPrice(BigDecimal.valueOf(ORDER_VALUE)).build()
            )).build();
        // act
        var paymentStatusUpdatedModel = converter.toPaymentStatusUpdatedModel(order.getOrderId(), payload);

        assertThat(paymentStatusUpdatedModel.getOrderDetails())
            .as("PaymentStatusUpdatedModel.OrderDetails successfully converted")
            .extracting("carrier", "carrierVariant", "shippingMethod",
                "externalOrderNumber", "orderCreationDate", "orderValue", "shippingFees",
                "shippingFeesTaxPercentage", "shippingFeesCancelled", "currency", "checkout")
            .containsExactlyInAnyOrder(null, null, null, null, null,
                BigDecimal.valueOf(ORDER_VALUE), BigDecimal.valueOf(SHIPPING_FEES),
                BigDecimal.valueOf(SHIPPING_FEES_TAX_PERCENTAGE), false, null, null);

        assertThat(paymentStatusUpdatedModel.getPayments().get(0))
            .as("PaymentStatusUpdatedModel.Payments successfully converted")
            .extracting("name", "subMethod", "amount")
            .containsExactlyInAnyOrder("ADYEN_CARD", "ADYEN_CREDIT_CARD", BigDecimal.valueOf(ORDER_VALUE));

        assertThat(paymentStatusUpdatedModel.getOrderLines().get(0))
            .as("PaymentStatusUpdatedModel.OrderLines successfully converted")
            .extracting("ean", "lineNumber")
            .containsExactlyInAnyOrder("5715423786800", null);
    }
}
