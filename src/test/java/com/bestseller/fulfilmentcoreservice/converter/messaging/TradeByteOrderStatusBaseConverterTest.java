package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.converter.view.TradeByteOrderStatusBaseConverterImpl;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsCancelledGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.OrderLineReturned;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
public class TradeByteOrderStatusBaseConverterTest {

    @InjectMocks
    private TradeByteOrderStatusBaseConverterImpl converter;

    @Test
    public void convert_orderStatusUpdateTradeByte_whenStatusIsReturned() {
        // arrange
        var tbOrderId = "TB612678123";
        var orderLineReturned = new OrderLineReturned();
        orderLineReturned.setOrderId(tbOrderId);
        orderLineReturned.setEan("5715423786800");
        final var quantity = 4;
        orderLineReturned.setLineNumber(1);
        orderLineReturned.setQuantity(quantity);

        // act
        var tradeByteOrderStatusBaseModel = converter.toReturned(orderLineReturned);

        // assert
        assertThat(tradeByteOrderStatusBaseModel)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "ean", "lineNumber", "quantity")
            .containsExactlyInAnyOrder(tbOrderId,
                "5715423786800", 1, quantity);

        assertThat(tradeByteOrderStatusBaseModel.getOrderState().name()).isEqualTo(OrderState.RETURNED.name());
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenStatusIsCancelled() {
        // arrange
        var tbOrderId = "TB612678123";
        var orderPartsCancelled = OrderPartsCancelledGenerator.generateOrderPartsCancelled(tbOrderId);

        // act
        var tradeByteOrderStatusBaseModel = converter.toCancelled(orderPartsCancelled);

        // assert
        assertThat(tradeByteOrderStatusBaseModel)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "orderState")
            .containsExactlyInAnyOrder(tbOrderId, OrderState.CANCELLED);
    }
}
