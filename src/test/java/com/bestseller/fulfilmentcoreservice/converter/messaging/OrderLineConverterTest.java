package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.OrderLineQtyStatusModel;
import com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLinePromotion;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class OrderLineConverterTest {

    private final OrderLineConverter converter = Mappers.getMapper(OrderLineConverter.class);

    @Test
    void toOrderLines_givenValidDemandwareOrder_returnsOrderLinesModel() {
        // arrange
        String orderId = "OL8899789789";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertThat(result)
            .as("Fields were correctly converted")
            .extracting("ean", "skuId", "lineNumber", "name", "partnerReference", "standardRetailPrice", "originalQty",
                "openQty", "brandDescription", "type", "bonusProduct", "taxRate", "vatClassId")
            .containsExactlyInAnyOrder(
                tuple(
                    ValidOrderPlacedGenerator.EAN_1,
                    ValidOrderPlacedGenerator.EAN_1,
                    1,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_1,
                    null,
                    ValidOrderPlacedGenerator.LIST_PRICE_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.BRAND_1,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    ValidOrderPlacedGenerator.VAT_CLASS
                ),

                tuple(
                    ValidOrderPlacedGenerator.EAN_2,
                    ValidOrderPlacedGenerator.EAN_2,
                    2,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_2,
                    null,
                    ValidOrderPlacedGenerator.RETAIL_PRICE_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.BRAND_2,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    null
                )
            );
    }

    @Test
    void toOrderLines_givenOnlinePaymentOrder_throwsException() {
        // arrange
        String orderId = "OL8899789710";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        validOrderPlaced.setOrderLines(new ArrayList<>(validOrderPlaced.getOrderLines()));
        validOrderPlaced.getOrderLines().add(validOrderPlaced.getOrderLines().get(1));
        validOrderPlaced.setOfflinePayment(false);

        // act
        var exception = assertThrows(
            IllegalArgumentException.class,
            () -> converter.toOrderLines(validOrderPlaced));
        assertThat(exception)
            .as("Exception matches")
            .hasMessage("Order OL8899789710 contains multiple lines with same ean 5715209167540 "
                + "but merging lines for online payment is not allowed. It will be rejected.");
    }

    @Test
    void toOrderLines_givenValidTradebyteOrder_returnsOrderLinesModel() {
        // arrange
        String orderId = "TB000781273";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateTradebyteOrder(orderId);

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertThat(result)
            .as("Fields were correctly converted")
            .extracting("ean", "skuId", "lineNumber", "name", "partnerReference", "standardRetailPrice", "originalQty",
                "openQty", "brandDescription", "type", "bonusProduct", "taxRate", "vatClassId")
            .containsExactlyInAnyOrder(
                tuple(
                    ValidOrderPlacedGenerator.EAN_1,
                    ValidOrderPlacedGenerator.EAN_1,
                    1,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_1,
                    null,
                    ValidOrderPlacedGenerator.LIST_PRICE_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.BRAND_1,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    ValidOrderPlacedGenerator.VAT_CLASS
                ),

                tuple(
                    ValidOrderPlacedGenerator.EAN_2,
                    ValidOrderPlacedGenerator.EAN_2,
                    2,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_2,
                    null,
                    ValidOrderPlacedGenerator.RETAIL_PRICE_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.BRAND_2,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    null
                )
            );

        List<OrderLineQtyStatusModel> orderLineQtyStatus1 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 1)
            .findFirst().get()
            .getOrderLineQtyStatus();

        List<OrderLineQtyStatusModel> orderLineQtyStatus2 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 2)
            .findFirst().get()
            .getOrderLineQtyStatus();

        assertThat(orderLineQtyStatus1.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 1 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_1, null);

        assertThat(orderLineQtyStatus2.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 2 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_2, null);
    }

    @Test
    void toOrderLines_givenValidIsoDemandwareOrder_returnsOrderLinesModel() {
        // arrange
        String orderId = "OL8899789710";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        validOrderPlaced.setOrderLines(new ArrayList<>(validOrderPlaced.getOrderLines()));
        validOrderPlaced.getOrderLines().add(validOrderPlaced.getOrderLines().get(1));

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertThat(result)
            .as("Fields were correctly converted")
            .extracting("ean", "skuId", "lineNumber", "name", "partnerReference", "standardRetailPrice", "originalQty",
                "openQty", "brandDescription", "type", "bonusProduct", "taxRate", "vatClassId")
            .containsExactlyInAnyOrder(
                tuple(
                    ValidOrderPlacedGenerator.EAN_1,
                    ValidOrderPlacedGenerator.EAN_1,
                    1,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_1,
                    null,
                    ValidOrderPlacedGenerator.LIST_PRICE_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.BRAND_1,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    ValidOrderPlacedGenerator.VAT_CLASS
                ),

                tuple(
                    ValidOrderPlacedGenerator.EAN_2,
                    ValidOrderPlacedGenerator.EAN_2,
                    2,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_2,
                    null,
                    ValidOrderPlacedGenerator.RETAIL_PRICE_2,
                    ValidOrderPlacedGenerator.QUANTITY_2 * 2,
                    ValidOrderPlacedGenerator.QUANTITY_2 * 2,
                    ValidOrderPlacedGenerator.BRAND_2,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    null
                )
            );
    }

    @Test
    void toOrderLines_givenValidTradebyteOrderWithRepeatedOrderLines_returnsOrderLinesModel() {
        // arrange
        String orderId = "TB000781273";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateTradebyteOrder(orderId);
        validOrderPlaced.setOrderLines(new ArrayList<>(validOrderPlaced.getOrderLines()));
        validOrderPlaced.getOrderLines().add(validOrderPlaced.getOrderLines().get(0));

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertThat(result)
            .as("Fields were correctly converted")
            .extracting("ean", "skuId", "lineNumber", "name", "partnerReference", "standardRetailPrice", "originalQty",
                "openQty", "brandDescription", "type", "bonusProduct", "taxRate", "vatClassId")
            .containsExactlyInAnyOrder(
                tuple(
                    ValidOrderPlacedGenerator.EAN_1,
                    ValidOrderPlacedGenerator.EAN_1,
                    1,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_1,
                    null,
                    ValidOrderPlacedGenerator.LIST_PRICE_1,
                    ValidOrderPlacedGenerator.QUANTITY_1 * 2,
                    ValidOrderPlacedGenerator.QUANTITY_1 * 2,
                    ValidOrderPlacedGenerator.BRAND_1,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    ValidOrderPlacedGenerator.VAT_CLASS
                ),

                tuple(
                    ValidOrderPlacedGenerator.EAN_2,
                    ValidOrderPlacedGenerator.EAN_2,
                    2,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_2,
                    null,
                    ValidOrderPlacedGenerator.RETAIL_PRICE_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.BRAND_2,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    null
                )
            );

        List<OrderLineQtyStatusModel> orderLineQtyStatus1 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 1)
            .findFirst().get()
            .getOrderLineQtyStatus();

        List<OrderLineQtyStatusModel> orderLineQtyStatus2 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 2)
            .findFirst().get()
            .getOrderLineQtyStatus();

        assertThat(orderLineQtyStatus1.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 1 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_1, null);

        assertThat(orderLineQtyStatus2.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 2 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_2, null);
    }

    @Test
    void toOrderLines_givenValidTradebyteOrderWithOrderLinesWithDiscountValueAndPromotion_returnsOrderLinesModel() {
        // arrange
        String orderId = "TB000781273";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateTradebyteOrder(orderId);
        validOrderPlaced.getOrderLines().get(0).setDiscountValue(new BigDecimal("10.00"));
        validOrderPlaced.getOrderLines().get(0).setOrderLinePromotion(new OrderLinePromotion()
            .withPromotionId("PROMO_ID")
            .withCampaignId("CAMPAIGN_ID")
            .withCouponId("COUPON_ID"));

        // Negative value has no effect on the calculation
        validOrderPlaced.getOrderLines().get(1).setDiscountValue(new BigDecimal("-10.00"));

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertThat(result)
            .as("Fields were correctly converted")
            .extracting("ean", "skuId", "lineNumber", "name", "partnerReference", "standardRetailPrice", "originalQty",
                "openQty", "brandDescription", "type", "bonusProduct", "taxRate", "vatClassId", "promotionId",
                "campaignId", "couponId", "ecomId")
            .containsExactlyInAnyOrder(
                tuple(
                    ValidOrderPlacedGenerator.EAN_1,
                    ValidOrderPlacedGenerator.EAN_1,
                    1,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_1,
                    null,
                    ValidOrderPlacedGenerator.LIST_PRICE_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.QUANTITY_1,
                    ValidOrderPlacedGenerator.BRAND_1,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    ValidOrderPlacedGenerator.VAT_CLASS,
                    "PROMO_ID",
                    "CAMPAIGN_ID",
                    "COUPON_ID",
                    ValidOrderPlacedGenerator.ECOM_ORDERLINE_ID_1
                ),

                tuple(
                    ValidOrderPlacedGenerator.EAN_2,
                    ValidOrderPlacedGenerator.EAN_2,
                    2,
                    ValidOrderPlacedGenerator.PRODUCT_NAME_2,
                    null,
                    ValidOrderPlacedGenerator.RETAIL_PRICE_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.QUANTITY_2,
                    ValidOrderPlacedGenerator.BRAND_2,
                    "LINE",
                    false,
                    ValidOrderPlacedGenerator.VAT,
                    null,
                    null,
                    null,
                    null,
                    ValidOrderPlacedGenerator.ECOM_ORDERLINE_ID_2
                )
            );

        List<OrderLineQtyStatusModel> orderLineQtyStatus1 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 1)
            .findFirst().get()
            .getOrderLineQtyStatus();

        List<OrderLineQtyStatusModel> orderLineQtyStatus2 = result.stream()
            .filter(orderLine -> orderLine.getLineNumber() == 2)
            .findFirst().get()
            .getOrderLineQtyStatus();

        assertThat(orderLineQtyStatus1.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 1 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_1, null);

        assertThat(orderLineQtyStatus2.get(0).getTradebyteOrderLineQtyStatus())
            .as("OrderLineQtyStatus for order line 2 correctly converted")
            .extracting("originalLineNumber", "lastTradebyteStatus")
            .containsExactlyInAnyOrder(ValidOrderPlacedGenerator.EXTERNAL_ITEM_ID_2, null);
    }

    @Test
    void toOrderLines_givenValidDemandwareOrderWithoutListPrice_returnsOrderLinesModel() {
        // arrange
        String orderId = "OL8899789789";
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        validOrderPlaced.getOrderLines().get(0).setListPrice(new BigDecimal("49.99"));

        // act
        var result = converter.toOrderLines(validOrderPlaced);

        // assert
        assertEquals(ValidOrderPlacedGenerator.RETAIL_PRICE_2,
            result.get(1).getStandardRetailPrice());
    }

    @Test
    void testOrderLines_givenValidOrderLineWithAbnormalOrdinaryLineNumber_mapsTheExactValue() {
        String orderId = "OL8899789789";
        final int abnormalLineNUmber = 200;
        var validOrderPlaced = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        var ean = validOrderPlaced.getOrderLines().getFirst();
        validOrderPlaced.getOrderLines().getFirst().setLineNumber(abnormalLineNUmber);

        var result = converter.toOrderLines(validOrderPlaced);

        assertTrue(
            result.stream()
                .anyMatch(orderLine ->
                    orderLine.getLineNumber() == abnormalLineNUmber
                        && orderLine.getEan().equals(ean.getEan())));
    }
}
