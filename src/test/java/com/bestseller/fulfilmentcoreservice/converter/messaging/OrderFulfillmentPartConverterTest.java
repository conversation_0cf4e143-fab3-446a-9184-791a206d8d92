package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.OrderFulfilmentPartModel;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderPartsRoutedGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
public class OrderFulfillmentPartConverterTest {

    @InjectMocks
    private OrderFulfillmentPartConverterImpl converter;

    @Test
    public void convert_orderPartsRouted_orderFulfillmentPartModel() {
        // arrange
        var tbOrderId = "TB".concat("612678123");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = OrderPartsRoutedGenerator.generateOrderPartsRouted(tbOrderId);

        // act
        var orderFulfillmentPart = converter.toOrderFulfillmentPartModel(order, message);

        // assert
        assertThat(orderFulfillmentPart)
            .as("OrderFulfillmentPart successfully converted")
            .extracting("partNumber", "fulfillmentNode")
            .containsExactlyInAnyOrder(1, "fulfillmentNode");
    }

    @Test
    public void convert_orderFulfillmentPartModel_orderFulfillmentPart() {
        // arrange
        var tbOrderId = "TB".concat("612678123");
        var order = OrderGenerator.createOrder(tbOrderId);
        var orderFulfillmentPartModel = OrderFulfilmentPartModel.builder()
            .order(order)
            .partNumber(1)
            .fulfillmentNode("fulfillmentNode")
            .build();

        // act
        var orderFulfillmentPart = converter.toOrderFulfillmentPart(orderFulfillmentPartModel);

        // assert
        assertThat(orderFulfillmentPart)
            .as("OrderFulfillmentPart successfully converted")
            .extracting("partNumber", "fulfillmentNode")
            .containsExactlyInAnyOrder(1, "fulfillmentNode");
    }
}
