package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderFulfillmentPart;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderStatusUpdateInfo;
import com.bestseller.fulfilmentcoreservice.persistence.entity.TradebyteOrderLineQtyStatus;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator.FULFILMENT_NODE;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
public class OrderStatusUpdateTradeByteConverterTest {

    private static final int LINE_NUMBER = 3;

    @InjectMocks
    private OrderStatusUpdateTradeByteConverterImpl converter;

    @Test
    public void convert_orderStatusUpdateTradeByte_whenStatusIsReturned() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        // act
        var orderStatusUpdateTradebyte = converter.toReturned(order, order.getOrderLines().get(0),
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0));

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.RETURNED.getIdentifier(), 1);
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenStatusIsDispatched() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);
        OrderLineQtyStatus orderLineQtyStatus = OrderLineQtyStatus.builder()
            .orderFulfillmentPart(OrderFulfillmentPart
                .builder()
                .fulfillmentNode(FULFILMENT_NODE)
                .holdFromRouting(true).build())
            .orderState(OrderState.DISPATCHED)
            .orderStatusUpdateInfo(OrderStatusUpdateInfo.builder()
                .returnShipmentId("return-shipmentId")
                .build())
            .build();

        // act
        var orderStatusUpdateTradebyte = converter.toDispatched(order, order.getOrderLines().get(0),
            orderLineQtyStatus, "tracking-number");

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId",
                "idCode", "idCodeReturnProposal")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.DISPATCHED.getIdentifier(), 1,
                "tracking-number", "return-shipmentId");
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenOriginalLineNumberIsGreaterZero() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        order.getOrderLines().get(0).getOrderLineQtyStatus()
            .get(0).setTradebyteOrderLineQtyStatus(TradebyteOrderLineQtyStatus
                .builder().originalLineNumber(LINE_NUMBER).build());
        // act
        var orderStatusUpdateTradebyte = converter.toReturned(order, order.getOrderLines().get(0),
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0));

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.RETURNED.getIdentifier(), LINE_NUMBER);
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenOriginalLineNumberIsLessZero() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        order.getOrderLines().get(0).getOrderLineQtyStatus()
            .get(0).setTradebyteOrderLineQtyStatus(TradebyteOrderLineQtyStatus
                .builder().originalLineNumber(0).build());
        // act
        var orderStatusUpdateTradebyte = converter.toReturned(order, order.getOrderLines().get(0),
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0));

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.RETURNED.getIdentifier(), 1);
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenTradebyteOrderLineQuantityStatusIsNull() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        order.getOrderLines().get(0).getOrderLineQtyStatus()
            .get(0).setTradebyteOrderLineQtyStatus(null);
        // act
        var orderStatusUpdateTradebyte = converter.toReturned(order, order.getOrderLines().get(0),
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0));

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.RETURNED.getIdentifier(), 1);
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenOrderLineQuantityStatusIsNull() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        // act
        var orderStatusUpdateTradebyte = converter.toReturned(order, order.getOrderLines().get(0),
            null);

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.RETURNED.getIdentifier(), 1);
    }

    @Test
    public void convert_orderStatusUpdateTradeByte_whenStatusIsCancelled() {
        // arrange
        var tbOrderId = "TB612678123";
        var order = OrderGenerator.createOrder(tbOrderId);

        // act
        var orderStatusUpdateTradebyte = converter.toCancelled(order, order.getOrderLines().get(0),
            order.getOrderLines().get(0).getOrderLineQtyStatus().get(0));

        // assert
        assertThat(orderStatusUpdateTradebyte)
            .as("orderStatusUpdateTradeByte successfully converted")
            .extracting("orderId", "sku", "channelSign", "status", "tbOrderItemId")
            .containsExactlyInAnyOrder(tbOrderId,
                "*************", "3061", OrderState.CANCELLED.getIdentifier(), 1);
    }
}
