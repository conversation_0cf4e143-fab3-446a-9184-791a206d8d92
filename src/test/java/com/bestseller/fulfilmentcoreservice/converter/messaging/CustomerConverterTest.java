package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.enums.CustomerType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class CustomerConverterTest {

    public static final Boolean IS_LOGGED_IN = null;
    private static final String FIRST_NAME = "First Name";
    private static final String LAST_NAME = "Last Name";
    private static final String CUSTOMER_ID = "CI77897699";
    private static final String EMAIL = "<EMAIL>";
    private static final String EXTERNAL_CUSTOMER_ID = "ECI78921361289";
    private static final String EMPLOYEE_NUMBER = "EN8097218361";
    private CustomerConverter customerConverter;

    @BeforeEach
    void setUp() {
        customerConverter = Mappers.getMapper(CustomerConverter.class);
    }

    @Test
    void toCustomerModel_nullValidOrderPlaced_returnsNullCustomerModel() {
        // arrange
        ValidOrderPlaced validOrderPlaced = null;

        // act
        var result = customerConverter.toCustomerModel(validOrderPlaced);

        // assert
        assertThat(result).isNull();
    }

    @Test
    void toCustomerModel_validOrderPlacedWithShipTo_returnsCustomerModel() {
        // arrange
        Address address = new Address()
            .withAddressLine1("Address 1")
            .withFirstName(FIRST_NAME)
            .withLastName(LAST_NAME);

        ValidOrderPlaced validOrderPlaced = new ValidOrderPlaced()
            .withCustomerInformation(new CustomerInformation()
                .withCustomerId(CUSTOMER_ID)
                .withEmail(EMAIL)
                .withExternalCustomerId(EXTERNAL_CUSTOMER_ID)
                .withIsEmployee(false)
                .withIsLoggedIn(IS_LOGGED_IN)
                .withEmployeeNumber(EMPLOYEE_NUMBER))
            .withShippingInformation(new ShippingInformation()
                .withShippingAddress(address));

        // act
        var result = customerConverter.toCustomerModel(validOrderPlaced);

        // assert
        assertThat(result)
            .extracting("customerId", "name", "email", "employeeId", "type",
                "externalCustomerId", "isLoggedIn")
            .containsExactly(CUSTOMER_ID, FIRST_NAME + " " + LAST_NAME, EMAIL, null,
                CustomerType.CUSTOMER.name(), EXTERNAL_CUSTOMER_ID, IS_LOGGED_IN);
    }

    @Test
    void toCustomerModel_validOrderPlacedWithoutShipTo_returnsCustomerModel() {
        // arrange
        Address address = new Address()
            .withFirstName(FIRST_NAME)
            .withLastName(LAST_NAME);

        ValidOrderPlaced validOrderPlaced = new ValidOrderPlaced()
            .withCustomerInformation(new CustomerInformation()
                .withCustomerId(CUSTOMER_ID)
                .withEmail(EMAIL)
                .withExternalCustomerId(EXTERNAL_CUSTOMER_ID)
                .withIsEmployee(true)
                .withEmployeeNumber(EMPLOYEE_NUMBER)
                .withIsLoggedIn(false)
                .withBillingAddress(address));

        // act
        var result = customerConverter.toCustomerModel(validOrderPlaced);

        // assert
        assertThat(result)
            .extracting("customerId", "name", "email", "employeeId", "type",
                "externalCustomerId", "isLoggedIn")
            .containsExactly(CUSTOMER_ID, FIRST_NAME + " " + LAST_NAME, EMAIL, EMPLOYEE_NUMBER,
                CustomerType.EMPLOYEE.name(), EXTERNAL_CUSTOMER_ID, false);
    }

    @Test
    void toCustomerModel_validOrderPlacedWithoutShipToAddress_returnsCustomerModel() {
        // arrange
        Address address = new Address()
            .withFirstName(FIRST_NAME)
            .withLastName(LAST_NAME);

        ValidOrderPlaced validOrderPlaced = new ValidOrderPlaced()
            .withShippingInformation(new ShippingInformation())
            .withCustomerInformation(new CustomerInformation()
                .withCustomerId(CUSTOMER_ID)
                .withEmail(EMAIL)
                .withExternalCustomerId(EXTERNAL_CUSTOMER_ID)
                .withIsEmployee(true)
                .withEmployeeNumber(EMPLOYEE_NUMBER)
                 .withIsLoggedIn(true)
                .withBillingAddress(address));

        // act
        var result = customerConverter.toCustomerModel(validOrderPlaced);

        // assert
        assertThat(result)
            .extracting("customerId", "name", "email", "employeeId", "type",
                "externalCustomerId", "isLoggedIn")
            .containsExactly(CUSTOMER_ID, FIRST_NAME + " " + LAST_NAME, EMAIL, EMPLOYEE_NUMBER,
                CustomerType.EMPLOYEE.name(), EXTERNAL_CUSTOMER_ID, true);
    }

}
