package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertIterableEquals;

@ExtendWith(MockitoExtension.class)
class OrderFinalizedConverterTest {

    @InjectMocks
    private OrderFinalizedConverterImpl converter;

    @Test
    void testConvert() {
        // arrange
        var order = OrderGenerator.createOrder("OL12212");

        // act
        var actual = converter.toOrderFinalized(order);

        // assert
        assertThat(actual)
            .isNotNull();

        assertThat(actual.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(actual.getOrderLines())
            .hasSize(order.getOrderLines().size());

        for (int i = 0; i < order.getOrderLines().size(); i++) {
            var expectedOrderLine = order.getOrderLines().get(i);
            var actualOrderLine = actual.getOrderLines().get(i);
            assertThat(actualOrderLine.getLineNumber()).isEqualTo(expectedOrderLine.getLineNumber());
            assertThat(actualOrderLine.getEan()).isEqualTo(expectedOrderLine.getEan());
            assertIterableEquals(
                expectedOrderLine.getOrderLineQtyStatus()
                .stream()
                .map(orderLineQtyStatus -> orderLineQtyStatus.getOrderState().name())
                .toList(),
                actualOrderLine.getQuantityStatuses());
        }
    }
}
