package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderStatusUpdated.OrderStatusUpdated;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderStatusUpdatedConverterTest {

    @InjectMocks
    private OrderStatusUpdatedConverterImpl converter;

    @Mock
    private Clock utcClock;

    @Test
    void convert_orderStatusUpdated_whenStatusIsRouting() {
        convert_orderStatusUpdated(OrderStatusUpdated.Type.ROUTING);
    }

    @Test
    void convert_orderStatusUpdated_whenStatusIsCancelled() {
        convert_orderStatusUpdated(OrderStatusUpdated.Type.CANCELLED);
    }

    @Test
    void convert_orderStatusUpdated_whenStatusIsRouted() {
        convert_orderStatusUpdated(OrderStatusUpdated.Type.ROUTED);
    }

    @Test
    void convert_orderStatusUpdated_whenStatusIsReturned() {
        convert_orderStatusUpdated(OrderStatusUpdated.Type.RETURNED);
    }

    @Test
    void convert_orderStatusUpdated_whenStatusIsReturnedInStore() {
        convert_orderStatusUpdated(OrderStatusUpdated.Type.POS_RETURNED_IN_STORE);
    }

    private void convert_orderStatusUpdated(OrderStatusUpdated.Type type) {
        // arrange
        var orderId = "*********";
        var tbOrderId = "TB".concat(orderId);
        var order = Order.builder()
            .orderId(tbOrderId)
            .lastModifiedTS(ZonedDateTime.now())
            .build();
        var orderLineId = UUID.randomUUID();

        var statusUpdatedPayload = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    OrderLine.builder()
                        .id(orderLineId)
                        .states(List.of(type.name()))
                        .build()
                )
            ).build();

        ZonedDateTime fixedNow = ZonedDateTime.now(ZoneId.of("UTC"));
        Clock fixedClock = Clock.fixed(fixedNow.toInstant(), ZoneId.of("UTC"));
        when(utcClock.instant()).thenReturn(fixedClock.instant());
        when(utcClock.getZone()).thenReturn(fixedClock.getZone());

        // act
        OrderStatusUpdated orderStatusUpdated = switch (type) {
            case ROUTING -> converter.convertTo(order, statusUpdatedPayload, OrderStatusUpdated.Type.ROUTING);
            case CANCELLED -> converter.convertTo(order, statusUpdatedPayload, OrderStatusUpdated.Type.CANCELLED);
            case ROUTED -> converter.convertTo(order, statusUpdatedPayload, OrderStatusUpdated.Type.ROUTED);
            case RETURNED -> converter.convertTo(order, statusUpdatedPayload, OrderStatusUpdated.Type.RETURNED);
            case POS_RETURNED_IN_STORE -> converter.convertTo(
                order, statusUpdatedPayload, OrderStatusUpdated.Type.POS_RETURNED_IN_STORE);
            default -> throw new IllegalArgumentException("Unexpected type: " + type);
        };

        // assert
        assertThat(orderStatusUpdated)
            .as("OrderStatusUpdated successfully converted")
            .extracting("orderId", "type")
            .containsExactlyInAnyOrder(tbOrderId, type);
        assertThat(orderStatusUpdated.getPayload()).isNotNull();
        assertThat(orderStatusUpdated.getTimestamp()).isNotNull();
    }
}
