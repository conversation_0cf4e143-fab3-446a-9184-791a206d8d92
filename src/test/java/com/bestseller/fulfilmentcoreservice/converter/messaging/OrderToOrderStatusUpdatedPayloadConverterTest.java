package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.Order;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLineQtyStatus;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import com.logistics.statetransition.OrderState;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class OrderToOrderStatusUpdatedPayloadConverterTest {

    private final OrderToOrderStatusUpdatedPayloadConverter converter = new OrderToOrderStatusUpdatedPayloadConverter();

    @ParameterizedTest
    @MethodSource("data")
    void shouldTestConverter(Order order, DefaultOrderStatusUpdatedPayload expectedPayload) {
        // act
        var payload = converter.convert(order);

        // assert
        assertThat(payload)
            .usingRecursiveComparison()
            .isEqualTo(expectedPayload);

    }

    private static Stream<Arguments> data() {
        var ecomId1 = UUID.randomUUID();
        var ecomId2 = UUID.randomUUID();
        return Stream.of(
            Arguments.of(
                Order.builder()
                    .orderLines(Collections.emptyList())
                    .build(),
                DefaultOrderStatusUpdatedPayload.builder().orderLines(Collections.emptyList()).build()
            ),
            Arguments.of(
                Order.builder()
                    .orderLines(List.of(
                        OrderLine.builder()
                            .ecomId(ecomId1)
                            .orderLineQtyStatus(Collections.emptyList())
                            .build()
                    ))
                    .build(),
                DefaultOrderStatusUpdatedPayload.builder()
                    .orderLines(Collections.emptyList())
                    .build()
            ),
            Arguments.of(
                Order.builder()
                    .orderLines(List.of(
                        OrderLine.builder()
                            .ecomId(ecomId1)
                            .changed(true)
                            .orderLineQtyStatus(
                                List.of(
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.PLACED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.PLACED)
                                        .build()
                                )
                            )
                            .build(),
                        OrderLine.builder()
                            .ecomId(ecomId2)
                            .changed(true)
                            .orderLineQtyStatus(
                                List.of(
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build()
                                )
                            )
                            .build()
                    ))
                    .build(),
                DefaultOrderStatusUpdatedPayload.builder()
                    .orderLines(
                        List.of(
                            com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                                .id(ecomId1)
                                .states(List.of("PLACED", "EXPORTED", "PLACED"))
                                .build(),
                            com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                                .id(ecomId2)
                                .states(List.of("EXPORTED", "EXPORTED"))
                                .build()
                        )
                    )
                    .build()
            ),

            Arguments.of(
                Order.builder()
                    .orderLines(List.of(
                        OrderLine.builder()
                            .ecomId(ecomId1)
                            .changed(true)
                            .orderLineQtyStatus(
                                List.of(
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.PLACED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.PLACED)
                                        .build()
                                )
                            )
                            .build(),
                        OrderLine.builder()
                            .ecomId(ecomId2)
                            .orderLineQtyStatus(
                                List.of(
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build(),
                                    OrderLineQtyStatus.builder()
                                        .orderState(OrderState.EXPORTED)
                                        .build()
                                )
                            )
                            .build()
                    ))
                    .build(),
                DefaultOrderStatusUpdatedPayload.builder()
                    .orderLines(
                        List.of(
                            com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                                .id(ecomId1)
                                .states(List.of("PLACED", "EXPORTED", "PLACED"))
                                .build()
                        )
                    )
                    .build()
            )
        );
    }
}
