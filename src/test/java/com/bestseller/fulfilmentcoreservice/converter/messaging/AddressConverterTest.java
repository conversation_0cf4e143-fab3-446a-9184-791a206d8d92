package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.EntityType;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class AddressConverterTest {

    private static final String CARRIER_VARIANT = "HOME";
    private static final String ADDRESS1 = "a street";
    private static final String ADDRESS2 = "line 2";
    private static final String ADDRESS3 = "Line 3";
    private static final String CITY = "Amsterdam";
    private static final String COUNTRY = "Netherlands";
    private static final String FIRST_NAME = "John";
    private static final String HOUSE_NUMBER = "5";
    private static final String HOUSE_NUMBER_EXT = "second floor";
    private static final String LAST_NAME = "Doe";
    private static final String PHONE_NUMBER = "1234";
    private static final String SALUTATION = "Hello";
    private static final String STATE = "Noord-Holland";
    private static final String ZIP_CODE = "1060AB";
    private static final String DHL = "DHL";
    private static final String PHYSICAL_STORE_ID = "1221";
    private static final String POSTAL_CODE = "1448 PL";

    private final AddressConverter addressConverter = Mappers.getMapper(AddressConverter.class);

    @Test
    void convert_billingAddress_returnsBillingAddress() {
        // arrange
        var address = getAddress();
        var customerInfo = new CustomerInformation();
        customerInfo.setBillingAddress(address);

        String carrierVariant = "HOME";
        var orderDetails = new OrderDetails();
        orderDetails.setCarrierVariant(carrierVariant);

        var message = new ValidOrderPlaced();
        message.setCustomerInformation(customerInfo);
        message.setOrderDetails(orderDetails);

        // act
        var result = addressConverter.toBillingAddress(message);

        // assert
        assertThat(result)
            .extracting("address1", "address2", "address3", "city", "countryCode", "firstName",
                "houseNumber", "houseNumberExt", "lastName")
            .containsExactly(ADDRESS1, ADDRESS2, ADDRESS3, CITY, COUNTRY, FIRST_NAME,
                HOUSE_NUMBER, HOUSE_NUMBER_EXT, LAST_NAME);

    }

    @Test
    void convert_givenEmptyHouseNumber_extractNothing() {
        // arrange
        var addressLine = "MarktbreiterStr";
        var billingAddress = getAddress();
        billingAddress.setHouseNumber(null);
        billingAddress.setAddressLine1(addressLine);
        var customerInfo = new CustomerInformation();
        customerInfo.setBillingAddress(billingAddress);

        var shippingAddress = getAddress();
        shippingAddress.setHouseNumber(null);
        shippingAddress.setAddressLine1(addressLine);

        var shippingInformation = new ShippingInformation();
        shippingInformation.setShippingAddress(shippingAddress);


        var orderDetails = new OrderDetails();
        orderDetails.setCarrierVariant(CARRIER_VARIANT);

        var message = new ValidOrderPlaced();
        message.setCustomerInformation(customerInfo);
        message.setOrderDetails(orderDetails);
        message.setShippingInformation(shippingInformation);

        // act
        var billingResult = addressConverter.toBillingAddress(message);
        var shippingResult = addressConverter.toShippingAddress(message);

        // assert
        assertThat(billingResult)
            .extracting("houseNumber")
            .isNull();

        assertThat(shippingResult)
            .extracting("houseNumber")
            .isNull();
    }

    @ParameterizedTest
    @MethodSource("houseNumberProvider")
    void convert_givenEmptyHouseNumber_extractHouseNumberFromAddress(String addressLine, String expectedHouseNumber) {
        // arrange
        var billingAddress = getAddress();
        billingAddress.setHouseNumber(null);
        billingAddress.setAddressLine1(addressLine);
        var customerInfo = new CustomerInformation();
        customerInfo.setBillingAddress(billingAddress);

        var shippingAddress = getAddress();
        shippingAddress.setHouseNumber(null);
        shippingAddress.setAddressLine1(addressLine);

        var shippingInformation = new ShippingInformation();
        shippingInformation.setShippingAddress(shippingAddress);


        var orderDetails = new OrderDetails();
        orderDetails.setCarrierVariant(CARRIER_VARIANT);

        var message = new ValidOrderPlaced();
        message.setCustomerInformation(customerInfo);
        message.setOrderDetails(orderDetails);
        message.setShippingInformation(shippingInformation);

        // act
        var billingResult = addressConverter.toBillingAddress(message);
        var shippingResult = addressConverter.toShippingAddress(message);

        // assert
        assertThat(billingResult)
            .extracting("houseNumber")
            .isEqualTo(expectedHouseNumber);

        assertThat(shippingResult)
            .extracting("houseNumber")
            .isEqualTo(expectedHouseNumber);
    }

    @ParameterizedTest
    @MethodSource("addressLinesProvider")
    void convert_givenEmptyHouseNumber_extractAddressLine1FromAddress(String addressLine, String expectedAddressLine) {
        // arrange
        var billingAddress = getAddress();
        billingAddress.setHouseNumber(null);
        billingAddress.setAddressLine1(addressLine);
        var customerInfo = new CustomerInformation();
        customerInfo.setBillingAddress(billingAddress);

        var shippingAddress = getAddress();
        shippingAddress.setHouseNumber(null);
        shippingAddress.setAddressLine1(addressLine);

        var shippingInformation = new ShippingInformation();
        shippingInformation.setShippingAddress(shippingAddress);


        var orderDetails = new OrderDetails();
        orderDetails.setCarrierVariant(CARRIER_VARIANT);

        var message = new ValidOrderPlaced();
        message.setCustomerInformation(customerInfo);
        message.setOrderDetails(orderDetails);
        message.setShippingInformation(shippingInformation);

        // act
        var billingResult = addressConverter.toBillingAddress(message);
        var shippingResult = addressConverter.toShippingAddress(message);

        // assert
        assertThat(billingResult)
            .extracting("address1")
            .isEqualTo(expectedAddressLine);

        assertThat(shippingResult)
            .extracting("address1")
            .isEqualTo(expectedAddressLine);
    }

    @Test
    void convert_shippingAddress_returnsShippingAddress() {
        // arrange
        var address = getAddress();

        var orderDetails = new OrderDetails()
            .withCarrierVariant(CARRIER_VARIANT)
            .withCarrier(DHL)
            .withPhysicalStoreId(PHYSICAL_STORE_ID);


        var message = new ValidOrderPlaced();
        message.setOrderDetails(orderDetails);
        message.setShippingInformation(new ShippingInformation().withShippingAddress(address));

        // act
        var result = addressConverter.toShippingAddress(message);

        // assert
        assertThat(result)
            .extracting("address1", "address2", "address3", "city", "countryCode", "firstName",
                "houseNumber", "houseNumberExt", "lastName", "physicalStoreId",
                "carrierVariant", "deliveryOption")
            .containsExactly(ADDRESS1, ADDRESS2, ADDRESS3, CITY, COUNTRY, FIRST_NAME,
                HOUSE_NUMBER, HOUSE_NUMBER_EXT, LAST_NAME, PHYSICAL_STORE_ID, CARRIER_VARIANT,
                DHL);

    }

    @Test
    void getPostCode_givenNLCountry_returnsThePostCodeCorrected() {
        // arrange
        Address address = new Address()
            .withCountry("NL")
            .withZipcode(POSTAL_CODE);
        // act
        assertThat(addressConverter.getPostCode(address))
            .as("Post code corrected")
            .isEqualTo(POSTAL_CODE);
    }

    @Test
    void getPostCode_givenPLCountry_returnsThePostCodeCorrected() {
        // arrange
        Address address = new Address()
            .withCountry("PL")
            .withZipcode(POSTAL_CODE);
        // act
        assertThat(addressConverter.getPostCode(address))
            .as("Post code not corrected")
            .isEqualTo(POSTAL_CODE);
    }

    @Test
    void toAdditionalInformationModels_givenAdditionalInformation_returnsAdditionalInfoForShippingInfo() {
        // arrange
        Set<AdditionalInformation> additionalInformation = new HashSet<>();
        additionalInformation.add(new AdditionalInformation().withKey("key1").withValue("value1"));
        // act
        var result = addressConverter.toAdditionalInformationModels(additionalInformation);
        // assert
        assertThat(result.size())
            .as("Has the same size")
            .isEqualTo(additionalInformation.size());
        additionalInformation
            .forEach(ai -> {
                var item = result.stream()
                    .filter(r -> r.getKey().equals(ai.getKey()))
                    .findFirst()
                    .orElseThrow();
                assertThat(item)
                    .as("Contains the additional information")
                    .extracting("key", "value", "entityType")
                    .containsExactly(ai.getKey(), ai.getValue(), EntityType.SHIPPING_INFORMATION);
            });
    }

    @NotNull
    private static Address getAddress() {
        var address = new Address();
        address.setAddressLine1(ADDRESS1);
        address.setAddressLine2(ADDRESS2);
        address.setAddressLine3(ADDRESS3);
        address.setCity(CITY);
        address.setCountry(COUNTRY);
        address.setFirstName(FIRST_NAME);
        address.setHouseNumber(HOUSE_NUMBER);
        address.setHouseNumberExtended(HOUSE_NUMBER_EXT);
        address.setLastName(LAST_NAME);
        address.setPhoneNumber(PHONE_NUMBER);
        address.setSalutation(SALUTATION);
        address.setState(STATE);
        address.setZipcode(ZIP_CODE);
        return address;
    }

    private static Stream<Arguments> houseNumberProvider() {
        return Stream.of(
            Arguments.of("Marktbreiter Str. 54", "54"),
            Arguments.of("Marktbreiter Str.54", "54"),
            Arguments.of("Marktbreiter Str 54", "54"),
            Arguments.of("Marktbreiter Str. 54 a", "54 a"),
            Arguments.of("Landsberger Allee 26-32", "26-32"),
            Arguments.of("26-32 Landsberger Allee ",
                null)//the case that we can't extract the right house number so we skip this case
        );
    }

    private static Stream<Arguments> addressLinesProvider() {
        return Stream.of(Arguments.of("Marktbreiter Str. 54", "Marktbreiter Str"),
            Arguments.of("Marktbreiter Str.54", "Marktbreiter Str"),
            Arguments.of("Marktbreiter Str 54", "Marktbreiter Str"),
            Arguments.of("Marktbreiter Str. 54 a", "Marktbreiter Str"),
            Arguments.of("Landsberger Allee 26-32", "Landsberger Allee"),
            Arguments.of("26-32 Landsberger Allee ", "26-32 Landsberger Allee ")
        );
    }

}
