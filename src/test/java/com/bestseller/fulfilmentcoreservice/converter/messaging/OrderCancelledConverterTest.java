package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderCancelledConverterTest {

    @InjectMocks
    private OrderCancelledConverterImpl converter;

    @Test
    void convert_order_toOrderCancelled() {
        // arrange
        var tbOrderId = "TB".concat("612678123");
        var order = OrderGenerator.createOrder(tbOrderId);

        // act
        var orderCancelled = converter.toCancelled(order);

        // assert
        assertThat(orderCancelled)
            .as("OrderCancelled successfully converted")
            .extracting("orderId", "warehouse")
            .containsExactlyInAnyOrder(tbOrderId, "HERMES");

        assertThat(orderCancelled.getOrderLines())
            .extracting("ean", "quantity")
            .containsExactlyInAnyOrder(Tuple.tuple("5715423786800", 2));
    }
}
