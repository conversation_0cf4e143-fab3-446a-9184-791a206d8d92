package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.interfacecontractannotator.model.orderstatusupdated.DefaultOrderStatusUpdatedPayload;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class OrderDispatchedStatusUpdatedPayloadConverterTest {

    private final OrderDispatchedStatusUpdatedPayloadConverter builder =
        new OrderDispatchedStatusUpdatedPayloadConverter();

    @Test
    void testConvert() {
        // arrange
        var orderLine = OrderLine.builder()
            .ecomId(UUID.randomUUID())
            .ean(UUID.randomUUID().toString())
            .changed(true)
            .orderLineQtyStatus(Collections.emptyList())
            .build();

        var orderLineDispatched = new OrderLineDispatched();
        orderLineDispatched.setEan(orderLine.getEan());
        orderLineDispatched.setTrackingNumber(UUID.randomUUID().toString());
        orderLineDispatched.setReturnShipmentId(UUID.randomUUID().toString());

        // act
        var actual = builder.convert(List.of(orderLine), orderLineDispatched);

        // assert
        var expected = DefaultOrderStatusUpdatedPayload.builder()
            .orderLines(
                List.of(
                    com.bestseller.interfacecontractannotator.model.orderstatusupdated.OrderLine.builder()
                        .id(orderLine.getEcomId())
                        .states(Collections.emptyList())
                        .trackingNumber(orderLineDispatched.getTrackingNumber())
                        .returnShipmentId(orderLineDispatched.getReturnShipmentId())
                        .build()
                )
            )
            .build();
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }
}
