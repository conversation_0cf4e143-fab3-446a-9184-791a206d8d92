package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.EntityType;
import com.bestseller.fulfilmentcoreservice.core.model.OrderModel;
import com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.logistics.statetransition.Platform;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

import static com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator.ANNOUNCED_DELIVERY_DATE;
import static com.bestseller.fulfilmentcoreservice.core.utils.ValidOrderPlacedGenerator.IS_TEST;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedConverterTest {

    @InjectMocks
    private ValidOrderPlacedConverterImpl converter;

    @Mock
    private AddressConverter addressConverter;

    @Mock
    private CustomerConverter customerConverter;

    @Mock
    private OrderLineConverter orderLineConverter;

    @Test
    void convert_validOrderPlaced_returnsOrderModel() {
        // arrange
        var orderId = "612678123";
        var tbOrderId = "TB".concat(orderId);
        var message = ValidOrderPlacedGenerator.generateTradebyteOrder(tbOrderId);

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel)
            .as("ValidOrderPlaced successfully converted")
            .extracting("orderId", "orderDate", "checkoutScopeId", "platform",
                "channel", "shippingMethod", "externalOrderId", "brandedShipping", "billToMatchesShipTo",
                "market", "test", "orderValue", "isoStoreId", "offlinePayment")
            .containsExactlyInAnyOrder(tbOrderId, ZonedDateTime.parse("2023-10-11T07:57:12Z"), "ys",
                Platform.TRADEBYTE.name(), ValidOrderPlacedGenerator.CHANNEL_TRADEBYTE, "STANDARD",
                orderId, "YS", false, "BSE-NL", IS_TEST, ValidOrderPlacedGenerator.ORDER_VALUE,
                ValidOrderPlacedGenerator.ISO_STORE_ID, true);

        verify(addressConverter).toShippingAddress(message);
        verify(addressConverter).toBillingAddress(message);
        verify(customerConverter).toCustomerModel(message);
        verify(orderLineConverter).toOrderLines(message);
    }

    @Test
    void convert_validOrderPlacedWithDefaultShippingMethodAndDemandwareOrder_returnsOrderModel() {
        // arrange
        var orderId = "612678135";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        message.getOrderDetails().setShippingMethod(null);

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel)
            .as("ValidOrderPlaced successfully converted")
            .extracting("orderDate", "checkoutScopeId", "platform",
                "channel", "shippingMethod", "externalOrderId", "brandedShipping", "billToMatchesShipTo",
                "market", "test", "announcedDeliveryDate")
            .containsExactlyInAnyOrder(ZonedDateTime.parse("2023-10-11T07:57:12Z"), "ys",
                Platform.DEMANDWARE.name(), "storefront", "STANDARD", null, "YS", false, "BSE-NL", IS_TEST,
                ANNOUNCED_DELIVERY_DATE);

        verify(addressConverter).toShippingAddress(message);
        verify(addressConverter).toBillingAddress(message);
        verify(customerConverter).toCustomerModel(message);
        verify(orderLineConverter).toOrderLines(message);
    }

    @Test
    public void toAdditionalInformationModels_givenNullAdditionalInformation_returnEmptyAdditionalInfo() {
        // arrange
        var orderId = "612678135";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        message.getOrderDetails().setShippingMethod(null);
        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel.getAdditionalOrderInformation().isEmpty()).isTrue();
    }

    @Test
    public void toAdditionalInformationModels_givenNullShippingInformation_whenShippingMethodVirtual() {
        // arrange
        var orderId = "612678135";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        message.getOrderDetails().setShippingMethod(null);

        Set<AdditionalInformation> additionalInformation1 = new HashSet<>();
        additionalInformation1.add(new AdditionalInformation().withKey("key1").withValue("value1"));
        message.getShippingInformation().setAdditionalInformation(additionalInformation1);

        message.setShippingInformation(null);
        message.getOrderDetails().setAdditionalInformation(additionalInformation1);
        message.getOrderDetails().setShippingMethod("VIRTUAL");

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel.getShippingAddress()).isNull();
        assertThat(orderModel.getAdditionalOrderInformation())
            .as("ShippingAdditionalInfo successfully converted")
            .extracting("key", "value", "entityType")
            .containsExactlyInAnyOrder(Tuple.tuple("key1", "value1", EntityType.ORDER_DETAILS));
    }

    @Test
    public void toAdditionalInformationModels_givenNullAdditionalInformation_returnsAdditionalInfo() {
        // arrange
        var orderId = "612678135";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        message.getOrderDetails().setShippingMethod(null);

        Set<AdditionalInformation> additionalInformation1 = new HashSet<>();
        additionalInformation1.add(new AdditionalInformation().withKey("key1").withValue("value1"));
        message.getShippingInformation().setAdditionalInformation(additionalInformation1);

        Set<AdditionalInformation> additionalInformation2 = new HashSet<>();
        additionalInformation2.add(new AdditionalInformation().withKey("key2").withValue("value2"));
        message.getOrderDetails().setAdditionalInformation(additionalInformation2);

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel.getAdditionalOrderInformation())
            .as("ShippingAdditionalInfo successfully converted")
            .extracting("key", "value", "entityType")
            .containsExactlyInAnyOrder(Tuple.tuple("key1", "value1", EntityType.SHIPPING_INFORMATION),
                Tuple.tuple("key2", "value2", EntityType.ORDER_DETAILS));
    }

    @Test
    void convert_validOrderPlaced_shouldMapCurrency_fromOrderDetails() {
        // arrange
        var orderId = "orderId";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);
        message.getOrderDetails().setCurrency("DKK");

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel.getCurrency()).isEqualTo("DKK");
    }

    @Test
    void convert_validOrderPlaced_shouldMapCurrency_fromPayment() {
        // arrange
        var orderId = "orderId";
        var message = ValidOrderPlacedGenerator.generateDemandwareOrder(orderId);

        // act
        OrderModel orderModel = converter.toOrderModel(message);

        // assert
        assertThat(orderModel.getCurrency()).isEqualTo("EUR");
    }
}
