package com.bestseller.fulfilmentcoreservice.converter.messaging;

import com.bestseller.fulfilmentcoreservice.core.model.OrderForFulfilmentModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderLineModel;
import com.bestseller.fulfilmentcoreservice.core.model.OrderType;
import com.bestseller.fulfilmentcoreservice.core.utils.OrderGenerator;
import com.bestseller.fulfilmentcoreservice.core.utils.PaymentStatusUpdatedGenerator;
import com.bestseller.fulfilmentcoreservice.persistence.entity.OrderLine;
import com.bestseller.fulfilmentcoreservice.persistence.enums.EntityType;
import com.logistics.statetransition.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderForFulfilmentConverterTest {

    private static final Double ORDER_VALUE = 399.95;

    @InjectMocks
    private OrderForFulfilmentConverterImpl converter;

    @Test
    void convert_paymentStatusUpdated_orderForFulfilment() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);

        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        assertThat(orderForFulfilmentModel)
            .as("OrderForFulfilment successfully converted")
            .extracting("orderId", "isTest", "channel", "marketPlace")
            .containsExactlyInAnyOrder(tbOrderId, false, "storefront", "3061");

        assertThat(orderForFulfilmentModel.getCustomerInformation())
            .as("CustomerInformation successfully converted")
            .extracting("email", "customerId", "externalCustomerNumber")
            .containsExactlyInAnyOrder("<EMAIL>", "00186577", null);

        assertThat(orderForFulfilmentModel.getShippingInformation().getShippingAddress())
            .as("ShippingInformation successfully converted")
            .extracting("address1", "city", "countryCode", "phone", "postCode")
            .containsExactlyInAnyOrder("Frögatan", "Solna", "NL", "+31712474580", "3061 GG");

        assertThat(orderForFulfilmentModel.getCustomerInformation().getBillingAddress())
            .as("BillingAddress successfully converted")
            .extracting("address1", "city", "countryCode", "phone", "postCode")
            .containsExactlyInAnyOrder("Frögatan", "Solna", "NL", "+31712474580", "3061 GG");

        assertThat(orderForFulfilmentModel.getOrderDetails())
            .as("OrderDetails successfully converted")
            .extracting("carrier", "carrierVariant", "shippingMethod",
                "externalOrderNumber", "orderValue", "orderType", "currency", "checkout", "isoStoreId")
            .containsExactlyInAnyOrder("DHL", "HOME", "MARKETPLACE", "*********",
                null, "STANDARD", "EUR", "ys", "ISO Store ID");

        assertThat(orderForFulfilmentModel.getPayments().get(0))
            .as("OrderForFulfilment successfully converted")
            .extracting("name", "subMethod", "amount")
            .containsExactlyInAnyOrder("ADYEN_CARD", "ADYEN_CREDIT_CARD", BigDecimal.valueOf(ORDER_VALUE));

        assertThat(orderForFulfilmentModel.getOrderLines().get(0))
            .as("OrderForFulfilment successfully converted")
            .extracting("ean", "lineNumber")
            .containsExactlyInAnyOrder("*************", 1);

        assertThat(orderForFulfilmentModel.getPlatform()).isEqualTo("platform");

        assertThat(orderForFulfilmentModel.getFulfillmentAdvice())
            .as("ForFulfilmentAdvice successfully converted")
            .extracting("fulfillmentNode", "holdFromRouting")
            .containsExactly("fulfilmentNode", true);

        order.getAdditionalOrderInformation()
            .stream()
            .filter(ai -> EntityType.SHIPPING_INFORMATION.equals(ai.getEntityType()))
            .forEach(additionalInformation -> {
                var messageAdditionalInformation = orderForFulfilmentModel
                    .getAdditionalOrderInformation()
                    .stream()
                    .filter(ai -> ai.getKey().equals(additionalInformation.getKey()))
                    .filter(ai -> ai.getValue().equals(additionalInformation.getValue()))
                    .findAny();
                assert messageAdditionalInformation.isPresent();
            });
    }

    @Test
    void convert_paymentStatusUpdated_userDeviceInfoIsNull() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.setUserDeviceInfo(null);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        assertThat(orderForFulfilmentModel.getPlatform()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_platformIsBlank() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.getUserDeviceInfo().setPlatform("");
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        assertThat(orderForFulfilmentModel.getPlatform()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_fulfillmentNodeIsNull() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.getOrderLines().get(0)
            .getOrderLineQtyStatus().get(0)
            .getOrderFulfillmentPart().setFulfillmentNode(null);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        assertThat(orderForFulfilmentModel.getFulfillmentAdvice()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_orderLineQtyStatusIsEmpty() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.getOrderLines().get(0).setOrderLineQtyStatus(List.of());
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        assertThat(orderForFulfilmentModel.getFulfillmentAdvice()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_paymentOrderDetailsIsEmpty() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        message.setOrderDetails(null);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);
        converter.completeOrderDetails(message, orderForFulfilmentModel);

        // assert
        assertThat(orderForFulfilmentModel.getOrderDetails().getOrderValue()).isNull();
        assertThat(orderForFulfilmentModel.getOrderDetails().getShippingFeesTaxPercentage()).isNull();
        assertThat(orderForFulfilmentModel.getOrderDetails().getShippingFeesCancelled()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_setupPaymentOrderLines() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.getOrderLines().get(0).setEan("1234567890123");
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);
        converter.completeOrderForFulfilmentModel(message, orderForFulfilmentModel);

        // assert
        var paymentOrderLine = message.getOrderLines().get(0);
        var orderForFulfilmentOrderLine = orderForFulfilmentModel.getOrderLines().get(0);
        assertThat(orderForFulfilmentOrderLine.getDiscountValue()).isEqualTo(paymentOrderLine.getDiscountValue());
        assertThat(orderForFulfilmentOrderLine.getRetailPrice()).isEqualTo(paymentOrderLine.getRetailPrice());
        assertThat(orderForFulfilmentOrderLine.getTaxPercentage()).isEqualTo(paymentOrderLine.getTaxPercentage());
    }

    @Test
    void convert_paymentStatusUpdated_setPlatformIfIsTradeByte() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);
        converter.completeOrderForFulfilmentModel(message, orderForFulfilmentModel);

        // assert
        assertThat(orderForFulfilmentModel.getOrderDetails().getOrderType())
            .isEqualTo(OrderType.MARKETPLACE.name());
    }

    @Test
    void convert_paymentStatusUpdated_setPlatformIfIsDemandWare() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.setPlatform(Platform.DEMANDWARE);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);
        converter.completeOrderForFulfilmentModel(message, orderForFulfilmentModel);

        // assert
        assertThat(orderForFulfilmentModel.getOrderDetails().getOrderType())
            .isEqualTo(OrderType.STANDARD.name());
    }

    @Test
    void convert_paymentStatusUpdated_setPlatformIfIsNull() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        order.setPlatform(null);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);
        converter.completeOrderForFulfilmentModel(message, orderForFulfilmentModel);

        // assert
        assertThat(orderForFulfilmentModel.getOrderDetails().getOrderType()).isNull();
    }

    @Test
    void convert_paymentStatusUpdated_filterVirtualProducts() {
        // arrange
        var tbOrderId = "TB".concat("*********");
        var order = OrderGenerator.createOrder(tbOrderId);
        var orderLines = new ArrayList<>(order.getOrderLines());
        orderLines.add(OrderLine.builder()
            .ean("1234567890123")
            .lineNumber(2)
            .virtualProduct(true)
            .build());
        order.setOrderLines(orderLines);
        var message = PaymentStatusUpdatedGenerator.toPaymentStatusUpdatedModel(tbOrderId);
        var messageOrderLines = new ArrayList<>(message.getOrderLines());
        messageOrderLines.add(OrderLineModel.builder()
            .ean("1234567890123")
            .lineNumber(2)
            .virtualProduct(true)
            .build());
        message.setOrderLines(messageOrderLines);
        // act
        OrderForFulfilmentModel orderForFulfilmentModel = converter.toOrderForFulfilmentModel(order, message);

        // assert
        order.getOrderLines().forEach(orderLine -> {
            boolean isVirtualProduct = orderLine.isVirtualProduct();
            var orderForFulfilmentOrderLine = orderForFulfilmentModel.getOrderLines().stream()
                .filter(ofl -> ofl.getEan().equals(orderLine.getEan()))
                .findAny();
            if (isVirtualProduct) {
                assertThat(orderForFulfilmentOrderLine).isEmpty();
            } else {
                assertThat(orderForFulfilmentOrderLine).isPresent();
            }
        });
    }
}
