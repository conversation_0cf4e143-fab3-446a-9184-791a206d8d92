package com.bestseller.fulfilmentcoreservice.persistence.enums;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

import java.util.stream.Stream;

/**
 * This enum represents the state of an order.
 */
@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
public enum OrderState {

    WEIRD_STATE(-1),
    BEGIN(0),
    DRAFT(500),
    BLOCKED(750),

    PLACED(1000),
    SUPPLIER_PLACED(1100),
    SUPPLIER_RECEIVED(1150),

    ROUTING(1160),
    ROUTED(1170),
    RESUBMIT(1200),

    EXPORTED(1400),
    PENDING(1500),
    PROCESSING(1600),
    DISPATCHED(3000),

    RETURNED(5000),
    POS_RETURNED_IN_STORE(5100),
    RETURN_PROCESSED(5600),

    CANCELLED(7000),
    CANCELLATION_REFUND_MIXED_CAPTURE(7200),
    CANCELLATION_REFUND(7100),
    RETURNED_IN_STORE(5500),
    PURGE(9000);

    private final int identifier;

    OrderState(Integer identifier) {
        this.identifier = identifier;
    }

    public int getIdentifier() {
        return identifier;
    }

    /**
     * Find an Order State given an identifier.
     *
     * @return orderState
     */
    public static OrderState fromIdentifier(Integer identifier) {
        if (identifier == null) {
            return null;
        }

        return Stream.of(values())
            .filter(state -> state.getIdentifier() == identifier)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown identifier = " + identifier));
    }

    public boolean isGreaterThanOrEquals(OrderState other) {
        return compareTo(other) >= 0;
    }

    public boolean isLessThanOrEquals(OrderState other) {
        return compareTo(other) <= 0;
    }

    public boolean isGreaterThan(OrderState other) {
        return compareTo(other) > 0;
    }

    public boolean isLessThan(OrderState other) {
        return compareTo(other) < 0;
    }
}
