# fulfilment-core-service

Git repository of project Fulfilment Core Service (FCS) Owner: LOGISTICS

## How to start application on a local machine:

1. Activate 'local' spring profile in application.yaml config: "spring.profiles.active: local"
2. Set up manually default DB url in application.yaml:
   url: ${OMS_DB_URL:*************************************************}
3. Set up manually default username and password in DB configuration in application.yaml instead of xxxx values:
   username: ${OMS_DB_USERNAME:xxxx}
   password: ${OMS_DB_PASSWORD:xxxx}

## Hashing passwords:

Given the password "myPassword" the following command will generate the hashed password:
> ./bcryptPassword.sh "myPassword"

Read the output from the terminal
"Hashing password: "{bcrypt}$2a$05$QX7UNTwtrhRJKYHXMiU6HOUC9amVQlqWuv0uhJ7PD8qMSRdFjtfL6""

Use the hashed password in the application.yaml file: "
{bcrypt}$2a$05$QX7UNTwtrhRJKYHXMiU6HOUC9amVQlqWuv0uhJ7PD8qMSRdFjtfL6"

## Application profiles:

| Profile | Description                   |
|---------|-------------------------------|
| local   | Integration test / Unit tests |
| dev     | Development purposes          |
| acc     | Acceptance purposes           |
| prod    | Production purposes           |

## Devspace (Optional):
[Deployment and development environment on K8s](https://bestseller.jira.com/wiki/spaces/BLD/pages/4134371333/Local+deployment+and+development+for+microservices)