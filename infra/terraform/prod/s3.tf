data "aws_lambda_function" "order_file_bundle_generator" {
  function_name = "logistic_lambdas-file-bundle-gen-indexing-${local.env}"
}

module "archive_bucket" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/s3-bucket/aws"
  version = "1.2.1"

  env         = local.env
  project     = "oms-archive"
  owner       = local.owner
  vcs         = local.vcs
  custom_name = "bse-oms-archive-${local.env}.bseint.io"
  s3_events = [
    {
      lambda_function_arn = data.aws_lambda_function.order_file_bundle_generator.arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "sfcc-order/"
      filter_suffix       = ""
      id                  = "S3-COMMERCE_CLOUD-ORDER-TRIGGER"

    },
    {
      lambda_function_arn = data.aws_lambda_function.order_file_bundle_generator.arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "sfcc-order-status/"
      filter_suffix       = ""
      id                  = "S3-COMMERCE_CLOUD-ORDER_STATUS-TRIGGER"
    },
    {
      lambda_function_arn = data.aws_lambda_function.order_file_bundle_generator.arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "tb-order-status/"
      filter_suffix       = ""
      id                  = "S3-TRADEBYTE-ORDER_STATUS-TRIGGER"
    }
  ]
}
