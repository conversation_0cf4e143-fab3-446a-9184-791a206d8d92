module "standard_monitors" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/standard-monitors/datadog"
  version = "~> 4.4.1"

  owner                 = local.owner
  project               = local.project
  env                   = local.env
  vcs                   = local.vcs
  jira_integration_name = "ets-fulfilment-incident"

  kafka_consumer_lag_configs = [
    {
      topic_name         = "ValidOrderPlaced"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentStatusUpdated"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderPartsRouted"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderLineExported"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderLineAcknowledged"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderLineDispatched"
      threshold_warning  = 5000
      threshold_critical = 10000
    },
    {
      topic_name         = "OrderLineReturned"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderReturnedInStore"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderPartsCancelled"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "GiftCardPurchased"
      threshold_warning  = 250
      threshold_critical = 500
    }
  ]

  no_kafka_message_produced_configs = [
    {
      topic_name = "OrderStatusUpdated",
      interval   = "1h"
    },
    {
      topic_name = "OrderForFulfillment",
      interval   = "1h"
    },
    {
      topic_name = "OrderStatusUpdateTradebyte",
      interval   = "1d"
    },
    {
      topic_name = "OrderReturned",
      interval   = "1d"
    },
    {
      topic_name = "RefundCreationRequested",
      interval   = "1d"
    },
    {
      topic_name = "OrderCancelled",
      interval   = "2d"
    },
    {
      topic_name = "RefundRequested",
      interval   = "1d"
    },
    {
      topic_name = "OrderFinalized",
      interval   = "6h"
    }
  ]

  no_kafka_message_consumed_configs = [
    {
      topic_name = "ValidOrderPlaced",
      interval   = "1h"
    },
    {
      topic_name = "PaymentStatusUpdated",
      interval   = "1h"
    },
    {
      topic_name = "OrderPartsRouted",
      interval   = "1h"
    },
    {
      topic_name = "OrderLineExported",
      interval   = "1h"
    },
    {
      topic_name = "OrderLineAcknowledged",
      interval   = "1d"
    },
    {
      topic_name = "OrderLineDispatched",
      interval   = "12h"
    },
    {
      topic_name = "OrderLineReturned",
      interval   = "1d"
    },
    {
      topic_name = "OrderReturnedInStore",
      interval   = "1d"
    },
    {
      topic_name = "OrderPartsCancelled",
      interval   = "1d"
    },
    {
      topic_name = "GiftCardPurchased",
      interval   = "1d"
    }
  ]
}

locals {

  jira_integration_name  = "ets-fulfilment-incident"
  critical_jira_priority = "P2"
  major_jira_priority    = "P3"
  notifications_p2_p3    = <<-EOT
      {{#is_alert}} {{override_priority '${local.critical_jira_priority}'}} @jira-${local.jira_integration_name} {{/is_alert}}
      {{#is_warning}} {{override_priority '${local.major_jira_priority}'}} @jira-${local.jira_integration_name} {{/is_warning}}
      {{#is_recovery}} The status is NORMAL. The current value is {{value}}. @jira-${local.jira_integration_name} {{/is_recovery}}
    EOT

  no_order_status_changed_configs = [
    {
      order_status = "ROUTING",
      interval     = "1h"
    },
    {
      order_status = "ROUTED",
      interval     = "1h"
    },
    {
      order_status = "RETURNED",
      interval     = "1d"
    },
    {
      order_status = "PROCESSING",
      interval     = "5h"
    },
    {
      order_status = "PLACED",
      interval     = "1h"
    },
    {
      order_status = "EXPORTED",
      interval     = "1h"
    },
    {
      order_status = "DISPATCHED",
      interval     = "12h"
    },
    {
      order_status = "CANCELLED",
      interval     = "1d"
    }
  ]

  too_much_order_status_changed_configs = [
    {
      order_status = "BLOCKED",
      interval     = "1d",
      threshold    = 5
    }
  ]
}

module "no_order_status_changed_monitor" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version = "~> 3.1.1"

  for_each = { for config in local.no_order_status_changed_configs : config.order_status => config }

  name    = "no-order-status-changed-${lower(each.value.order_status)}"
  vcs     = local.vcs
  env     = local.env
  owner   = local.owner
  project = local.project

  query = <<-EOT
    sum(last_${lower(each.value.interval)}):avg:order.status.changing.total{env:${local.env},service:${local.project},status:${lower(each.value.order_status)}}.as_count() <= 0
  EOT

  threshold_warning  = null
  threshold_critical = 0
  threshold_ok       = 1
  critical_recovery  = null
  warning_recovery   = null

  message = "No ${each.value.order_status} order status changed for last ${each.value.interval} ${local.notifications_p2_p3}"
}

module "too_much_order_status_changed_monitor" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/monitor-metric-alert/datadog"
  version = "~> 3.1.1"

  for_each = { for config in local.too_much_order_status_changed_configs : config.order_status => config }

  name    = "too-much-order-status-changed-${lower(each.value.order_status)}"
  vcs     = local.vcs
  env     = local.env
  owner   = local.owner
  project = local.project

  query = <<-EOT
    sum(last_${lower(each.value.interval)}):avg:order.status.changing.total{env:${local.env},service:${local.project},status:${lower(each.value.order_status)}}.as_count() > ${each.value.threshold}
  EOT

  threshold_warning  = null
  threshold_critical = each.value.threshold
  threshold_ok       = each.value.threshold
  critical_recovery  = null
  warning_recovery   = null

  message = "More than ${each.value.threshold} ${each.value.order_status} order status changed for last ${each.value.interval} ${local.notifications_p2_p3}"
}
