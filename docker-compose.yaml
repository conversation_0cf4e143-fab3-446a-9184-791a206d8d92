services:
  zookeeper:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-zookeeper:1
    ports:
      - "2181:2181"

  kafka:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-kafka:2.6.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_CREATE_TOPICS: RefundCompleted:1:1

  mysql:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-mysql:8.0.25
    ports:
      - "3306:3306"
    depends_on:
      - kafka
    environment:
      MYSQL_DATABASE: fcs
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      MYSQL_USER: fcs
      MYSQL_PASSWORD: fcs
      MYSQL_ROOT_PASSWORD: fcs